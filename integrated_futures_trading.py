#!/usr/bin/env python3
"""
Integrated Futures Trading System
Works with IBKR MCP Server and Supabase MCP for comprehensive futures trading
"""
import asyncio
import sys
from datetime import datetime, time
from typing import Dict, List, Optional
import pytz
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

# Import IBKR MCP services
from ibkr_mcp_server.app.services.ibkr_service import ibkr_service
from ibkr_mcp_server.app.services.order_management_service import OrderManagementService

# Import Supabase integration
from supabase_config import SupabaseTradeLogger, RealtimeMonitor, supabase

class IntegratedFuturesTrader:
    """Advanced futures trader integrated with IBKR MCP and Supabase MCP"""
    
    def __init__(self):
        self.ibkr_service = ibkr_service
        self.supabase_logger = None
        self.realtime_monitor = None
        self.oms = None
        
        # Futures contracts configuration
        self.futures_config = {
            'MNQ': {
                'symbol': 'MNQ',
                'exchange': 'CME',
                'contract_month': '202503',  # March 2025
                'tick_size': 0.25,
                'multiplier': 2,  # $2 per point
                'margin_req': 1760,
                'max_position': 5,
                'scalp_target_ticks': 8,  # 2 points = $4
                'stop_loss_ticks': 12,    # 3 points = $6
                'trail_stop_ticks': 8
            },
            'MES': {
                'symbol': 'MES',
                'exchange': 'CME',
                'contract_month': '202503',  # March 2025
                'tick_size': 0.25,
                'multiplier': 5,  # $5 per point
                'margin_req': 1320,
                'max_position': 5,
                'scalp_target_ticks': 6,  # 1.5 points = $7.50
                'stop_loss_ticks': 8,     # 2 points = $10
                'trail_stop_ticks': 6
            }
        }
        
        # Trading state
        self.market_data = {}
        self.positions = {}
        self.active_orders = {}
        self.daily_stats = {
            'trades': 0,
            'pnl': 0.0,
            'wins': 0,
            'losses': 0
        }
        
    async def initialize(self):
        """Initialize the integrated futures trading system"""
        print("🚀 Initializing Integrated Futures Trading System...")
        
        # Initialize Supabase integration
        self.supabase_logger = SupabaseTradeLogger()
        await self.supabase_logger.initialize("FUTURES_TRADER")
        self.realtime_monitor = RealtimeMonitor(self.supabase_logger)
        print("✅ Supabase integration initialized")
        
        # Initialize IBKR connection
        if not self.ibkr_service.connected:
            await self.ibkr_service.connect(host="127.0.0.1", port=7497, client_id=3)
        print("✅ IBKR connection established")
        
        # Initialize Order Management Service
        self.oms = OrderManagementService(ibkr_svc=self.ibkr_service)
        print("✅ Order Management Service ready")
        
        # Set up futures contracts and market data
        await self.setup_futures_contracts()
        
        # Create trading strategy in Supabase
        await self.create_strategy_record()
        
        print("✅ Integrated Futures Trading System ready!")
        
    async def setup_futures_contracts(self):
        """Set up futures contracts and subscribe to market data"""
        print("📊 Setting up futures contracts...")
        
        for symbol, config in self.futures_config.items():
            try:
                # Create contract specification
                contract_spec = {
                    'symbol': symbol,
                    'secType': 'FUT',
                    'exchange': config['exchange'],
                    'currency': 'USD',
                    'lastTradeDateOrContractMonth': config['contract_month']
                }
                
                # Get market data through IBKR MCP
                market_data = await self.ibkr_service.get_market_data(
                    symbol=symbol,
                    exchange=config['exchange']
                )
                
                if market_data:
                    self.market_data[symbol] = market_data
                    print(f"✅ {symbol} market data subscribed")
                    
                    # Log market data request to Supabase
                    await self.supabase_logger.log_system_event({
                        "event_type": "MARKET_DATA_SUBSCRIPTION",
                        "message": f"Subscribed to {symbol} market data",
                        "details": {"symbol": symbol, "exchange": config['exchange']}
                    })
                else:
                    print(f"❌ Failed to get market data for {symbol}")
                    
            except Exception as e:
                print(f"❌ Error setting up {symbol}: {e}")
    
    async def create_strategy_record(self):
        """Create strategy record in Supabase"""
        try:
            strategy_data = {
                "name": "Integrated Micro Futures Scalping",
                "strategy_type": "FUTURES_SCALPING",
                "symbols": list(self.futures_config.keys()),
                "parameters": {
                    "max_position_per_symbol": 5,
                    "scalp_targets": {k: v['scalp_target_ticks'] for k, v in self.futures_config.items()},
                    "stop_losses": {k: v['stop_loss_ticks'] for k, v in self.futures_config.items()},
                    "trading_session": "RTH",  # Regular Trading Hours
                    "risk_management": "INTEGRATED_GUARDRAILS"
                },
                "active": True
            }
            
            result = supabase.table("strategies").insert(strategy_data).execute()
            self.strategy_id = result.data[0]['id']
            print(f"✅ Strategy created in Supabase: ID {self.strategy_id}")
            
        except Exception as e:
            print(f"⚠️ Could not create strategy record: {e}")
            self.strategy_id = None
    
    def check_market_hours(self) -> tuple[bool, str]:
        """Check if futures market is open"""
        now = datetime.now(pytz.timezone('America/Chicago'))
        current_time = now.time()
        weekday = now.weekday()
        
        # Market closed Saturday
        if weekday == 5:
            return False, "Market closed on Saturday"
            
        # Daily maintenance break
        if time(16, 0) <= current_time <= time(17, 0):
            return False, "Daily maintenance break (4-5 PM CT)"
            
        return True, "Market open for futures trading"
    
    async def generate_scalping_signal(self, symbol: str) -> Optional[str]:
        """Generate scalping signals using market data"""
        try:
            # Get current market data
            market_data = await self.ibkr_service.get_market_data(symbol=symbol)
            if not market_data:
                return None
            
            bid = market_data.get('bid', 0)
            ask = market_data.get('ask', 0)
            last = market_data.get('last', 0)
            volume = market_data.get('volume', 0)
            
            if not all([bid, ask, last]):
                return None
            
            # Simple scalping logic
            spread = ask - bid
            mid_price = (bid + ask) / 2
            
            # Volume-based signal
            if volume > 1000:  # High volume
                if last <= bid + spread * 0.3:  # Near bid
                    return "BUY"
                elif last >= ask - spread * 0.3:  # Near ask
                    return "SELL"
            
            return None
            
        except Exception as e:
            print(f"❌ Error generating signal for {symbol}: {e}")
            return None
    
    async def place_scalping_order(self, symbol: str, signal: str) -> Optional[Dict]:
        """Place a scalping order with integrated risk management"""
        try:
            config = self.futures_config[symbol]
            
            # Check position limits
            current_positions = await self.ibkr_service.get_positions()
            symbol_position = sum(
                abs(pos.get('position', 0)) 
                for pos in current_positions 
                if pos.get('symbol') == symbol
            )
            
            if symbol_position >= config['max_position']:
                print(f"⚠️ Position limit reached for {symbol}: {symbol_position}/{config['max_position']}")
                return None
            
            # Get current market data
            market_data = await self.ibkr_service.get_market_data(symbol=symbol)
            if not market_data:
                return None
            
            # Calculate order parameters
            quantity = 1  # Start with 1 contract
            
            if signal == "BUY":
                entry_price = market_data['ask']
                stop_price = entry_price - (config['stop_loss_ticks'] * config['tick_size'])
                target_price = entry_price + (config['scalp_target_ticks'] * config['tick_size'])
            else:  # SELL
                entry_price = market_data['bid']
                stop_price = entry_price + (config['stop_loss_ticks'] * config['tick_size'])
                target_price = entry_price - (config['scalp_target_ticks'] * config['tick_size'])
            
            # Place bracket order through IBKR MCP
            order_result = await self.oms.place_bracket_order(
                symbol=symbol,
                quantity=quantity,
                action=signal,
                entry_price=entry_price,
                stop_price=stop_price,
                target_price=target_price,
                exchange=config['exchange']
            )
            
            if order_result and order_result.get('status') == 'success':
                # Log to Supabase
                await self.supabase_logger.log_trade({
                    "symbol": symbol,
                    "action": signal,
                    "quantity": quantity,
                    "price": entry_price,
                    "order_type": "FUTURES_SCALP",
                    "status": "SUBMITTED",
                    "strategy_id": self.strategy_id,
                    "stop_price": stop_price,
                    "target_price": target_price
                })
                
                # Update daily stats
                self.daily_stats['trades'] += 1
                
                print(f"📤 {symbol} Scalp Order Placed:")
                print(f"   Signal: {signal}")
                print(f"   Entry: ${entry_price:.2f}")
                print(f"   Target: ${target_price:.2f} (+${config['scalp_target_ticks'] * config['tick_size'] * config['multiplier']:.2f})")
                print(f"   Stop: ${stop_price:.2f} (-${config['stop_loss_ticks'] * config['tick_size'] * config['multiplier']:.2f})")
                
                return order_result
            else:
                print(f"❌ Failed to place order for {symbol}")
                return None
                
        except Exception as e:
            print(f"❌ Error placing scalping order for {symbol}: {e}")
            return None
    
    async def monitor_and_trade(self):
        """Main trading loop with monitoring"""
        print("🔄 Starting futures trading monitor...")
        
        # Check market hours
        is_open, status = self.check_market_hours()
        if not is_open:
            print(f"❌ {status}")
            return
        
        print(f"✅ {status}")
        
        try:
            while True:
                # Monitor each futures symbol
                for symbol in self.futures_config.keys():
                    try:
                        # Generate signal
                        signal = await self.generate_scalping_signal(symbol)
                        
                        if signal:
                            # Place order
                            order_result = await self.place_scalping_order(symbol, signal)
                            
                            if order_result:
                                # Wait before next trade
                                await asyncio.sleep(30)
                        
                        # Update positions in Supabase
                        await self.update_positions()
                        
                    except Exception as e:
                        print(f"❌ Error processing {symbol}: {e}")
                
                # Display current status
                await self.display_status()
                
                # Wait before next iteration
                await asyncio.sleep(5)
                
        except KeyboardInterrupt:
            print("\n⏹️ Trading monitor stopped by user")
        except Exception as e:
            print(f"❌ Trading monitor error: {e}")
    
    async def update_positions(self):
        """Update position data in Supabase"""
        try:
            positions = await self.ibkr_service.get_positions()
            
            for position in positions:
                symbol = position.get('symbol')
                if symbol in self.futures_config:
                    await self.supabase_logger.update_position({
                        "symbol": symbol,
                        "side": "LONG" if position.get('position', 0) > 0 else "SHORT",
                        "quantity": abs(position.get('position', 0)),
                        "avg_cost": position.get('avgCost', 0),
                        "market_price": position.get('marketPrice', 0),
                        "unrealized_pnl": position.get('unrealizedPNL', 0),
                        "market_value": position.get('marketValue', 0),
                        "strategy_id": self.strategy_id
                    })
                    
        except Exception as e:
            print(f"⚠️ Error updating positions: {e}")
    
    async def display_status(self):
        """Display current trading status"""
        print("\033[H\033[J", end="")  # Clear screen
        print("=" * 70)
        print(f"🎯 INTEGRATED FUTURES TRADER - {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 70)
        
        # Display market data
        for symbol, config in self.futures_config.items():
            try:
                market_data = await self.ibkr_service.get_market_data(symbol=symbol)
                if market_data:
                    print(f"\n{symbol} (${config['multiplier']}/point):")
                    print(f"  Last: ${market_data.get('last', 0):.2f}")
                    print(f"  Bid/Ask: ${market_data.get('bid', 0):.2f} / ${market_data.get('ask', 0):.2f}")
                    print(f"  Volume: {market_data.get('volume', 0):,}")
            except:
                print(f"\n{symbol}: Market data unavailable")
        
        # Display daily stats
        print(f"\n📊 Daily Stats:")
        print(f"  Trades: {self.daily_stats['trades']}")
        print(f"  P&L: ${self.daily_stats['pnl']:.2f}")
        print(f"  Win Rate: {(self.daily_stats['wins'] / max(self.daily_stats['trades'], 1)) * 100:.1f}%")
        
        # Display positions
        try:
            positions = await self.ibkr_service.get_positions()
            futures_positions = [p for p in positions if p.get('symbol') in self.futures_config]
            
            if futures_positions:
                print(f"\n📈 Active Positions:")
                for pos in futures_positions:
                    print(f"  {pos['symbol']}: {pos.get('position', 0)} @ ${pos.get('avgCost', 0):.2f} | P&L: ${pos.get('unrealizedPNL', 0):.2f}")
            else:
                print(f"\n📈 No active positions")
                
        except Exception as e:
            print(f"\n📈 Positions: Error loading ({e})")

def main():
    """Main function"""
    print("🚀 INTEGRATED FUTURES TRADING SYSTEM")
    print("=" * 60)
    print("Features:")
    print("  ✅ IBKR MCP Integration")
    print("  ✅ Supabase MCP Integration") 
    print("  ✅ Real-time Trade Logging")
    print("  ✅ Advanced Risk Management")
    print("  ✅ Micro Futures Scalping (MNQ, MES)")
    print("=" * 60)
    
    async def run_trader():
        trader = IntegratedFuturesTrader()
        await trader.initialize()
        await trader.monitor_and_trade()
    
    # Run the trader
    try:
        asyncio.run(run_trader())
    except KeyboardInterrupt:
        print("\n👋 Futures trader stopped")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
