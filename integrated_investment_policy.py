#!/usr/bin/env python3
"""
Integrated Investment Policy Manager
Works with IBKR MCP, Supabase MCP, and IBKR connection
"""
import asyncio
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

# Import IBKR MCP services
from ibkr_mcp_server.app.services.ibkr_service import ibkr_service
from ibkr_mcp_server.app.services.order_management_service import OrderManagementService

# Import Supabase integration
from supabase_config import SupabaseTradeLogger, RealtimeMonitor, supabase

class IntegratedInvestmentPolicy:
    """Comprehensive investment policy manager integrated with IBKR and Supabase"""
    
    def __init__(self, account_id: str):
        self.account_id = account_id
        self.ibkr_service = ibkr_service
        self.supabase_logger = None
        self.realtime_monitor = None
        
        # Policy templates for different security types
        self.security_templates = {
            'STOCK': {
                'risk_score': 5,
                'liquidity_score': 8,
                'default_allocation': 0.6,
                'max_position_size': 0.05,
                'stop_loss_pct': 0.02,
                'take_profit_pct': 0.06
            },
            'OPTION': {
                'risk_score': 8,
                'liquidity_score': 6,
                'default_allocation': 0.1,
                'max_position_size': 0.02,
                'stop_loss_pct': 0.5,
                'take_profit_pct': 1.0
            },
            'FUTURE': {
                'risk_score': 9,
                'liquidity_score': 9,
                'default_allocation': 0.2,
                'max_position_size': 0.03,
                'stop_loss_pct': 0.015,
                'take_profit_pct': 0.045
            },
            'CRYPTO': {
                'risk_score': 10,
                'liquidity_score': 7,
                'default_allocation': 0.05,
                'max_position_size': 0.01,
                'stop_loss_pct': 0.05,
                'take_profit_pct': 0.15
            },
            'ETF': {
                'risk_score': 4,
                'liquidity_score': 9,
                'default_allocation': 0.3,
                'max_position_size': 0.1,
                'stop_loss_pct': 0.03,
                'take_profit_pct': 0.08
            }
        }
        
        # Investment objectives templates
        self.objective_templates = {
            'CONSERVATIVE': {
                'target_return': 8.0,
                'max_drawdown': 5.0,
                'risk_tolerance': 'LOW',
                'time_horizon': 'LONG',
                'sharpe_target': 1.0
            },
            'MODERATE': {
                'target_return': 15.0,
                'max_drawdown': 10.0,
                'risk_tolerance': 'MEDIUM',
                'time_horizon': 'MEDIUM',
                'sharpe_target': 1.2
            },
            'AGGRESSIVE': {
                'target_return': 25.0,
                'max_drawdown': 20.0,
                'risk_tolerance': 'HIGH',
                'time_horizon': 'SHORT',
                'sharpe_target': 1.5
            },
            'SPECULATION': {
                'target_return': 50.0,
                'max_drawdown': 35.0,
                'risk_tolerance': 'VERY_HIGH',
                'time_horizon': 'SHORT',
                'sharpe_target': 2.0
            }
        }
        
    async def initialize(self):
        """Initialize the integrated policy manager"""
        print("📋 Initializing Integrated Investment Policy Manager...")
        
        # Initialize Supabase integration
        self.supabase_logger = SupabaseTradeLogger()
        await self.supabase_logger.initialize(self.account_id)
        self.realtime_monitor = RealtimeMonitor(self.supabase_logger)
        print("✅ Supabase integration initialized")
        
        # Create policy tables if they don't exist
        await self.create_policy_tables()
        
        print("✅ Integrated Investment Policy Manager ready!")
        
    async def create_policy_tables(self):
        """Create investment policy tables in Supabase"""
        try:
            # Create investment objectives table
            objectives_table = """
            CREATE TABLE IF NOT EXISTS investment_objectives (
                id BIGSERIAL PRIMARY KEY,
                account_id TEXT NOT NULL,
                name TEXT NOT NULL,
                objective_type TEXT NOT NULL,
                target_return DECIMAL(5,2),
                return_period TEXT DEFAULT 'ANNUAL',
                risk_tolerance TEXT NOT NULL,
                allocated_capital DECIMAL(15,2),
                max_position_size DECIMAL(5,2),
                max_portfolio_allocation DECIMAL(5,2),
                time_horizon TEXT,
                max_drawdown DECIMAL(5,2),
                sharpe_target DECIMAL(5,2),
                constraints JSONB DEFAULT '{}',
                active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
            """
            
            # Create security policies table
            policies_table = """
            CREATE TABLE IF NOT EXISTS security_policies (
                id BIGSERIAL PRIMARY KEY,
                objective_id BIGINT REFERENCES investment_objectives(id),
                security_type TEXT NOT NULL,
                policy_name TEXT NOT NULL,
                max_single_position DECIMAL(15,2),
                max_total_exposure DECIMAL(15,2),
                concentration_limit DECIMAL(5,2),
                daily_loss_limit DECIMAL(15,2),
                stop_loss_rules JSONB DEFAULT '{}',
                take_profit_rules JSONB DEFAULT '{}',
                position_sizing_rules JSONB DEFAULT '{}',
                active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
            """
            
            # Create trading strategies table
            strategies_table = """
            CREATE TABLE IF NOT EXISTS trading_strategies (
                id BIGSERIAL PRIMARY KEY,
                objective_id BIGINT REFERENCES investment_objectives(id),
                security_type TEXT NOT NULL,
                strategy_name TEXT NOT NULL,
                strategy_category TEXT,
                parameters JSONB DEFAULT '{}',
                expected_win_rate DECIMAL(5,2),
                expected_profit_factor DECIMAL(5,2),
                market_conditions JSONB DEFAULT '{}',
                active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
            """
            
            # Create performance benchmarks table
            benchmarks_table = """
            CREATE TABLE IF NOT EXISTS performance_benchmarks (
                id BIGSERIAL PRIMARY KEY,
                objective_id BIGINT REFERENCES investment_objectives(id),
                benchmark_name TEXT NOT NULL,
                benchmark_symbol TEXT,
                comparison_metrics JSONB DEFAULT '{}',
                outperformance_target DECIMAL(5,2),
                tracking_error_limit DECIMAL(5,2),
                active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
            """
            
            # Note: In a real implementation, these would be executed via Supabase migrations
            # For now, we'll log that the tables should be created
            print("📊 Policy tables schema defined (would be created via Supabase migrations)")
            
        except Exception as e:
            print(f"⚠️ Error defining policy tables: {e}")
    
    async def create_investment_objective(self, objective_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new investment objective"""
        try:
            # Use template if specified
            template_name = objective_data.get('template')
            if template_name and template_name in self.objective_templates:
                template = self.objective_templates[template_name]
                # Merge template with custom data
                for key, value in template.items():
                    if key not in objective_data:
                        objective_data[key] = value
            
            # Create objective record
            objective = {
                "account_id": self.account_id,
                "name": objective_data.get("name", "Investment Objective"),
                "objective_type": objective_data.get("objective_type", "GROWTH"),
                "target_return": objective_data.get("target_return", 15.0),
                "return_period": objective_data.get("return_period", "ANNUAL"),
                "risk_tolerance": objective_data.get("risk_tolerance", "MEDIUM"),
                "allocated_capital": objective_data.get("allocated_capital", 50000.0),
                "max_position_size": objective_data.get("max_position_size", 0.05),
                "max_portfolio_allocation": objective_data.get("max_portfolio_allocation", 1.0),
                "time_horizon": objective_data.get("time_horizon", "MEDIUM"),
                "max_drawdown": objective_data.get("max_drawdown", 10.0),
                "sharpe_target": objective_data.get("sharpe_target", 1.2),
                "constraints": objective_data.get("constraints", {}),
                "active": True
            }
            
            # Insert into Supabase
            result = supabase.table("investment_objectives").insert(objective).execute()
            
            if result.data:
                objective_id = result.data[0]['id']
                
                # Create default security policies for this objective
                await self.create_default_security_policies(objective_id)
                
                # Log creation
                await self.supabase_logger.log_system_event({
                    "event_type": "INVESTMENT_OBJECTIVE_CREATED",
                    "message": f"Created investment objective: {objective['name']}",
                    "details": {"objective_id": objective_id, "objective": objective}
                })
                
                print(f"✅ Investment objective created: {objective['name']} (ID: {objective_id})")
                return {"status": "success", "objective_id": objective_id, "objective": result.data[0]}
            else:
                return {"status": "error", "message": "Failed to create objective"}
                
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def create_default_security_policies(self, objective_id: int):
        """Create default security policies for an objective"""
        try:
            for security_type, template in self.security_templates.items():
                policy = {
                    "objective_id": objective_id,
                    "security_type": security_type,
                    "policy_name": f"Default {security_type} Policy",
                    "max_single_position": template['max_position_size'] * 100000,  # Assuming $100k portfolio
                    "max_total_exposure": template['default_allocation'] * 100000,
                    "concentration_limit": template['max_position_size'],
                    "daily_loss_limit": 1000.0,  # Default $1000 daily loss limit
                    "stop_loss_rules": {
                        "type": "PERCENTAGE",
                        "value": template['stop_loss_pct'],
                        "trailing": True
                    },
                    "take_profit_rules": {
                        "type": "PERCENTAGE",
                        "value": template['take_profit_pct']
                    },
                    "position_sizing_rules": {
                        "method": "FIXED_FRACTIONAL",
                        "risk_per_trade": 0.02
                    },
                    "active": True
                }
                
                supabase.table("security_policies").insert(policy).execute()
                
            print(f"✅ Created default security policies for objective {objective_id}")
            
        except Exception as e:
            print(f"⚠️ Error creating default policies: {e}")
    
    async def get_active_objectives(self) -> List[Dict[str, Any]]:
        """Get all active investment objectives for the account"""
        try:
            result = supabase.table("investment_objectives").select("*").eq(
                "account_id", self.account_id
            ).eq("active", True).execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            print(f"❌ Error fetching objectives: {e}")
            return []
    
    async def get_security_policy(self, security_type: str, objective_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """Get security policy for a specific security type"""
        try:
            query = supabase.table("security_policies").select("*").eq("security_type", security_type).eq("active", True)
            
            if objective_id:
                query = query.eq("objective_id", objective_id)
            
            result = query.limit(1).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            print(f"❌ Error fetching security policy: {e}")
            return None
    
    async def validate_trade_against_policy(self, trade_params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a trade against investment policies"""
        try:
            symbol = trade_params.get('symbol')
            action = trade_params.get('action')
            quantity = trade_params.get('quantity')
            price = trade_params.get('price', 0)
            security_type = trade_params.get('security_type', 'STOCK')
            
            # Get applicable policy
            policy = await self.get_security_policy(security_type)
            if not policy:
                return {"allowed": True, "warnings": ["No policy found for security type"]}
            
            validation = {
                "allowed": True,
                "reasons": [],
                "warnings": [],
                "policy_applied": policy['policy_name']
            }
            
            # Check position size limits
            position_value = quantity * price
            max_position = policy.get('max_single_position', float('inf'))
            
            if position_value > max_position:
                validation['allowed'] = False
                validation['reasons'].append(f"Position size ${position_value:.2f} exceeds limit ${max_position:.2f}")
            
            # Check concentration limits
            concentration_limit = policy.get('concentration_limit', 1.0)
            # This would require getting current portfolio value from IBKR
            # For now, we'll add a warning
            if position_value > 10000:  # Placeholder check
                validation['warnings'].append(f"Large position size: ${position_value:.2f}")
            
            # Log validation
            await self.supabase_logger.log_system_event({
                "event_type": "TRADE_POLICY_VALIDATION",
                "message": f"Trade validation for {symbol}: {'ALLOWED' if validation['allowed'] else 'BLOCKED'}",
                "details": {"trade_params": trade_params, "validation": validation}
            })
            
            return validation
            
        except Exception as e:
            return {"allowed": False, "reasons": [f"Validation error: {str(e)}"]}
    
    async def get_policy_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive policy dashboard data"""
        try:
            # Get active objectives
            objectives = await self.get_active_objectives()
            
            # Get policy compliance metrics
            dashboard = {
                "account_id": self.account_id,
                "active_objectives": len(objectives),
                "objectives": objectives,
                "policy_status": "ACTIVE",
                "last_updated": datetime.now().isoformat()
            }
            
            # Add performance metrics if available
            if objectives:
                # This would calculate actual performance vs targets
                dashboard["performance_summary"] = {
                    "objectives_on_track": len([obj for obj in objectives if obj.get('target_return', 0) > 0]),
                    "total_allocated_capital": sum(obj.get('allocated_capital', 0) for obj in objectives),
                    "risk_utilization": "MODERATE"  # Placeholder
                }
            
            return {"status": "success", "dashboard": dashboard}
            
        except Exception as e:
            return {"status": "error", "message": str(e)}

async def main():
    """Test the integrated investment policy manager"""
    print("🚀 INTEGRATED INVESTMENT POLICY MANAGER")
    print("=" * 60)
    
    policy_manager = IntegratedInvestmentPolicy("TEST_ACCOUNT")
    await policy_manager.initialize()
    
    # Test creating an investment objective
    print("\n🧪 Testing investment objective creation...")
    objective_result = await policy_manager.create_investment_objective({
        "name": "Moderate Growth Portfolio",
        "template": "MODERATE",
        "allocated_capital": 100000.0,
        "constraints": {
            "allowed_sectors": ["TECH", "HEALTHCARE", "FINANCE"],
            "max_single_stock": 0.05
        }
    })
    
    print(f"Result: {objective_result}")
    
    # Test trade validation
    print("\n🧪 Testing trade validation...")
    trade_validation = await policy_manager.validate_trade_against_policy({
        "symbol": "AAPL",
        "action": "BUY",
        "quantity": 100,
        "price": 180.0,
        "security_type": "STOCK"
    })
    
    print(f"Trade validation: {trade_validation}")
    
    # Get dashboard
    print("\n📊 Getting policy dashboard...")
    dashboard = await policy_manager.get_policy_dashboard()
    print(f"Dashboard: {dashboard}")

if __name__ == "__main__":
    asyncio.run(main())
