#!/usr/bin/env python3
"""
Setup Supabase Database Tables
Creates all required tables for IBKR-Supabase integration
"""
import sys
sys.path.append("/Users/<USER>/IBKR/b-team")

from supabase_config import supabase

def create_tables():
    """Create all required Supabase tables"""
    print("🗄️ Setting up Supabase database tables...")
    
    tables_created = 0
    
    # 1. System Events Table
    try:
        result = supabase.rpc('create_table_if_not_exists', {
            'table_name': 'system_events',
            'table_sql': '''
                CREATE TABLE IF NOT EXISTS system_events (
                    id BIGSERIAL PRIMARY KEY,
                    event_type TEXT NOT NULL,
                    message TEXT NOT NULL,
                    details JSONB DEFAULT '{}',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            '''
        }).execute()
        print("  ✅ system_events table ready")
        tables_created += 1
    except Exception as e:
        # Try direct SQL execution
        try:
            supabase.postgrest.schema('public').rpc('exec_sql', {
                'sql': '''
                    CREATE TABLE IF NOT EXISTS system_events (
                        id BIGSERIAL PRIMARY KEY,
                        event_type TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details JSONB DEFAULT '{}',
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    )
                '''
            }).execute()
            print("  ✅ system_events table created")
            tables_created += 1
        except Exception as e2:
            print(f"  ⚠️ system_events table creation skipped: {e2}")
    
    # 2. Trades Table
    try:
        supabase.postgrest.schema('public').rpc('exec_sql', {
            'sql': '''
                CREATE TABLE IF NOT EXISTS trades (
                    id BIGSERIAL PRIMARY KEY,
                    account_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    price DECIMAL(10,4),
                    order_type TEXT DEFAULT 'MARKET',
                    status TEXT DEFAULT 'SUBMITTED',
                    commission DECIMAL(10,4) DEFAULT 0,
                    ib_order_id TEXT,
                    strategy_id INTEGER,
                    execution_time TIMESTAMP WITH TIME ZONE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            '''
        }).execute()
        print("  ✅ trades table ready")
        tables_created += 1
    except Exception as e:
        print(f"  ⚠️ trades table creation skipped: {e}")
    
    # 3. Positions Table
    try:
        supabase.postgrest.schema('public').rpc('exec_sql', {
            'sql': '''
                CREATE TABLE IF NOT EXISTS positions (
                    id BIGSERIAL PRIMARY KEY,
                    account_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    entry_price DECIMAL(10,4),
                    current_price DECIMAL(10,4),
                    pnl DECIMAL(12,4) DEFAULT 0,
                    pnl_percentage DECIMAL(8,4) DEFAULT 0,
                    current_value DECIMAL(12,4) DEFAULT 0,
                    status TEXT DEFAULT 'OPEN',
                    strategy_id INTEGER,
                    exit_price DECIMAL(10,4),
                    exit_time TIMESTAMP WITH TIME ZONE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            '''
        }).execute()
        print("  ✅ positions table ready")
        tables_created += 1
    except Exception as e:
        print(f"  ⚠️ positions table creation skipped: {e}")
    
    # 4. Performance Table
    try:
        supabase.postgrest.schema('public').rpc('exec_sql', {
            'sql': '''
                CREATE TABLE IF NOT EXISTS performance (
                    id BIGSERIAL PRIMARY KEY,
                    account_id TEXT NOT NULL,
                    total_value DECIMAL(15,4) DEFAULT 0,
                    cash_balance DECIMAL(15,4) DEFAULT 0,
                    positions_value DECIMAL(15,4) DEFAULT 0,
                    daily_pnl DECIMAL(12,4) DEFAULT 0,
                    total_pnl DECIMAL(12,4) DEFAULT 0,
                    win_rate DECIMAL(5,4) DEFAULT 0,
                    sharpe_ratio DECIMAL(8,4) DEFAULT 0,
                    max_drawdown DECIMAL(5,4) DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            '''
        }).execute()
        print("  ✅ performance table ready")
        tables_created += 1
    except Exception as e:
        print(f"  ⚠️ performance table creation skipped: {e}")
    
    # 5. Strategies Table
    try:
        supabase.postgrest.schema('public').rpc('exec_sql', {
            'sql': '''
                CREATE TABLE IF NOT EXISTS strategies (
                    id BIGSERIAL PRIMARY KEY,
                    name TEXT NOT NULL,
                    strategy_type TEXT NOT NULL,
                    symbols TEXT[] DEFAULT '{}',
                    parameters JSONB DEFAULT '{}',
                    active BOOLEAN DEFAULT true,
                    total_pnl DECIMAL(12,4) DEFAULT 0,
                    win_rate DECIMAL(5,4) DEFAULT 0,
                    sharpe_ratio DECIMAL(8,4) DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            '''
        }).execute()
        print("  ✅ strategies table ready")
        tables_created += 1
    except Exception as e:
        print(f"  ⚠️ strategies table creation skipped: {e}")
    
    # 6. Alerts Table
    try:
        supabase.postgrest.schema('public').rpc('exec_sql', {
            'sql': '''
                CREATE TABLE IF NOT EXISTS alerts (
                    id BIGSERIAL PRIMARY KEY,
                    name TEXT NOT NULL,
                    condition_type TEXT NOT NULL,
                    symbol TEXT,
                    threshold_value DECIMAL(12,4),
                    severity TEXT DEFAULT 'INFO',
                    active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            '''
        }).execute()
        print("  ✅ alerts table ready")
        tables_created += 1
    except Exception as e:
        print(f"  ⚠️ alerts table creation skipped: {e}")
    
    return tables_created

def insert_sample_data():
    """Insert sample data for testing"""
    print("\n📊 Inserting sample data...")
    
    try:
        # Insert sample strategy
        strategy_data = {
            "name": "Demo Strategy",
            "strategy_type": "MOMENTUM",
            "symbols": ["AAPL", "MSFT", "GOOGL"],
            "parameters": {"risk_level": "medium", "max_position_size": 10000},
            "active": True
        }
        
        result = supabase.table("strategies").insert(strategy_data).execute()
        print("  ✅ Sample strategy inserted")
        
        # Insert sample system event
        event_data = {
            "event_type": "SYSTEM_INIT",
            "message": "IBKR-Supabase integration initialized",
            "details": {"version": "2.1.0", "features": ["trade_logging", "real_time_monitoring"]}
        }
        
        result = supabase.table("system_events").insert(event_data).execute()
        print("  ✅ Sample system event inserted")
        
        # Insert sample alert
        alert_data = {
            "name": "Daily Loss Limit",
            "condition_type": "DAILY_LOSS",
            "threshold_value": 1000.00,
            "severity": "HIGH",
            "active": True
        }
        
        result = supabase.table("alerts").insert(alert_data).execute()
        print("  ✅ Sample alert inserted")
        
        return True
        
    except Exception as e:
        print(f"  ⚠️ Sample data insertion failed: {e}")
        return False

def test_tables():
    """Test that all tables are working"""
    print("\n🧪 Testing database tables...")
    
    tables_to_test = [
        "system_events",
        "trades", 
        "positions",
        "performance",
        "strategies",
        "alerts"
    ]
    
    working_tables = 0
    
    for table in tables_to_test:
        try:
            result = supabase.table(table).select("*").limit(1).execute()
            print(f"  ✅ {table} table working")
            working_tables += 1
        except Exception as e:
            print(f"  ❌ {table} table failed: {e}")
    
    return working_tables == len(tables_to_test)

def main():
    """Main setup function"""
    print("🚀 SUPABASE DATABASE SETUP")
    print("=" * 50)
    
    # Test connection first
    try:
        result = supabase.table("information_schema.tables").select("table_name").limit(1).execute()
        print("✅ Supabase connection successful")
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        print("Please check your Supabase credentials and try again.")
        return
    
    # Create tables
    tables_created = create_tables()
    print(f"\n📊 Database setup complete: {tables_created} tables ready")
    
    # Insert sample data
    if insert_sample_data():
        print("✅ Sample data inserted successfully")
    
    # Test tables
    if test_tables():
        print("\n🎉 ALL TABLES WORKING CORRECTLY!")
        print("✅ Supabase database is ready for IBKR integration")
    else:
        print("\n⚠️ Some tables may have issues")
        print("🔧 Please check the errors above")
    
    print("\n📋 Next steps:")
    print("1. Run the integration verification again")
    print("2. Test the integrated MCP server")
    print("3. Restart Claude Desktop to use the new setup")

if __name__ == "__main__":
    main()
