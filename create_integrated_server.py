#!/usr/bin/env python3
"""
Create Integrated IBKR-Supabase MCP Server
This script combines the full 108-tool IBKR server with Supabase integration
"""
import shutil
from pathlib import Path

def create_integrated_server():
    """Create the integrated server by combining both servers"""
    
    print("🔧 Creating Integrated IBKR-Supabase MCP Server...")
    
    # Read the full MCP server
    with open("b-team/full_mcp_server.py", "r") as f:
        full_server_content = f.read()

    # Read the enhanced server template
    with open("enhanced_ibkr_supabase_mcp_server.py", "r") as f:
        enhanced_content = f.read()
    
    # Extract the tools section from full_mcp_server.py
    # Find where the tools start (after the basic connection tools)
    tools_start = full_server_content.find("# 3. MARKET DATA TOOLS")
    tools_end = full_server_content.find("def main():")
    
    if tools_start == -1 or tools_end == -1:
        print("❌ Could not find tools section in full_mcp_server.py")
        return False
    
    # Extract all the tools
    all_tools = full_server_content[tools_start:tools_end]
    
    # Find where to insert in enhanced server
    insert_point = enhanced_content.find("# Include all the remaining tools")
    if insert_point == -1:
        print("❌ Could not find insertion point in enhanced server")
        return False
    
    # Find the end of the comment block
    comment_end = enhanced_content.find("def main():", insert_point)
    
    # Replace the comment with actual tools
    integrated_content = (
        enhanced_content[:insert_point] + 
        all_tools + 
        enhanced_content[comment_end:]
    )
    
    # Add Supabase logging to key tools by modifying their implementations
    integrated_content = add_supabase_logging_to_tools(integrated_content)
    
    # Write the integrated server
    with open("b-team/integrated_ibkr_supabase_mcp_server.py", "w") as f:
        f.write(integrated_content)

    print("✅ Integrated server created: b-team/integrated_ibkr_supabase_mcp_server.py")
    return True

def add_supabase_logging_to_tools(content):
    """Add Supabase logging to key trading tools"""
    
    # Add logging to market data tools
    content = content.replace(
        'return {"status": "success", "market_data": data}',
        '''# Log market data request to Supabase
        if supabase_logger:
            await supabase_logger.log_system_event({
                "event_type": "MARKET_DATA_REQUEST",
                "message": f"Market data requested for {symbol}",
                "details": {"symbol": symbol, "exchange": exchange}
            })
        
        return {"status": "success", "market_data": data}'''
    )
    
    # Add logging to order placement tools
    content = content.replace(
        'return {"status": "success", "order": order}',
        '''# Log order to Supabase
        if supabase_logger and order:
            await supabase_logger.log_trade({
                "symbol": symbol,
                "action": action,
                "quantity": quantity,
                "order_type": "MARKET",
                "status": "SUBMITTED",
                "order_id": order.get("orderId")
            })
        
        return {"status": "success", "order": order}'''
    )
    
    return content

def update_claude_config():
    """Update Claude Desktop configuration to use the integrated server"""
    
    config_content = '''
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/IBKR/b-team"
      ]
    },
    "brave-search": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-brave-search"
      ],
      "env": {
        "BRAVE_API_KEY": "BSAjQpxPzjay5DrCA-uy3QkB27tTouN"
      },
      "disabled": false,
      "autoApprove": []
    },
    "dalle-mcp": {
      "command": "node",
      "args": [
        "/Users/<USER>/Documents/Cline/MCP/dalle-mcp-server/build/index.js"
      ],
      "env": {
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "SAVE_DIR": "/Users/<USER>/adverp/docs/dall-e"
      },
      "disabled": false,
      "autoApprove": [
        "/Users/<USER>/adverp/docs/dall-e"
      ]
    },
    "fundraise-server": {
      "command": "node",
      "args": [
        "/Users/<USER>/Documents/Cline/MCP/fundraise-server/build/index.js"
      ]
    },
    "github.com/supabase-community/supabase-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "********************************************"
      ],
      "disabled": false,
      "autoApprove": []
    },
    "github-mcp-server": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"
      },
      "disabled": false,
      "autoApprove": []
    },
    "ibkr-trading": {
      "command": "/Users/<USER>/IBKR/.venv/bin/python",
      "args": [
        "/Users/<USER>/IBKR/b-team/integrated_ibkr_supabase_mcp_server.py"
      ],
      "env": {
        "IBKR_AUTO_CONNECT": "false",
        "IBKR_DEFAULT_EXCHANGE": "SMART",
        "IBKR_DEFAULT_CURRENCY": "USD",
        "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"
      },
      "disabled": false
    }
  }
}'''
    
    with open("b-team/claude_desktop_config_integrated.json", "w") as f:
        f.write(config_content)
    
    print("✅ Updated Claude Desktop configuration created")

def test_integration():
    """Test the integrated server"""
    print("\n🧪 Testing integrated server...")
    
    # Test imports
    try:
        import subprocess
        result = subprocess.run([
            "/Users/<USER>/IBKR/.venv/bin/python", 
            "-c", 
            """
import sys
sys.path.append('/Users/<USER>/IBKR/b-team')

try:
    from supabase_config import SupabaseTradeLogger
    print('✅ Supabase integration available')
except ImportError as e:
    print(f'❌ Supabase integration failed: {e}')

try:
    from ibkr_mcp_server.app.services.ibkr_service import ibkr_service
    print('✅ IBKR service available')
except ImportError as e:
    print(f'❌ IBKR service failed: {e}')

try:
    from mcp.server.fastmcp import FastMCP
    print('✅ MCP server available')
except ImportError as e:
    print(f'❌ MCP server failed: {e}')
"""
        ], capture_output=True, text=True, timeout=10)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

def main():
    """Main function"""
    print("🚀 IBKR-Supabase Integration Setup")
    print("=" * 50)
    
    # Create integrated server
    if create_integrated_server():
        print("✅ Integrated server created successfully")
    else:
        print("❌ Failed to create integrated server")
        return
    
    # Update Claude config
    update_claude_config()
    
    # Test integration
    test_integration()
    
    print("\n🎉 Integration setup complete!")
    print("\n📋 Next steps:")
    print("1. Install the new configuration:")
    print("   cp claude_desktop_config_integrated.json '/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json'")
    print("2. Restart Claude Desktop")
    print("3. Test with: test_connection()")
    print("4. Test Supabase integration with: get_enhanced_server_info()")

if __name__ == "__main__":
    main()
