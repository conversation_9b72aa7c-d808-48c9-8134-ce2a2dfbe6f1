#!/usr/bin/env python3
"""
Setup Supabase Integration for IBKR MCP Server
This script adds Supabase integration to the existing full MCP server
"""
import os
import shutil
from pathlib import Path

def setup_supabase_integration():
    """Add Supabase integration to the existing IBKR MCP server"""
    
    print("🔧 Setting up Supabase Integration for IBKR MCP Server...")
    
    # Check if files exist
    full_server_path = "full_mcp_server.py"
    supabase_config_path = "supabase_config.py"
    
    if not os.path.exists(full_server_path):
        print(f"❌ {full_server_path} not found")
        return False
    
    if not os.path.exists(supabase_config_path):
        print(f"❌ {supabase_config_path} not found")
        return False
    
    # Read the full server
    with open(full_server_path, "r") as f:
        server_content = f.read()
    
    # Add Supabase imports at the top
    supabase_imports = """
# Import Supabase integration
try:
    from supabase_config import Su<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RealtimeMonitor, supabase
    SUPABASE_AVAILABLE = True
    print("✅ Supabase integration available", file=sys.stderr)
except ImportError as e:
    SUPABASE_AVAILABLE = False
    print(f"⚠️ Supabase integration not available: {e}", file=sys.stderr)
    # Create dummy classes to prevent errors
    class SupabaseTradeLogger:
        async def initialize(self, account_id): pass
        async def log_trade(self, trade_data): pass
        async def log_system_event(self, event_data): pass
        async def log_performance(self, perf_data): pass
    class RealtimeMonitor:
        def __init__(self, logger): pass
        def stop_monitoring(self): pass
    supabase = None
"""
    
    # Find where to insert imports (after existing imports)
    import_insertion_point = server_content.find("# Global service instances")
    if import_insertion_point == -1:
        import_insertion_point = server_content.find("# Server lifespan management")
    
    if import_insertion_point == -1:
        print("❌ Could not find insertion point for imports")
        return False
    
    # Insert Supabase imports
    server_content = (
        server_content[:import_insertion_point] + 
        supabase_imports + 
        "\n" + 
        server_content[import_insertion_point:]
    )
    
    # Add Supabase globals
    globals_addition = """
# Supabase integration globals
supabase_logger = None
realtime_monitor = None
"""
    
    globals_insertion_point = server_content.find("oms_instance = None")
    if globals_insertion_point != -1:
        # Insert after oms_instance line
        end_of_line = server_content.find("\n", globals_insertion_point) + 1
        server_content = (
            server_content[:end_of_line] + 
            globals_addition + 
            server_content[end_of_line:]
        )
    
    # Modify the lifespan function to include Supabase initialization
    lifespan_modification = """
        # Initialize Supabase integration if available
        if SUPABASE_AVAILABLE:
            try:
                global supabase_logger, realtime_monitor
                supabase_logger = SupabaseTradeLogger()
                await supabase_logger.initialize("DEMO_ACCOUNT")
                realtime_monitor = RealtimeMonitor(supabase_logger)
                print("✅ Supabase integration initialized", file=sys.stderr)
            except Exception as e:
                print(f"⚠️ Supabase initialization failed: {e}", file=sys.stderr)
        
"""
    
    # Find the lifespan function and add Supabase initialization
    lifespan_start = server_content.find("print(\"✅ Order Management Service initialized\", file=sys.stderr)")
    if lifespan_start != -1:
        end_of_line = server_content.find("\n", lifespan_start) + 1
        server_content = (
            server_content[:end_of_line] + 
            lifespan_modification + 
            server_content[end_of_line:]
        )
    
    # Add Supabase logging to key functions
    server_content = add_supabase_logging_to_functions(server_content)
    
    # Write the enhanced server
    enhanced_server_path = "full_mcp_server_with_supabase.py"
    with open(enhanced_server_path, "w") as f:
        f.write(server_content)
    
    print(f"✅ Enhanced server created: {enhanced_server_path}")
    return True

def add_supabase_logging_to_functions(content):
    """Add Supabase logging to key functions"""
    
    # Add logging to connection function
    content = content.replace(
        'return {"status": "success", "message": "Connected to TWS", "details": result}',
        '''# Log connection to Supabase
        if SUPABASE_AVAILABLE and supabase_logger:
            try:
                await supabase_logger.log_system_event({
                    "event_type": "CONNECTION",
                    "message": f"Connected to TWS at {host}:{port}",
                    "details": {"host": host, "port": port, "client_id": client_id}
                })
            except: pass
        
        return {"status": "success", "message": "Connected to TWS with Supabase logging", "details": result}'''
    )
    
    # Add logging to order placement
    content = content.replace(
        'return {"status": "success", "order": order}',
        '''# Log order to Supabase
        if SUPABASE_AVAILABLE and supabase_logger:
            try:
                await supabase_logger.log_trade({
                    "symbol": symbol,
                    "action": action,
                    "quantity": quantity,
                    "order_type": "MARKET",
                    "status": "SUBMITTED",
                    "order_id": order.get("orderId") if order else None
                })
            except: pass
        
        return {"status": "success", "order": order}'''
    )
    
    return content

def create_supabase_tools():
    """Create additional Supabase-specific tools"""
    
    supabase_tools = '''
# ============================================================================
# SUPABASE INTEGRATION TOOLS (Additional 10 tools)
# ============================================================================

@mcp.tool()
async def get_supabase_status() -> Dict:
    """Get Supabase integration status"""
    return {
        "status": "success",
        "supabase_available": SUPABASE_AVAILABLE,
        "logger_initialized": supabase_logger is not None,
        "monitor_active": realtime_monitor is not None
    }

@mcp.tool()
async def log_custom_event(event_type: str, message: str, details: Dict = None) -> Dict:
    """Log a custom event to Supabase"""
    try:
        if not SUPABASE_AVAILABLE or not supabase_logger:
            return {"status": "error", "message": "Supabase not available"}
        
        await supabase_logger.log_system_event({
            "event_type": event_type,
            "message": message,
            "details": details or {}
        })
        
        return {"status": "success", "message": "Event logged to Supabase"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_trading_performance_supabase(days: int = 30) -> Dict:
    """Get trading performance from Supabase"""
    try:
        if not SUPABASE_AVAILABLE or not supabase:
            return {"status": "error", "message": "Supabase not available"}
        
        from datetime import datetime, timedelta
        start_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        # Get performance data
        result = supabase.table("performance").select("*").gte("created_at", start_date).execute()
        
        return {"status": "success", "performance_data": result.data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_trade_history_supabase(days: int = 7) -> Dict:
    """Get trade history from Supabase"""
    try:
        if not SUPABASE_AVAILABLE or not supabase:
            return {"status": "error", "message": "Supabase not available"}
        
        from datetime import datetime, timedelta
        start_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        result = supabase.table("trades").select("*").gte("created_at", start_date).order("created_at", desc=True).execute()
        
        return {"status": "success", "trades": result.data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def sync_with_supabase() -> Dict:
    """Sync current IBKR data with Supabase"""
    try:
        if not SUPABASE_AVAILABLE or not supabase_logger:
            return {"status": "error", "message": "Supabase not available"}
        
        # Get current positions and sync
        if ibkr_service.connected:
            positions = await ibkr_service.get_positions()
            
            for position in positions:
                await supabase_logger.update_position({
                    "symbol": position.get("symbol"),
                    "quantity": position.get("position", 0),
                    "avg_cost": position.get("avgCost", 0),
                    "market_value": position.get("marketValue", 0)
                })
            
            return {"status": "success", "synced_positions": len(positions)}
        else:
            return {"status": "error", "message": "IBKR not connected"}
    except Exception as e:
        return {"status": "error", "message": str(e)}
'''
    
    return supabase_tools

def update_claude_config():
    """Update Claude Desktop configuration"""
    
    config_content = '''{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/IBKR/b-team"
      ]
    },
    "brave-search": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-brave-search"
      ],
      "env": {
        "BRAVE_API_KEY": "BSAjQpxPzjay5DrCA-uy3QkB27tTouN"
      },
      "disabled": false,
      "autoApprove": []
    },
    "github.com/supabase-community/supabase-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "********************************************"
      ],
      "disabled": false,
      "autoApprove": []
    },
    "ibkr-trading": {
      "command": "/Users/<USER>/IBKR/.venv/bin/python",
      "args": [
        "/Users/<USER>/IBKR/b-team/full_mcp_server_with_supabase.py"
      ],
      "env": {
        "IBKR_AUTO_CONNECT": "false",
        "IBKR_DEFAULT_EXCHANGE": "SMART",
        "IBKR_DEFAULT_CURRENCY": "USD",
        "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"
      },
      "disabled": false
    }
  }
}'''
    
    with open("claude_desktop_config_supabase_integrated.json", "w") as f:
        f.write(config_content)
    
    print("✅ Updated Claude Desktop configuration created")

def main():
    """Main function"""
    print("🚀 IBKR-Supabase Integration Setup")
    print("=" * 50)
    
    # Setup integration
    if setup_supabase_integration():
        print("✅ Supabase integration added successfully")
        
        # Add Supabase tools to the enhanced server
        with open("full_mcp_server_with_supabase.py", "a") as f:
            f.write(create_supabase_tools())
        
        print("✅ Additional Supabase tools added")
        
        # Update Claude config
        update_claude_config()
        
        print("\n🎉 Integration setup complete!")
        print("\n📋 Next steps:")
        print("1. Install the new configuration:")
        print("   cp claude_desktop_config_supabase_integrated.json '/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json'")
        print("2. Restart Claude Desktop")
        print("3. Test with: test_connection()")
        print("4. Test Supabase integration with: get_supabase_status()")
        
    else:
        print("❌ Failed to setup integration")

if __name__ == "__main__":
    main()
