Collecting mcp==1.8.0
  Using cached mcp-1.8.0-py3-none-any.whl.metadata (25 kB)
Collecting fastapi
  Using cached fastapi-0.115.12-py3-none-any.whl.metadata (27 kB)
Requirement already satisfied: uvicorn in ./.venv/lib/python3.12/site-packages (0.34.3)
Collecting ib_async
  Using cached ib_async-1.0.3-py3-none-any.whl.metadata (8.1 kB)
Collecting ibapi
  Using cached ibapi-9.81.1.post1-py3-none-any.whl
Collecting pandas
  Using cached pandas-2.2.3-cp312-cp312-macosx_11_0_arm64.whl.metadata (89 kB)
Requirement already satisfied: matplotlib in ./.venv/lib/python3.12/site-packages (3.10.3)
Collecting nest_asyncio
  Using cached nest_asyncio-1.6.0-py3-none-any.whl.metadata (2.8 kB)
Requirement already satisfied: anyio>=4.5 in ./.venv/lib/python3.12/site-packages (from mcp==1.8.0) (4.9.0)
Requirement already satisfied: httpx-sse>=0.4 in ./.venv/lib/python3.12/site-packages (from mcp==1.8.0) (0.4.0)
Requirement already satisfied: httpx>=0.27 in ./.venv/lib/python3.12/site-packages (from mcp==1.8.0) (0.28.1)
Requirement already satisfied: pydantic-settings>=2.5.2 in ./.venv/lib/python3.12/site-packages (from mcp==1.8.0) (2.9.1)
Requirement already satisfied: pydantic<3.0.0,>=2.7.2 in ./.venv/lib/python3.12/site-packages (from mcp==1.8.0) (2.11.5)
Requirement already satisfied: python-multipart>=0.0.9 in ./.venv/lib/python3.12/site-packages (from mcp==1.8.0) (0.0.20)
Requirement already satisfied: sse-starlette>=1.6.1 in ./.venv/lib/python3.12/site-packages (from mcp==1.8.0) (2.3.6)
Requirement already satisfied: starlette>=0.27 in ./.venv/lib/python3.12/site-packages (from mcp==1.8.0) (0.47.0)
Collecting starlette>=0.27 (from mcp==1.8.0)
  Using cached starlette-0.46.2-py3-none-any.whl.metadata (6.2 kB)
Requirement already satisfied: typing-extensions>=4.8.0 in ./.venv/lib/python3.12/site-packages (from fastapi) (4.14.0)
Requirement already satisfied: click>=7.0 in ./.venv/lib/python3.12/site-packages (from uvicorn) (8.2.1)
Requirement already satisfied: h11>=0.8 in ./.venv/lib/python3.12/site-packages (from uvicorn) (0.16.0)
Collecting eventkit (from ib_async)
  Using cached eventkit-1.0.3-py3-none-any.whl.metadata (5.4 kB)
Requirement already satisfied: numpy>=1.26.0 in ./.venv/lib/python3.12/site-packages (from pandas) (2.2.6)
Requirement already satisfied: python-dateutil>=2.8.2 in ./.venv/lib/python3.12/site-packages (from pandas) (2.9.0.post0)
Collecting pytz>=2020.1 (from pandas)
  Using cached pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas)
  Using cached tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Requirement already satisfied: contourpy>=1.0.1 in ./.venv/lib/python3.12/site-packages (from matplotlib) (1.3.2)
Requirement already satisfied: cycler>=0.10 in ./.venv/lib/python3.12/site-packages (from matplotlib) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in ./.venv/lib/python3.12/site-packages (from matplotlib) (4.58.1)
Requirement already satisfied: kiwisolver>=1.3.1 in ./.venv/lib/python3.12/site-packages (from matplotlib) (1.4.8)
Requirement already satisfied: packaging>=20.0 in ./.venv/lib/python3.12/site-packages (from matplotlib) (25.0)
Requirement already satisfied: pillow>=8 in ./.venv/lib/python3.12/site-packages (from matplotlib) (11.2.1)
Requirement already satisfied: pyparsing>=2.3.1 in ./.venv/lib/python3.12/site-packages (from matplotlib) (3.2.3)
Requirement already satisfied: idna>=2.8 in ./.venv/lib/python3.12/site-packages (from anyio>=4.5->mcp==1.8.0) (3.10)
Requirement already satisfied: sniffio>=1.1 in ./.venv/lib/python3.12/site-packages (from anyio>=4.5->mcp==1.8.0) (1.3.1)
Requirement already satisfied: certifi in ./.venv/lib/python3.12/site-packages (from httpx>=0.27->mcp==1.8.0) (2025.4.26)
Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.12/site-packages (from httpx>=0.27->mcp==1.8.0) (1.0.9)
Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.12/site-packages (from pydantic<3.0.0,>=2.7.2->mcp==1.8.0) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in ./.venv/lib/python3.12/site-packages (from pydantic<3.0.0,>=2.7.2->mcp==1.8.0) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in ./.venv/lib/python3.12/site-packages (from pydantic<3.0.0,>=2.7.2->mcp==1.8.0) (0.4.1)
Requirement already satisfied: python-dotenv>=0.21.0 in ./.venv/lib/python3.12/site-packages (from pydantic-settings>=2.5.2->mcp==1.8.0) (1.1.0)
Requirement already satisfied: six>=1.5 in ./.venv/lib/python3.12/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)
Using cached mcp-1.8.0-py3-none-any.whl (119 kB)
Using cached fastapi-0.115.12-py3-none-any.whl (95 kB)
Using cached ib_async-1.0.3-py3-none-any.whl (77 kB)
Using cached pandas-2.2.3-cp312-cp312-macosx_11_0_arm64.whl (11.4 MB)
Using cached nest_asyncio-1.6.0-py3-none-any.whl (5.2 kB)
Using cached pytz-2025.2-py2.py3-none-any.whl (509 kB)
Using cached starlette-0.46.2-py3-none-any.whl (72 kB)
Using cached tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Using cached eventkit-1.0.3-py3-none-any.whl (31 kB)
Installing collected packages: pytz, tzdata, nest_asyncio, ibapi, eventkit, starlette, pandas, ib_async, fastapi, mcp
  Attempting uninstall: starlette
    Found existing installation: starlette 0.47.0
    Uninstalling starlette-0.47.0:
      Successfully uninstalled starlette-0.47.0
  Attempting uninstall: mcp
    Found existing installation: mcp 1.9.2
    Uninstalling mcp-1.9.2:
      Successfully uninstalled mcp-1.9.2
Successfully installed eventkit-1.0.3 fastapi-0.115.12 ib_async-1.0.3 ibapi-9.81.1.post1 mcp-1.8.0 nest_asyncio-1.6.0 pandas-2.2.3 pytz-2025.2 starlette-0.46.2 tzdata-2025.2
