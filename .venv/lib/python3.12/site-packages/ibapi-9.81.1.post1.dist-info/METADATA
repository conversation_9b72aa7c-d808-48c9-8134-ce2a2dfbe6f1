Metadata-Version: 2.4
Name: ibapi
Version: 9.81.1.post1
Summary: Official Interactive Brokers API
Home-page: https://interactivebrokers.github.io/tws-api
Author: IBG LLC
Author-email: <EMAIL>
License: IB API Non-Commercial License or the IB API Commercial License
Keywords: ib,interactive brokers,ibpy,tws
Classifier: Development Status :: 6 - Mature
Classifier: Intended Audience :: Developers
Classifier: License :: Other/Proprietary License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Requires-Python: >=3.1
Description-Content-Type: text/x-rst
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: requires-python
Dynamic: summary

A couple of things/definitions/conventions:

-  a *low level message* is some data prefixed with its size
-  a *high level message* is a list of fields separated by the NULL
   character; the fields are all strings; the message ID is the first
   field, the come others whose number and semantics depend on the
   message itself
-  a *request* is a message from client to TWS/IBGW (IB Gateway)
-  an *answer* is a message from TWS/IBGW to client

How the code is organized:

-  *comm* module: has tools that know how to handle (eg: encode/decode)
   low and high level messages
-  *Connection*: glorified socket
-  *Reader*: thread that uses Connection to read packets, transform to
   low level messages and put in a Queue
-  *Decoder*: knows how to take a low level message and decode into high
   level message
-  *Client*:
-  knows to send requests
-  has the message loop which takes low level messages from Queue and
   uses Decoder to tranform into high level message with which it then
   calls the corresponding Wrapper method
-  *Wrapper*: class that needs to be subclassed by the user so that it
   can get the incoming messages

The info/data flow is:

-  receiving:
-  *Connection.recv\_msg()* (which is essentially a socket) receives the
   packets

   -  uses *Connection.\ *recv*\ all\_msgs()* which tries to combine
      smaller packets into bigger ones based on some trivial heuristic

-  *Reader.run()* uses *Connection.recv\_msg()* to get a packet and then
   uses *comm.read\_msg()* to try to make it a low level message. If
   that can't be done yet (size prefix says so) then it waits for more
   packets
-  if a full low level message is received then it is placed in the
   Queue (remember this is a standalone thread)
-  the main thread runs the *Client.run()* loop which:

   -  gets a low level message from Queue
   -  uses *comm.py* to translate into high level message (fields)
   -  uses *Decoder.interpret()* to act based on that message

-  *Decoder.interpret()* will translate the fields into function
   parameters of the correct type and call with the
   correct/corresponding method of *Wrapper* class

-  sending:
-  *Client* class has methods that implement the *requests*. The user
   will call those request methods with the needed parameters and
   *Client* will send them to the TWS/IBGW.

Implementation notes:

-  the *Decoder* has two ways of handling a message (esentially decoding
   the fields)

   -  some message very neatly map to a function call; meaning that the
      number of fields and order are the same as the method parameters.
      For example: Wrapper.tickSize(). In this case a simple mapping is
      made between the incoming msg id and the Wrapper method:

   IN.TICK\_SIZE: HandleInfo(wrap=Wrapper.tickSize),

   -  other messages are more complex, depend on version number heavily
      or need field massaging. In this case the incoming message id is
      mapped to a processing function that will do all that and call the
      Wrapper method at the end. For example:

   IN.TICK\_PRICE: HandleInfo(proc=processTickPriceMsg),

Instalation notes:

-  you can use this to build a source distribution

python3 setup.py sdist

-  you can use this to build a wheel

python3 setup.py bdist\_wheel

-  you can use this to install the wheel

python3 -m pip install --user --upgrade
dist/ibapi-9.75.1-py3-none-any.whl
