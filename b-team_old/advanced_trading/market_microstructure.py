"""
Market Microstructure Analysis Module
=====================================

Analyzes order book dynamics and market microstructure
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from collections import deque
import time


class MarketMicrostructureAnalyzer:
    """Advanced market microstructure analysis"""
    
    def __init__(self, ib_client):
        self.ib = ib_client
        self.order_book_history = {}
        self.trade_flow = {}
        self.liquidity_metrics = {}
        
    def analyze_order_book_imbalance(self, symbol: str, depth: int = 5) -> Dict:
        """Analyze order book imbalance for trade signals"""
        # Get order book data
        bids, asks = self._get_order_book(symbol, depth)
        
        # Calculate imbalance metrics
        bid_volume = sum(level['size'] for level in bids)
        ask_volume = sum(level['size'] for level in asks)
        
        imbalance_ratio = (bid_volume - ask_volume) / (bid_volume + ask_volume)
        
        # Weighted mid price
        bid_weighted = sum(level['price'] * level['size'] for level in bids) / bid_volume if bid_volume > 0 else 0
        ask_weighted = sum(level['price'] * level['size'] for level in asks) / ask_volume if ask_volume > 0 else 0
        weighted_mid = (bid_weighted + ask_weighted) / 2
        
        # Pressure indicator
        pressure = self._calculate_order_pressure(bids, asks)
        
        return {
            'imbalance_ratio': imbalance_ratio,
            'bid_volume': bid_volume,
            'ask_volume': ask_volume,
            'weighted_mid': weighted_mid,
            'pressure': pressure,
            'signal': self._generate_imbalance_signal(imbalance_ratio, pressure)
        }
    
    def _calculate_order_pressure(self, bids: List[Dict], asks: List[Dict]) -> float:
        """Calculate order book pressure"""
        if not bids or not asks:
            return 0.0
        
        # Calculate weighted pressure based on price distance
        mid_price = (bids[0]['price'] + asks[0]['price']) / 2
        
        bid_pressure = 0
        ask_pressure = 0
        
        for i, level in enumerate(bids):
            distance = abs(level['price'] - mid_price) / mid_price
            weight = 1 / (1 + distance * 10)  # Decay function
            bid_pressure += level['size'] * weight
        
        for i, level in enumerate(asks):
            distance = abs(level['price'] - mid_price) / mid_price
            weight = 1 / (1 + distance * 10)
            ask_pressure += level['size'] * weight
        
        return (bid_pressure - ask_pressure) / (bid_pressure + ask_pressure)
    
    def _generate_imbalance_signal(self, imbalance: float, pressure: float) -> str:
        """Generate trading signal from imbalance"""
        combined_score = (imbalance + pressure) / 2
        
        if combined_score > 0.3:
            return "STRONG_BUY"
        elif combined_score > 0.1:
            return "BUY"
        elif combined_score < -0.3:
            return "STRONG_SELL"
        elif combined_score < -0.1:
            return "SELL"
        else:
            return "NEUTRAL"
    
    def detect_liquidity_gaps(self, symbol: str) -> List[Dict]:
        """Detect gaps in liquidity"""
        bids, asks = self._get_order_book(symbol, 10)
        gaps = []
        
        # Check bid side gaps
        for i in range(len(bids) - 1):
            gap_size = bids[i]['price'] - bids[i+1]['price']
            if gap_size > bids[i]['price'] * 0.001:  # 0.1% gap
                gaps.append({
                    'side': 'BID',
                    'price_from': bids[i+1]['price'],
                    'price_to': bids[i]['price'],
                    'gap_size': gap_size,
                    'gap_percent': gap_size / bids[i]['price']
                })
        
        # Check ask side gaps
        for i in range(len(asks) - 1):
            gap_size = asks[i+1]['price'] - asks[i]['price']
            if gap_size > asks[i]['price'] * 0.001:
                gaps.append({
                    'side': 'ASK',
                    'price_from': asks[i]['price'],
                    'price_to': asks[i+1]['price'],
                    'gap_size': gap_size,
                    'gap_percent': gap_size / asks[i]['price']
                })
        
        return gaps
    
    def analyze_market_impact(self, symbol: str, order_size: int, side: str) -> Dict:
        """Estimate market impact of large orders"""
        bids, asks = self._get_order_book(symbol, 20)
        
        # Simulate order execution
        remaining_size = order_size
        total_cost = 0
        levels_consumed = 0
        
        if side == "BUY":
            book = asks
        else:
            book = bids
        
        for level in book:
            if remaining_size <= 0:
                break
            
            executed = min(remaining_size, level['size'])
            total_cost += executed * level['price']
            remaining_size -= executed
            levels_consumed += 1
        
        if remaining_size > 0:
            # Not enough liquidity
            avg_price = total_cost / (order_size - remaining_size) if order_size > remaining_size else 0
            impact = float('inf')
        else:
            avg_price = total_cost / order_size
            mid_price = (bids[0]['price'] + asks[0]['price']) / 2
            impact = abs(avg_price - mid_price) / mid_price
        
        return {
            'average_price': avg_price,
            'market_impact': impact,
            'levels_consumed': levels_consumed,
            'liquidity_available': order_size - remaining_size,
            'slippage_estimate': impact * order_size * avg_price
        }
    
    def track_order_flow(self, symbol: str, window: int = 100) -> Dict:
        """Track order flow and aggressor side"""
        if symbol not in self.trade_flow:
            self.trade_flow[symbol] = deque(maxlen=window)
        
        # Get recent trades
        trades = self._get_recent_trades(symbol)
        
        for trade in trades:
            self.trade_flow[symbol].append(trade)
        
        # Analyze flow
        buy_volume = sum(t['size'] for t in self.trade_flow[symbol] if t['aggressor'] == 'BUY')
        sell_volume = sum(t['size'] for t in self.trade_flow[symbol] if t['aggressor'] == 'SELL')
        
        flow_ratio = (buy_volume - sell_volume) / (buy_volume + sell_volume) if (buy_volume + sell_volume) > 0 else 0
        
        # VWAP of aggressive orders
        buy_vwap = sum(t['price'] * t['size'] for t in self.trade_flow[symbol] if t['aggressor'] == 'BUY') / buy_volume if buy_volume > 0 else 0
        sell_vwap = sum(t['price'] * t['size'] for t in self.trade_flow[symbol] if t['aggressor'] == 'SELL') / sell_volume if sell_volume > 0 else 0
        
        return {
            'buy_volume': buy_volume,
            'sell_volume': sell_volume,
            'flow_ratio': flow_ratio,
            'buy_vwap': buy_vwap,
            'sell_vwap': sell_vwap,
            'trade_count': len(self.trade_flow[symbol]),
            'momentum': self._calculate_flow_momentum(symbol)
        }
    
    def _calculate_flow_momentum(self, symbol: str) -> float:
        """Calculate order flow momentum"""
        if symbol not in self.trade_flow or len(self.trade_flow[symbol]) < 10:
            return 0.0
        
        recent_trades = list(self.trade_flow[symbol])
        
        # Split into recent and older
        midpoint = len(recent_trades) // 2
        recent = recent_trades[midpoint:]
        older = recent_trades[:midpoint]
        
        recent_buy = sum(t['size'] for t in recent if t['aggressor'] == 'BUY')
        recent_sell = sum(t['size'] for t in recent if t['aggressor'] == 'SELL')
        older_buy = sum(t['size'] for t in older if t['aggressor'] == 'BUY')
        older_sell = sum(t['size'] for t in older if t['aggressor'] == 'SELL')
        
        recent_ratio = (recent_buy - recent_sell) / (recent_buy + recent_sell) if (recent_buy + recent_sell) > 0 else 0
        older_ratio = (older_buy - older_sell) / (older_buy + older_sell) if (older_buy + older_sell) > 0 else 0
        
        return recent_ratio - older_ratio
    
    def identify_hidden_liquidity(self, symbol: str) -> Dict:
        """Identify potential hidden/iceberg orders"""
        trades = list(self.trade_flow.get(symbol, []))
        if len(trades) < 20:
            return {'detected': False}
        
        # Look for repeated executions at same price/size
        price_size_counts = {}
        for trade in trades:
            key = (trade['price'], trade['size'])
            price_size_counts[key] = price_size_counts.get(key, 0) + 1
        
        # Find suspicious patterns
        suspicious = []
        for (price, size), count in price_size_counts.items():
            if count >= 3:  # Same price/size executed 3+ times
                suspicious.append({
                    'price': price,
                    'size': size,
                    'count': count,
                    'likely_iceberg': True
                })
        
        return {
            'detected': len(suspicious) > 0,
            'potential_icebergs': suspicious
        }
    
    def calculate_spread_metrics(self, symbol: str) -> Dict:
        """Calculate various spread metrics"""
        bids, asks = self._get_order_book(symbol, 1)
        
        if not bids or not asks:
            return {}
        
        bid = bids[0]['price']
        ask = asks[0]['price']
        mid = (bid + ask) / 2
        
        # Basic spread metrics
        spread = ask - bid
        spread_bps = (spread / mid) * 10000
        
        # Effective spread (would need actual trades)
        # For now, using quoted spread
        effective_spread = spread
        
        # Calculate over time
        if symbol not in self.liquidity_metrics:
            self.liquidity_metrics[symbol] = deque(maxlen=100)
        
        self.liquidity_metrics[symbol].append({
            'timestamp': time.time(),
            'spread': spread,
            'spread_bps': spread_bps,
            'bid': bid,
            'ask': ask
        })
        
        # Time-weighted average spread
        if len(self.liquidity_metrics[symbol]) > 1:
            total_time = 0
            weighted_spread = 0
            
            metrics = list(self.liquidity_metrics[symbol])
            for i in range(1, len(metrics)):
                time_diff = metrics[i]['timestamp'] - metrics[i-1]['timestamp']
                total_time += time_diff
                weighted_spread += metrics[i-1]['spread_bps'] * time_diff
            
            twas = weighted_spread / total_time if total_time > 0 else spread_bps
        else:
            twas = spread_bps
        
        return {
            'spread': spread,
            'spread_bps': spread_bps,
            'effective_spread': effective_spread,
            'time_weighted_spread': twas,
            'bid': bid,
            'ask': ask,
            'mid': mid
        }
    
    def detect_quote_stuffing(self, symbol: str) -> bool:
        """Detect potential quote stuffing or market manipulation"""
        # Would analyze quote update frequency
        # For now, placeholder
        return False
    
    def _get_order_book(self, symbol: str, depth: int) -> Tuple[List[Dict], List[Dict]]:
        """Get order book data from IB"""
        # Placeholder - would use IB API
        bids = [{'price': 100 - i * 0.01, 'size': 1000 - i * 100} for i in range(depth)]
        asks = [{'price': 100 + i * 0.01, 'size': 1000 - i * 100} for i in range(depth)]
        return bids, asks
    
    def _get_recent_trades(self, symbol: str) -> List[Dict]:
        """Get recent trades from IB"""
        # Placeholder - would use IB API
        return [
            {'price': 100.01, 'size': 100, 'aggressor': 'BUY', 'timestamp': time.time()},
            {'price': 99.99, 'size': 200, 'aggressor': 'SELL', 'timestamp': time.time()}
        ]
