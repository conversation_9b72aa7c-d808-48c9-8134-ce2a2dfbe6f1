"""
Risk Management Module
======================

Advanced risk analytics and automated risk controls
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Tuple, Optional
import warnings


class RiskManager:
    """Comprehensive risk management system"""
    
    def __init__(self, ib_client):
        self.ib = ib_client
        self.risk_limits = {}
        self.risk_metrics = {}
        self.stress_scenarios = {}
        
    def calculate_var(self, portfolio_returns: pd.Series, 
                     confidence_level: float = 0.95,
                     method: str = 'historical') -> float:
        """Calculate Value at Risk"""
        if method == 'historical':
            return self._historical_var(portfolio_returns, confidence_level)
        elif method == 'parametric':
            return self._parametric_var(portfolio_returns, confidence_level)
        elif method == 'monte_carlo':
            return self._monte_carlo_var(portfolio_returns, confidence_level)
        else:
            raise ValueError(f"Unknown VaR method: {method}")
    
    def _historical_var(self, returns: pd.Series, confidence: float) -> float:
        """Historical VaR calculation"""
        return np.percentile(returns, (1 - confidence) * 100)
    
    def _parametric_var(self, returns: pd.Series, confidence: float) -> float:
        """Parametric (variance-covariance) VaR"""
        mean = returns.mean()
        std = returns.std()
        z_score = stats.norm.ppf(1 - confidence)
        return mean + z_score * std
    
    def _monte_carlo_var(self, returns: pd.Series, confidence: float,
                        n_simulations: int = 10000) -> float:
        """Monte Carlo VaR simulation"""
        mean = returns.mean()
        std = returns.std()
        simulated_returns = np.random.normal(mean, std, n_simulations)
        return np.percentile(simulated_returns, (1 - confidence) * 100)
    
    def calculate_cvar(self, portfolio_returns: pd.Series,
                      confidence_level: float = 0.95) -> float:
        """Calculate Conditional Value at Risk (Expected Shortfall)"""
        var = self.calculate_var(portfolio_returns, confidence_level)
        return portfolio_returns[portfolio_returns <= var].mean()
    
    def stress_test_portfolio(self, positions: Dict, 
                            scenarios: List[Dict]) -> pd.DataFrame:
        """Run stress tests on portfolio"""
        results = []
        
        for scenario in scenarios:
            scenario_result = {
                'scenario': scenario['name'],
                'total_impact': 0,
                'details': {}
            }
            
            for asset, position in positions.items():
                if asset in scenario['shocks']:
                    impact = position['value'] * scenario['shocks'][asset]
                    scenario_result['total_impact'] += impact
                    scenario_result['details'][asset] = impact
            
            results.append(scenario_result)
        
        return pd.DataFrame(results)
    
    def create_stress_scenarios(self) -> List[Dict]:
        """Create standard stress test scenarios"""
        scenarios = [
            {
                'name': 'Market Crash 2008',
                'shocks': {
                    'SPY': -0.37,
                    'QQQ': -0.42,
                    'IWM': -0.39,
                    'GLD': 0.05,
                    'TLT': 0.14
                }
            },
            {
                'name': 'COVID-19 Crash',
                'shocks': {
                    'SPY': -0.34,
                    'QQQ': -0.28,
                    'IWM': -0.41,
                    'GLD': 0.08,
                    'TLT': 0.09
                }
            },
            {
                'name': 'Tech Bubble Burst',
                'shocks': {
                    'SPY': -0.22,
                    'QQQ': -0.51,
                    'IWM': -0.15,
                    'GLD': 0.12,
                    'TLT': 0.18
                }
            },
            {
                'name': 'Interest Rate Spike',
                'shocks': {
                    'SPY': -0.15,
                    'QQQ': -0.18,
                    'IWM': -0.12,
                    'GLD': -0.08,
                    'TLT': -0.25
                }
            }
        ]
        return scenarios
    
    def calculate_portfolio_margin(self, positions: Dict) -> float:
        """Calculate portfolio margin requirements"""
        total_margin = 0
        
        for asset, position in positions.items():
            # Simplified margin calculation - would use IB's actual margin model
            if position['type'] == 'STOCK':
                margin = position['value'] * 0.25  # 25% for stocks
            elif position['type'] == 'OPTION':
                margin = self._calculate_option_margin(position)
            elif position['type'] == 'FUTURE':
                margin = position['value'] * 0.10  # 10% for futures
            else:
                margin = position['value'] * 0.50  # Conservative default
            
            total_margin += margin
        
        return total_margin
    
    def _calculate_option_margin(self, position: Dict) -> float:
        """Calculate option margin requirements"""
        # Simplified - actual calculation is complex
        if position['right'] == 'C':
            if position['action'] == 'BUY':
                return 0  # Long options require no margin
            else:
                # Short call margin
                return max(
                    position['underlying_price'] * 0.20 - position['otm_amount'],
                    position['underlying_price'] * 0.10
                ) * position['quantity'] * 100
        else:
            # Put logic similar
            return position['value'] * 0.15
    
    def monitor_real_time_exposure(self, positions: Dict) -> Dict:
        """Monitor real-time risk exposure"""
        exposure = {
            'total_long': 0,
            'total_short': 0,
            'net_exposure': 0,
            'gross_exposure': 0,
            'sector_exposure': {},
            'asset_class_exposure': {},
            'currency_exposure': {}
        }
        
        for asset, position in positions.items():
            value = position['value']
            if position['quantity'] > 0:
                exposure['total_long'] += value
            else:
                exposure['total_short'] += abs(value)
            
            # Aggregate by categories
            if 'sector' in position:
                sector = position['sector']
                exposure['sector_exposure'][sector] = exposure['sector_exposure'].get(sector, 0) + value
            
            if 'asset_class' in position:
                asset_class = position['asset_class']
                exposure['asset_class_exposure'][asset_class] = exposure['asset_class_exposure'].get(asset_class, 0) + value
            
            if 'currency' in position:
                currency = position['currency']
                exposure['currency_exposure'][currency] = exposure['currency_exposure'].get(currency, 0) + value
        
        exposure['net_exposure'] = exposure['total_long'] - exposure['total_short']
        exposure['gross_exposure'] = exposure['total_long'] + exposure['total_short']
        
        return exposure
    
    def create_hedging_strategy(self, positions: Dict, 
                              hedge_ratio: float = 0.8) -> List[Dict]:
        """Create automated hedging recommendations"""
        hedges = []
        
        # Calculate portfolio beta
        portfolio_beta = self._calculate_portfolio_beta(positions)
        
        # Determine hedge amount
        total_exposure = sum(p['value'] for p in positions.values())
        hedge_amount = total_exposure * hedge_ratio * portfolio_beta
        
        # Recommend hedges
        if portfolio_beta > 0:
            # Long bias - hedge with shorts or puts
            hedges.append({
                'instrument': 'SPY',
                'type': 'PUT',
                'amount': hedge_amount,
                'strikes': self._calculate_put_strikes('SPY', hedge_amount)
            })
        else:
            # Short bias - hedge with longs or calls
            hedges.append({
                'instrument': 'SPY',
                'type': 'CALL',
                'amount': abs(hedge_amount),
                'strikes': self._calculate_call_strikes('SPY', abs(hedge_amount))
            })
        
        return hedges
    
    def _calculate_portfolio_beta(self, positions: Dict) -> float:
        """Calculate weighted portfolio beta"""
        total_value = sum(p['value'] for p in positions.values())
        weighted_beta = 0
        
        for asset, position in positions.items():
            weight = position['value'] / total_value
            beta = position.get('beta', 1.0)  # Default beta of 1.0
            weighted_beta += weight * beta
        
        return weighted_beta
    
    def _calculate_put_strikes(self, symbol: str, amount: float) -> List[float]:
        """Calculate optimal put strikes for hedging"""
        # Would fetch current price and calculate strikes
        return []
    
    def _calculate_call_strikes(self, symbol: str, amount: float) -> List[float]:
        """Calculate optimal call strikes for hedging"""
        return []
    
    def set_risk_limits(self, limits: Dict):
        """Set risk limits for automated enforcement"""
        self.risk_limits = limits
    
    def check_risk_limits(self, positions: Dict) -> List[Dict]:
        """Check if any risk limits are breached"""
        breaches = []
        exposure = self.monitor_real_time_exposure(positions)
        
        # Check various limits
        if 'max_position_size' in self.risk_limits:
            for asset, position in positions.items():
                if abs(position['value']) > self.risk_limits['max_position_size']:
                    breaches.append({
                        'type': 'POSITION_SIZE',
                        'asset': asset,
                        'value': position['value'],
                        'limit': self.risk_limits['max_position_size']
                    })
        
        if 'max_gross_exposure' in self.risk_limits:
            if exposure['gross_exposure'] > self.risk_limits['max_gross_exposure']:
                breaches.append({
                    'type': 'GROSS_EXPOSURE',
                    'value': exposure['gross_exposure'],
                    'limit': self.risk_limits['max_gross_exposure']
                })
        
        if 'max_sector_concentration' in self.risk_limits:
            for sector, exp in exposure['sector_exposure'].items():
                concentration = exp / exposure['gross_exposure']
                if concentration > self.risk_limits['max_sector_concentration']:
                    breaches.append({
                        'type': 'SECTOR_CONCENTRATION',
                        'sector': sector,
                        'concentration': concentration,
                        'limit': self.risk_limits['max_sector_concentration']
                    })
        
        return breaches
    
    def calculate_sharpe_ratio(self, returns: pd.Series, 
                             risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
        return np.sqrt(252) * excess_returns.mean() / excess_returns.std()
    
    def calculate_sortino_ratio(self, returns: pd.Series,
                              risk_free_rate: float = 0.02,
                              target_return: float = 0) -> float:
        """Calculate Sortino ratio (downside deviation)"""
        excess_returns = returns - risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < target_return]
        downside_deviation = np.sqrt(np.mean(downside_returns ** 2))
        return np.sqrt(252) * excess_returns.mean() / downside_deviation
    
    def calculate_max_drawdown(self, returns: pd.Series) -> Tuple[float, pd.Timestamp, pd.Timestamp]:
        """Calculate maximum drawdown and dates"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        max_dd = drawdown.min()
        end_date = drawdown.idxmin()
        start_date = cumulative[:end_date].idxmax()
        
        return max_dd, start_date, end_date
