"""
Historical Data Analytics Module
================================

Advanced historical data analysis capabilities
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta


class HistoricalDataAnalyzer:
    """Advanced historical data analytics"""
    
    def __init__(self, ib_client):
        self.ib = ib_client
        self.data_cache = {}
        
    def get_tick_by_tick_data(self, symbol: str, 
                             start_time: datetime,
                             end_time: datetime) -> pd.DataFrame:
        """Retrieve tick-by-tick historical data"""
        # Request tick data from IB
        ticks = self._request_tick_data(symbol, start_time, end_time)
        
        # Convert to DataFrame
        df = pd.DataFrame(ticks)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        return df
    
    def aggregate_bars(self, tick_data: pd.DataFrame,
                      bar_type: str = 'time',
                      bar_size: int = 300) -> pd.DataFrame:
        """Aggregate tick data into custom bars"""
        if bar_type == 'time':
            return self._create_time_bars(tick_data, bar_size)
        elif bar_type == 'volume':
            return self._create_volume_bars(tick_data, bar_size)
        elif bar_type == 'tick':
            return self._create_tick_bars(tick_data, bar_size)
        elif bar_type == 'dollar':
            return self._create_dollar_bars(tick_data, bar_size)
        elif bar_type == 'range':
            return self._create_range_bars(tick_data, bar_size)
        else:
            raise ValueError(f"Unknown bar type: {bar_type}")
    
    def _create_time_bars(self, ticks: pd.DataFrame, seconds: int) -> pd.DataFrame:
        """Create time-based bars"""
        return ticks.resample(f'{seconds}S').agg({
            'price': ['first', 'max', 'min', 'last'],
            'size': 'sum'
        })
    
    def _create_volume_bars(self, ticks: pd.DataFrame, volume: int) -> pd.DataFrame:
        """Create volume-based bars"""
        bars = []
        current_bar = {'open': None, 'high': 0, 'low': float('inf'), 
                      'close': 0, 'volume': 0, 'trades': 0}
        
        for idx, tick in ticks.iterrows():
            if current_bar['open'] is None:
                current_bar['open'] = tick['price']
                current_bar['timestamp'] = idx
            
            current_bar['high'] = max(current_bar['high'], tick['price'])
            current_bar['low'] = min(current_bar['low'], tick['price'])
            current_bar['close'] = tick['price']
            current_bar['volume'] += tick['size']
            current_bar['trades'] += 1
            
            if current_bar['volume'] >= volume:
                bars.append(current_bar.copy())
                current_bar = {'open': None, 'high': 0, 'low': float('inf'),
                             'close': 0, 'volume': 0, 'trades': 0}
        
        return pd.DataFrame(bars).set_index('timestamp')
    
    def _create_tick_bars(self, ticks: pd.DataFrame, num_ticks: int) -> pd.DataFrame:
        """Create tick count bars"""
        bars = []
        for i in range(0, len(ticks), num_ticks):
            chunk = ticks.iloc[i:i+num_ticks]
            if len(chunk) > 0:
                bars.append({
                    'timestamp': chunk.index[0],
                    'open': chunk['price'].iloc[0],
                    'high': chunk['price'].max(),
                    'low': chunk['price'].min(),
                    'close': chunk['price'].iloc[-1],
                    'volume': chunk['size'].sum(),
                    'trades': len(chunk)
                })
        
        return pd.DataFrame(bars).set_index('timestamp')
    
    def _create_dollar_bars(self, ticks: pd.DataFrame, dollar_value: float) -> pd.DataFrame:
        """Create dollar value bars"""
        bars = []
        current_bar = {'open': None, 'high': 0, 'low': float('inf'),
                      'close': 0, 'volume': 0, 'dollar_volume': 0}
        
        for idx, tick in ticks.iterrows():
            if current_bar['open'] is None:
                current_bar['open'] = tick['price']
                current_bar['timestamp'] = idx
            
            tick_dollar = tick['price'] * tick['size']
            current_bar['high'] = max(current_bar['high'], tick['price'])
            current_bar['low'] = min(current_bar['low'], tick['price'])
            current_bar['close'] = tick['price']
            current_bar['volume'] += tick['size']
            current_bar['dollar_volume'] += tick_dollar
            
            if current_bar['dollar_volume'] >= dollar_value:
                bars.append(current_bar.copy())
                current_bar = {'open': None, 'high': 0, 'low': float('inf'),
                             'close': 0, 'volume': 0, 'dollar_volume': 0}
        
        return pd.DataFrame(bars).set_index('timestamp')
    
    def _create_range_bars(self, ticks: pd.DataFrame, price_range: float) -> pd.DataFrame:
        """Create range bars (Renko-like)"""
        bars = []
        current_bar = None
        
        for idx, tick in ticks.iterrows():
            if current_bar is None:
                current_bar = {
                    'timestamp': idx,
                    'open': tick['price'],
                    'high': tick['price'],
                    'low': tick['price'],
                    'close': tick['price'],
                    'volume': tick['size']
                }
            else:
                current_bar['high'] = max(current_bar['high'], tick['price'])
                current_bar['low'] = min(current_bar['low'], tick['price'])
                current_bar['close'] = tick['price']
                current_bar['volume'] += tick['size']
                
                if current_bar['high'] - current_bar['low'] >= price_range:
                    bars.append(current_bar.copy())
                    current_bar = None
        
        return pd.DataFrame(bars).set_index('timestamp')
    
    def calculate_historical_volatility(self, symbol: str,
                                      lookback_days: int = 30,
                                      method: str = 'close_to_close') -> pd.Series:
        """Calculate historical volatility using various methods"""
        data = self._get_historical_data(symbol, lookback_days * 2)
        
        if method == 'close_to_close':
            returns = np.log(data['close'] / data['close'].shift(1))
            volatility = returns.rolling(lookback_days).std() * np.sqrt(252)
        
        elif method == 'parkinson':
            # Parkinson's volatility (using high-low)
            hl_ratio = np.log(data['high'] / data['low'])
            volatility = np.sqrt(1 / (4 * np.log(2)) * (hl_ratio ** 2).rolling(lookback_days).mean()) * np.sqrt(252)
        
        elif method == 'garman_klass':
            # Garman-Klass volatility
            hl_ratio = np.log(data['high'] / data['low'])
            co_ratio = np.log(data['close'] / data['open'])
            
            gk = 0.5 * hl_ratio ** 2 - (2 * np.log(2) - 1) * co_ratio ** 2
            volatility = np.sqrt(gk.rolling(lookback_days).mean()) * np.sqrt(252)
        
        elif method == 'rogers_satchell':
            # Rogers-Satchell volatility
            hc_ratio = np.log(data['high'] / data['close'])
            ho_ratio = np.log(data['high'] / data['open'])
            lc_ratio = np.log(data['low'] / data['close'])
            lo_ratio = np.log(data['low'] / data['open'])
            
            rs = hc_ratio * ho_ratio + lc_ratio * lo_ratio
            volatility = np.sqrt(rs.rolling(lookback_days).mean()) * np.sqrt(252)
        
        elif method == 'yang_zhang':
            # Yang-Zhang volatility (most accurate)
            returns = np.log(data['close'] / data['close'].shift(1))
            overnight = np.log(data['open'] / data['close'].shift(1))
            
            # Components
            overnight_var = overnight.rolling(lookback_days).var()
            close_var = returns.rolling(lookback_days).var()
            
            # Rogers-Satchell component
            hc_ratio = np.log(data['high'] / data['close'])
            ho_ratio = np.log(data['high'] / data['open'])
            lc_ratio = np.log(data['low'] / data['close'])
            lo_ratio = np.log(data['low'] / data['open'])
            rs = hc_ratio * ho_ratio + lc_ratio * lo_ratio
            rs_var = rs.rolling(lookback_days).mean()
            
            # Combine
            k = 0.34 / (1 + (lookback_days + 1) / (lookback_days - 1))
            volatility = np.sqrt((overnight_var + k * close_var + (1 - k) * rs_var) * 252)
        
        else:
            raise ValueError(f"Unknown volatility method: {method}")
        
        return volatility
    
    def generate_correlation_matrix(self, symbols: List[str],
                                  lookback_days: int = 60) -> pd.DataFrame:
        """Generate correlation matrix for multiple symbols"""
        returns_data = {}
        
        for symbol in symbols:
            data = self._get_historical_data(symbol, lookback_days)
            returns_data[symbol] = data['close'].pct_change()
        
        returns_df = pd.DataFrame(returns_data).dropna()
        return returns_df.corr()
    
    def calculate_beta(self, symbol: str, benchmark: str = 'SPY',
                      lookback_days: int = 252) -> float:
        """Calculate beta against benchmark"""
        # Get data
        symbol_data = self._get_historical_data(symbol, lookback_days)
        benchmark_data = self._get_historical_data(benchmark, lookback_days)
        
        # Calculate returns
        symbol_returns = symbol_data['close'].pct_change().dropna()
        benchmark_returns = benchmark_data['close'].pct_change().dropna()
        
        # Align data
        aligned = pd.DataFrame({
            'symbol': symbol_returns,
            'benchmark': benchmark_returns
        }).dropna()
        
        # Calculate beta
        covariance = aligned.cov().iloc[0, 1]
        benchmark_variance = aligned['benchmark'].var()
        
        return covariance / benchmark_variance
    
    def identify_regime_changes(self, symbol: str,
                              method: str = 'volatility') -> pd.DataFrame:
        """Identify market regime changes"""
        data = self._get_historical_data(symbol, 252)
        
        if method == 'volatility':
            # Use volatility regimes
            volatility = self.calculate_historical_volatility(symbol, 20)
            vol_percentiles = volatility.quantile([0.33, 0.67])
            
            regimes = pd.cut(volatility, 
                           bins=[-np.inf, vol_percentiles[0.33], 
                                 vol_percentiles[0.67], np.inf],
                           labels=['Low Vol', 'Medium Vol', 'High Vol'])
        
        elif method == 'trend':
            # Use trend regimes
            sma_50 = data['close'].rolling(50).mean()
            sma_200 = data['close'].rolling(200).mean()
            
            regimes = pd.Series(index=data.index, dtype='object')
            regimes[(data['close'] > sma_50) & (sma_50 > sma_200)] = 'Uptrend'
            regimes[(data['close'] < sma_50) & (sma_50 < sma_200)] = 'Downtrend'
            regimes[~regimes.isin(['Uptrend', 'Downtrend'])] = 'Sideways'
        
        # Identify change points
        regime_changes = regimes != regimes.shift(1)
        
        return pd.DataFrame({
            'regime': regimes,
            'change': regime_changes
        })
    
    def backtest_signal(self, symbol: str, signal: pd.Series,
                       initial_capital: float = 100000) -> Dict:
        """Simple backtesting framework"""
        data = self._get_historical_data(symbol, len(signal))
        
        # Align signal with price data
        prices = data['close']
        
        # Calculate positions
        positions = signal.shift(1)  # Enter next day
        
        # Calculate returns
        returns = prices.pct_change()
        strategy_returns = positions * returns
        
        # Calculate metrics
        cumulative_returns = (1 + strategy_returns).cumprod()
        total_return = cumulative_returns.iloc[-1] - 1
        
        # Sharpe ratio
        sharpe = np.sqrt(252) * strategy_returns.mean() / strategy_returns.std()
        
        # Max drawdown
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Win rate
        winning_trades = strategy_returns[strategy_returns > 0].count()
        total_trades = strategy_returns[strategy_returns != 0].count()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'cumulative_returns': cumulative_returns
        }
    
    def calculate_technical_indicators(self, symbol: str) -> pd.DataFrame:
        """Calculate comprehensive technical indicators"""
        data = self._get_historical_data(symbol, 200)
        
        # Trend indicators
        data['SMA_20'] = data['close'].rolling(20).mean()
        data['SMA_50'] = data['close'].rolling(50).mean()
        data['SMA_200'] = data['close'].rolling(200).mean()
        data['EMA_12'] = data['close'].ewm(span=12).mean()
        data['EMA_26'] = data['close'].ewm(span=26).mean()
        
        # MACD
        data['MACD'] = data['EMA_12'] - data['EMA_26']
        data['MACD_signal'] = data['MACD'].ewm(span=9).mean()
        data['MACD_histogram'] = data['MACD'] - data['MACD_signal']
        
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        data['RSI'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        data['BB_middle'] = data['close'].rolling(20).mean()
        bb_std = data['close'].rolling(20).std()
        data['BB_upper'] = data['BB_middle'] + 2 * bb_std
        data['BB_lower'] = data['BB_middle'] - 2 * bb_std
        
        # ATR
        high_low = data['high'] - data['low']
        high_close = abs(data['high'] - data['close'].shift())
        low_close = abs(data['low'] - data['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        data['ATR'] = true_range.rolling(14).mean()
        
        # Stochastic
        low_14 = data['low'].rolling(14).min()
        high_14 = data['high'].rolling(14).max()
        data['Stoch_K'] = 100 * ((data['close'] - low_14) / (high_14 - low_14))
        data['Stoch_D'] = data['Stoch_K'].rolling(3).mean()
        
        return data
    
    def _request_tick_data(self, symbol: str, start: datetime, end: datetime) -> List[Dict]:
        """Request tick data from IB API"""
        # Placeholder - would use IB API
        return []
    
    def _get_historical_data(self, symbol: str, days: int) -> pd.DataFrame:
        """Get historical OHLCV data"""
        # Placeholder - would use IB API
        dates = pd.date_range(end=datetime.now(), periods=days)
        return pd.DataFrame({
            'open': np.random.randn(days).cumsum() + 100,
            'high': np.random.randn(days).cumsum() + 101,
            'low': np.random.randn(days).cumsum() + 99,
            'close': np.random.randn(days).cumsum() + 100,
            'volume': np.random.randint(1000000, 10000000, days)
        }, index=dates)
