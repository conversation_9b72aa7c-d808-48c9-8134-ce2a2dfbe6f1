"""
Portfolio Analytics Module
=========================

Advanced portfolio analysis and optimization tools
"""

import pandas as pd
import numpy as np
from scipy.optimize import minimize
from typing import Dict, List, Tuple, Optional
import warnings


class PortfolioAnalyzer:
    """Comprehensive portfolio analytics"""
    
    def __init__(self, ib_client):
        self.ib = ib_client
        self.analytics_cache = {}
        
    def calculate_performance_attribution(self, portfolio: pd.DataFrame,
                                        benchmark: pd.DataFrame,
                                        start_date: pd.Timestamp,
                                        end_date: pd.Timestamp) -> Dict:
        """Perform performance attribution analysis"""
        # Calculate returns
        portfolio_return = self._calculate_portfolio_return(portfolio, start_date, end_date)
        benchmark_return = self._calculate_benchmark_return(benchmark, start_date, end_date)
        
        # Attribution components
        attribution = {
            'total_return': portfolio_return,
            'benchmark_return': benchmark_return,
            'excess_return': portfolio_return - benchmark_return,
            'asset_allocation': self._calculate_asset_allocation_effect(portfolio, benchmark),
            'security_selection': self._calculate_security_selection_effect(portfolio, benchmark),
            'interaction_effect': self._calculate_interaction_effect(portfolio, benchmark),
            'currency_effect': self._calculate_currency_effect(portfolio)
        }
        
        # Sector attribution
        attribution['sector_attribution'] = self._calculate_sector_attribution(portfolio, benchmark)
        
        return attribution
    
    def perform_factor_analysis(self, returns: pd.DataFrame,
                              factors: List[str] = None) -> Dict:
        """Analyze portfolio exposure to various factors"""
        if factors is None:
            factors = ['market', 'size', 'value', 'momentum', 'quality', 'volatility']
        
        # Get factor returns
        factor_returns = self._get_factor_returns(factors, returns.index)
        
        # Run regression
        results = {}
        for factor in factors:
            if factor in factor_returns.columns:
                beta, alpha, r_squared = self._run_factor_regression(
                    returns, factor_returns[factor]
                )
                results[factor] = {
                    'beta': beta,
                    'alpha': alpha,
                    'r_squared': r_squared,
                    'contribution': beta * factor_returns[factor].mean() * 252
                }
        
        # Multi-factor model
        multi_factor_results = self._run_multi_factor_regression(returns, factor_returns)
        results['multi_factor'] = multi_factor_results
        
        return results
    
    def optimize_portfolio_sharpe(self, expected_returns: pd.Series,
                                cov_matrix: pd.DataFrame,
                                risk_free_rate: float = 0.02) -> Dict:
        """Optimize portfolio for maximum Sharpe ratio"""
        n_assets = len(expected_returns)
        
        # Objective function (negative Sharpe ratio)
        def neg_sharpe(weights):
            portfolio_return = np.sum(expected_returns * weights)
            portfolio_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            return -(portfolio_return - risk_free_rate) / portfolio_vol
        
        # Constraints
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}  # Weights sum to 1
        ]
        
        # Bounds (no short selling)
        bounds = tuple((0, 1) for _ in range(n_assets))
        
        # Initial guess (equal weights)
        x0 = np.array([1/n_assets] * n_assets)
        
        # Optimize
        result = minimize(neg_sharpe, x0, method='SLSQP',
                        bounds=bounds, constraints=constraints)
        
        optimal_weights = result.x
        
        # Calculate portfolio metrics
        portfolio_return = np.sum(expected_returns * optimal_weights)
        portfolio_vol = np.sqrt(np.dot(optimal_weights.T, np.dot(cov_matrix, optimal_weights)))
        sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_vol
        
        return {
            'weights': dict(zip(expected_returns.index, optimal_weights)),
            'expected_return': portfolio_return,
            'volatility': portfolio_vol,
            'sharpe_ratio': sharpe_ratio
        }
    
    def calculate_drawdown_analysis(self, returns: pd.Series) -> Dict:
        """Comprehensive drawdown analysis"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        # Find all drawdown periods
        drawdown_periods = []
        in_drawdown = False
        start_idx = None
        
        for i in range(len(drawdown)):
            if drawdown.iloc[i] < 0 and not in_drawdown:
                in_drawdown = True
                start_idx = i
            elif drawdown.iloc[i] == 0 and in_drawdown:
                in_drawdown = False
                end_idx = i
                recovery_idx = i
                
                period_drawdown = drawdown.iloc[start_idx:end_idx+1]
                
                drawdown_periods.append({
                    'start_date': drawdown.index[start_idx],
                    'end_date': drawdown.index[end_idx],
                    'recovery_date': drawdown.index[recovery_idx],
                    'max_drawdown': period_drawdown.min(),
                    'duration_days': (drawdown.index[end_idx] - drawdown.index[start_idx]).days,
                    'recovery_days': (drawdown.index[recovery_idx] - drawdown.index[end_idx]).days
                })
        
        # Summary statistics
        all_drawdowns = [d['max_drawdown'] for d in drawdown_periods]
        durations = [d['duration_days'] for d in drawdown_periods]
        
        return {
            'current_drawdown': drawdown.iloc[-1],
            'max_drawdown': min(all_drawdowns) if all_drawdowns else 0,
            'avg_drawdown': np.mean(all_drawdowns) if all_drawdowns else 0,
            'num_drawdowns': len(drawdown_periods),
            'avg_duration': np.mean(durations) if durations else 0,
            'worst_drawdown_period': min(drawdown_periods, 
                                       key=lambda x: x['max_drawdown']) if drawdown_periods else None,
            'all_drawdowns': drawdown_periods
        }
    
    def run_monte_carlo_simulation(self, expected_returns: pd.Series,
                                 cov_matrix: pd.DataFrame,
                                 initial_value: float = 100000,
                                 time_horizon: int = 252,
                                 num_simulations: int = 10000) -> Dict:
        """Run Monte Carlo simulation for portfolio outcomes"""
        results = []
        
        for _ in range(num_simulations):
            # Generate random returns
            random_returns = np.random.multivariate_normal(
                expected_returns, cov_matrix, time_horizon
            )
            
            # Calculate portfolio value path
            portfolio_values = [initial_value]
            for daily_returns in random_returns:
                next_value = portfolio_values[-1] * (1 + np.sum(daily_returns))
                portfolio_values.append(next_value)
            
            final_value = portfolio_values[-1]
            max_value = max(portfolio_values)
            min_value = min(portfolio_values)
            
            results.append({
                'final_value': final_value,
                'total_return': (final_value - initial_value) / initial_value,
                'max_value': max_value,
                'min_value': min_value,
                'max_drawdown': (min_value - max_value) / max_value if max_value > 0 else 0
            })
        
        results_df = pd.DataFrame(results)
        
        return {
            'expected_final_value': results_df['final_value'].mean(),
            'median_final_value': results_df['final_value'].median(),
            'percentiles': {
                '5%': results_df['final_value'].quantile(0.05),
                '25%': results_df['final_value'].quantile(0.25),
                '75%': results_df['final_value'].quantile(0.75),
                '95%': results_df['final_value'].quantile(0.95)
            },
            'probability_of_loss': (results_df['final_value'] < initial_value).mean(),
            'expected_return': results_df['total_return'].mean(),
            'return_volatility': results_df['total_return'].std(),
            'worst_case_return': results_df['total_return'].min(),
            'best_case_return': results_df['total_return'].max(),
            'avg_max_drawdown': results_df['max_drawdown'].mean()
        }
    
    def calculate_risk_metrics(self, returns: pd.Series) -> Dict:
        """Calculate comprehensive risk metrics"""
        # Basic statistics
        mean_return = returns.mean()
        std_return = returns.std()
        
        # Downside risk metrics
        downside_returns = returns[returns < 0]
        downside_deviation = np.sqrt((downside_returns ** 2).mean())
        
        # Higher moments
        skewness = returns.skew()
        kurtosis = returns.kurtosis()
        
        # Tail risk
        var_95 = returns.quantile(0.05)
        var_99 = returns.quantile(0.01)
        cvar_95 = returns[returns <= var_95].mean()
        cvar_99 = returns[returns <= var_99].mean()
        
        # Risk-adjusted returns
        sharpe_ratio = mean_return / std_return * np.sqrt(252)
        sortino_ratio = mean_return / downside_deviation * np.sqrt(252)
        calmar_ratio = mean_return * 252 / abs(self.calculate_drawdown_analysis(returns)['max_drawdown'])
        
        return {
            'volatility': std_return * np.sqrt(252),
            'downside_deviation': downside_deviation * np.sqrt(252),
            'skewness': skewness,
            'kurtosis': kurtosis,
            'var_95': var_95,
            'var_99': var_99,
            'cvar_95': cvar_95,
            'cvar_99': cvar_99,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'max_daily_loss': returns.min(),
            'max_daily_gain': returns.max()
        }
    
    def optimize_risk_parity(self, cov_matrix: pd.DataFrame) -> Dict:
        """Optimize portfolio using risk parity approach"""
        n_assets = len(cov_matrix)
        
        # Objective: equal risk contribution
        def risk_parity_objective(weights):
            portfolio_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            marginal_contrib = np.dot(cov_matrix, weights) / portfolio_vol
            risk_contrib = weights * marginal_contrib
            
            # Target equal contribution
            target_contrib = portfolio_vol / n_assets
            
            # Minimize squared deviations
            return np.sum((risk_contrib - target_contrib) ** 2)
        
        # Constraints
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}
        ]
        
        # Bounds
        bounds = tuple((0.01, 1) for _ in range(n_assets))
        
        # Initial guess
        x0 = np.array([1/n_assets] * n_assets)
        
        # Optimize
        result = minimize(risk_parity_objective, x0, method='SLSQP',
                        bounds=bounds, constraints=constraints)
        
        optimal_weights = result.x
        
        # Calculate risk contributions
        portfolio_vol = np.sqrt(np.dot(optimal_weights.T, np.dot(cov_matrix, optimal_weights)))
        marginal_contrib = np.dot(cov_matrix, optimal_weights) / portfolio_vol
        risk_contributions = optimal_weights * marginal_contrib
        
        return {
            'weights': dict(zip(cov_matrix.index, optimal_weights)),
            'risk_contributions': dict(zip(cov_matrix.index, risk_contributions)),
            'portfolio_volatility': portfolio_vol
        }
    
    def optimize_minimum_variance(self, cov_matrix: pd.DataFrame) -> Dict:
        """Optimize for minimum variance portfolio"""
        n_assets = len(cov_matrix)
        
        # Objective function
        def portfolio_variance(weights):
            return np.dot(weights.T, np.dot(cov_matrix, weights))
        
        # Constraints
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}
        ]
        
        # Bounds
        bounds = tuple((0, 1) for _ in range(n_assets))
        
        # Initial guess
        x0 = np.array([1/n_assets] * n_assets)
        
        # Optimize
        result = minimize(portfolio_variance, x0, method='SLSQP',
                        bounds=bounds, constraints=constraints)
        
        optimal_weights = result.x
        portfolio_vol = np.sqrt(result.fun)
        
        return {
            'weights': dict(zip(cov_matrix.index, optimal_weights)),
            'volatility': portfolio_vol
        }
    
    def calculate_portfolio_turnover(self, weights_history: pd.DataFrame) -> Dict:
        """Calculate portfolio turnover metrics"""
        # Calculate changes in weights
        weight_changes = weights_history.diff().abs()
        
        # Daily turnover
        daily_turnover = weight_changes.sum(axis=1) / 2  # Divide by 2 (buy + sell)
        
        # Annualized turnover
        annual_turnover = daily_turnover.mean() * 252
        
        # Turnover by asset
        asset_turnover = weight_changes.mean() * 252
        
        return {
            'daily_turnover': daily_turnover,
            'annual_turnover': annual_turnover,
            'asset_turnover': asset_turnover.to_dict(),
            'avg_holding_period': 1 / annual_turnover if annual_turnover > 0 else float('inf')
        }
    
    def _calculate_portfolio_return(self, portfolio: pd.DataFrame, 
                                  start: pd.Timestamp, end: pd.Timestamp) -> float:
        """Calculate portfolio return over period"""
        # Placeholder implementation
        return np.random.uniform(-0.1, 0.2)
    
    def _calculate_benchmark_return(self, benchmark: pd.DataFrame,
                                  start: pd.Timestamp, end: pd.Timestamp) -> float:
        """Calculate benchmark return over period"""
        return np.random.uniform(-0.05, 0.15)
    
    def _calculate_asset_allocation_effect(self, portfolio: pd.DataFrame,
                                         benchmark: pd.DataFrame) -> float:
        """Calculate asset allocation effect"""
        return np.random.uniform(-0.02, 0.02)
    
    def _calculate_security_selection_effect(self, portfolio: pd.DataFrame,
                                           benchmark: pd.DataFrame) -> float:
        """Calculate security selection effect"""
        return np.random.uniform(-0.03, 0.03)
    
    def _calculate_interaction_effect(self, portfolio: pd.DataFrame,
                                    benchmark: pd.DataFrame) -> float:
        """Calculate interaction effect"""
        return np.random.uniform(-0.01, 0.01)
    
    def _calculate_currency_effect(self, portfolio: pd.DataFrame) -> float:
        """Calculate currency effect"""
        return np.random.uniform(-0.02, 0.02)
    
    def _calculate_sector_attribution(self, portfolio: pd.DataFrame,
                                    benchmark: pd.DataFrame) -> Dict:
        """Calculate sector-level attribution"""
        sectors = ['Technology', 'Healthcare', 'Finance', 'Consumer', 'Industrial']
        return {sector: np.random.uniform(-0.01, 0.01) for sector in sectors}
    
    def _get_factor_returns(self, factors: List[str], dates: pd.Index) -> pd.DataFrame:
        """Get factor returns data"""
        # Placeholder - would fetch actual factor data
        data = {}
        for factor in factors:
            data[factor] = pd.Series(
                np.random.randn(len(dates)) * 0.01,
                index=dates
            )
        return pd.DataFrame(data)
    
    def _run_factor_regression(self, returns: pd.Series, 
                             factor_returns: pd.Series) -> Tuple[float, float, float]:
        """Run single factor regression"""
        # Align data
        aligned = pd.DataFrame({
            'returns': returns,
            'factor': factor_returns
        }).dropna()
        
        if len(aligned) < 30:
            return 0, 0, 0
        
        # Simple linear regression
        X = aligned['factor'].values.reshape(-1, 1)
        y = aligned['returns'].values
        
        # Calculate beta
        cov = np.cov(X.flatten(), y)
        beta = cov[0, 1] / cov[0, 0]
        
        # Calculate alpha
        alpha = y.mean() - beta * X.mean()
        
        # Calculate R-squared
        y_pred = alpha + beta * X.flatten()
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - y.mean()) ** 2)
        r_squared = 1 - (ss_res / ss_tot)
        
        return beta, alpha, r_squared
    
    def _run_multi_factor_regression(self, returns: pd.Series,
                                   factor_returns: pd.DataFrame) -> Dict:
        """Run multi-factor regression"""
        # Placeholder
        return {
            'betas': {col: np.random.uniform(-1, 1) for col in factor_returns.columns},
            'alpha': np.random.uniform(-0.01, 0.01),
            'r_squared': np.random.uniform(0.5, 0.9)
        }
