"""
Options Trading Automation Module
=================================

Implements complex options strategies and Greeks calculations
"""

from ibapi.contract import Contract, ComboLeg
from ibapi.order import Order
import numpy as np
from scipy.stats import norm
from typing import List, Dict, Tuple, Optional
import pandas as pd


class OptionsTrader:
    """Advanced options trading automation"""
    
    def __init__(self, ib_client):
        self.ib = ib_client
        self.option_chains = {}
        self.greeks_cache = {}
        
    def create_option_contract(self, symbol: str, expiry: str, strike: float, 
                             right: str, exchange: str = "SMART") -> Contract:
        """Create an option contract"""
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "OPT"
        contract.exchange = exchange
        contract.currency = "USD"
        contract.lastTradeDateOrContractMonth = expiry
        contract.strike = strike
        contract.right = right  # "C" for call, "P" for put
        return contract
    
    def calculate_greeks(self, S: float, K: float, T: float, r: float, 
                        sigma: float, option_type: str = 'C') -> Dict[str, float]:
        """Calculate option Greeks using Black-Scholes"""
        d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        if option_type == 'C':
            delta = norm.cdf(d1)
            theta = (-S * norm.pdf(d1) * sigma / (2 * np.sqrt(T)) 
                    - r * K * np.exp(-r * T) * norm.cdf(d2)) / 365
        else:
            delta = norm.cdf(d1) - 1
            theta = (-S * norm.pdf(d1) * sigma / (2 * np.sqrt(T)) 
                    + r * K * np.exp(-r * T) * norm.cdf(-d2)) / 365
        
        gamma = norm.pdf(d1) / (S * sigma * np.sqrt(T))
        vega = S * norm.pdf(d1) * np.sqrt(T) / 100
        rho = K * T * np.exp(-r * T) * norm.cdf(d2) / 100 if option_type == 'C' else -K * T * np.exp(-r * T) * norm.cdf(-d2) / 100
        
        return {
            'delta': delta,
            'gamma': gamma,
            'theta': theta,
            'vega': vega,
            'rho': rho
        }
    
    def create_iron_condor(self, symbol: str, expiry: str, 
                          put_sell_strike: float, put_buy_strike: float,
                          call_sell_strike: float, call_buy_strike: float) -> List[Contract]:
        """Create an iron condor strategy"""
        contracts = []
        
        # Sell put
        put_sell = self.create_option_contract(symbol, expiry, put_sell_strike, "P")
        contracts.append(('SELL', put_sell))
        
        # Buy put (protection)
        put_buy = self.create_option_contract(symbol, expiry, put_buy_strike, "P")
        contracts.append(('BUY', put_buy))
        
        # Sell call
        call_sell = self.create_option_contract(symbol, expiry, call_sell_strike, "C")
        contracts.append(('SELL', call_sell))
        
        # Buy call (protection)
        call_buy = self.create_option_contract(symbol, expiry, call_buy_strike, "C")
        contracts.append(('BUY', call_buy))
        
        return contracts
    
    def create_butterfly_spread(self, symbol: str, expiry: str,
                               lower_strike: float, middle_strike: float, 
                               upper_strike: float, option_type: str = 'C') -> List[Contract]:
        """Create a butterfly spread"""
        contracts = []
        
        # Buy lower strike
        lower = self.create_option_contract(symbol, expiry, lower_strike, option_type)
        contracts.append(('BUY', lower))
        
        # Sell 2x middle strike
        middle = self.create_option_contract(symbol, expiry, middle_strike, option_type)
        contracts.append(('SELL', middle, 2))
        
        # Buy upper strike
        upper = self.create_option_contract(symbol, expiry, upper_strike, option_type)
        contracts.append(('BUY', upper))
        
        return contracts
    
    def create_straddle(self, symbol: str, expiry: str, strike: float, 
                       action: str = 'BUY') -> List[Contract]:
        """Create a straddle strategy"""
        contracts = []
        
        # Call leg
        call = self.create_option_contract(symbol, expiry, strike, "C")
        contracts.append((action, call))
        
        # Put leg
        put = self.create_option_contract(symbol, expiry, strike, "P")
        contracts.append((action, put))
        
        return contracts
    
    def scan_option_chain(self, symbol: str, expiry: str) -> pd.DataFrame:
        """Scan and analyze entire option chain"""
        # This would connect to IB to get the full option chain
        # For now, returning structure
        return pd.DataFrame({
            'strike': [],
            'bid_call': [],
            'ask_call': [],
            'volume_call': [],
            'oi_call': [],
            'iv_call': [],
            'bid_put': [],
            'ask_put': [],
            'volume_put': [],
            'oi_put': [],
            'iv_put': []
        })
    
    def find_high_iv_options(self, symbol: str, min_iv: float = 0.5) -> pd.DataFrame:
        """Find options with high implied volatility"""
        chain = self.scan_option_chain(symbol, "")
        high_iv = chain[(chain['iv_call'] > min_iv) | (chain['iv_put'] > min_iv)]
        return high_iv
    
    def create_calendar_spread(self, symbol: str, strike: float,
                              near_expiry: str, far_expiry: str,
                              option_type: str = 'C') -> List[Contract]:
        """Create a calendar spread"""
        contracts = []
        
        # Sell near term
        near = self.create_option_contract(symbol, near_expiry, strike, option_type)
        contracts.append(('SELL', near))
        
        # Buy far term
        far = self.create_option_contract(symbol, far_expiry, strike, option_type)
        contracts.append(('BUY', far))
        
        return contracts
    
    def auto_roll_options(self, position: Dict, days_to_expiry: int = 30) -> List[Order]:
        """Automatically roll options positions"""
        orders = []
        # Logic to close existing position and open new one
        return orders
    
    def volatility_surface_analysis(self, symbol: str) -> Dict:
        """Analyze volatility surface for opportunities"""
        analysis = {
            'skew': self.calculate_volatility_skew(symbol),
            'term_structure': self.analyze_term_structure(symbol),
            'anomalies': self.find_volatility_anomalies(symbol)
        }
        return analysis
    
    def calculate_volatility_skew(self, symbol: str) -> float:
        """Calculate volatility skew"""
        # Implementation would analyze IV across strikes
        return 0.0
    
    def analyze_term_structure(self, symbol: str) -> Dict:
        """Analyze volatility term structure"""
        return {}
    
    def find_volatility_anomalies(self, symbol: str) -> List[Dict]:
        """Find volatility arbitrage opportunities"""
        return []
