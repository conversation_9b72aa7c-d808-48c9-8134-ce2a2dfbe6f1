"""
Advanced Execution Algorithms Module
====================================

Implementation of sophisticated execution algorithms
"""

import pandas as pd
import numpy as np
import random
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from ibapi.order import Order


class ExecutionAlgorithms:
    """Advanced execution algorithms for optimal order execution"""
    
    def __init__(self, ib_client):
        self.ib = ib_client
        self.execution_analytics = {}
        
    def implementation_shortfall(self, symbol: str, side: str, 
                               quantity: int, urgency: float = 0.5) -> List[Order]:
        """Implementation Shortfall algorithm to minimize market impact"""
        # Get market data
        arrival_price = self._get_current_price(symbol)
        volatility = self._get_volatility(symbol)
        adv = self._get_average_daily_volume(symbol)
        spread = self._get_spread(symbol)
        
        # Calculate optimal trajectory
        participation_rate = self._calculate_participation_rate(quantity, adv, urgency)
        time_horizon = quantity / (adv * participation_rate)
        
        # Generate child orders
        orders = []
        remaining = quantity
        current_time = datetime.now()
        
        while remaining > 0:
            # Calculate slice size
            slice_size = min(
                int(adv * participation_rate * 0.01),  # 1% of expected volume
                remaining
            )
            
            # Adjust aggressiveness based on market conditions
            if self._is_favorable_market(symbol, side):
                slice_size = int(slice_size * 1.2)
            
            # Create order
            order = Order()
            order.action = side
            order.totalQuantity = slice_size
            order.orderType = "LMT"
            order.lmtPrice = self._calculate_limit_price(symbol, side, urgency)
            order.tif = "IOC"  # Immediate or Cancel
            
            orders.append(order)
            remaining -= slice_size
            
            # Add delay between slices
            if remaining > 0:
                delay = self._calculate_slice_delay(volatility, participation_rate)
                order.goodAfterTime = (current_time + timedelta(seconds=delay)).strftime("%Y%m%d %H:%M:%S")
                current_time += timedelta(seconds=delay)
        
        return orders
    
    def percentage_of_volume(self, symbol: str, side: str,
                           quantity: int, pov_target: float = 0.1,
                           start_time: datetime = None,
                           end_time: datetime = None) -> List[Order]:
        """POV algorithm to match a percentage of market volume"""
        orders = []
        
        # Set time boundaries
        if start_time is None:
            start_time = datetime.now()
        if end_time is None:
            end_time = start_time + timedelta(hours=1)
        
        # Calculate expected volume
        expected_volume = self._estimate_volume(symbol, start_time, end_time)
        target_quantity = min(quantity, int(expected_volume * pov_target))
        
        # Generate schedule
        time_slices = self._generate_time_slices(start_time, end_time, 20)
        slice_quantity = target_quantity // len(time_slices)
        
        for slice_time in time_slices:
            order = Order()
            order.action = side
            order.totalQuantity = slice_quantity
            order.orderType = "LMT"
            order.lmtPrice = self._calculate_adaptive_price(symbol, side)
            order.goodAfterTime = slice_time.strftime("%Y%m%d %H:%M:%S")
            
            # Add POV condition
            order.algoStrategy = "Adaptive"
            order.algoParams = [
                ("adaptivePriority", "Normal"),
                ("maxPctVol", str(pov_target))
            ]
            
            orders.append(order)
        
        return orders
    
    def vwap_strategy(self, symbol: str, side: str, quantity: int,
                     start_time: datetime = None,
                     end_time: datetime = None) -> List[Order]:
        """VWAP algorithm to match volume-weighted average price"""
        # Get historical volume distribution
        volume_curve = self._get_volume_curve(symbol)
        
        # Set default times
        if start_time is None:
            start_time = datetime.now()
        if end_time is None:
            end_time = start_time + timedelta(hours=6.5)  # Full trading day
        
        # Distribute quantity according to typical volume pattern
        orders = []
        time_buckets = self._create_time_buckets(start_time, end_time, 30)
        
        for i, (bucket_start, bucket_end) in enumerate(time_buckets):
            # Get expected volume percentage for this time bucket
            bucket_pct = volume_curve.get(bucket_start.hour, 0.033)  # Default 3.3% per 30min
            bucket_quantity = int(quantity * bucket_pct)
            
            if bucket_quantity > 0:
                order = Order()
                order.action = side
                order.totalQuantity = bucket_quantity
                order.orderType = "LMT"
                order.lmtPrice = self._calculate_vwap_price(symbol, side)
                order.goodAfterTime = bucket_start.strftime("%Y%m%d %H:%M:%S")
                order.goodTillDate = bucket_end.strftime("%Y%m%d %H:%M:%S")
                
                orders.append(order)
        
        return orders
    
    def twap_strategy(self, symbol: str, side: str, quantity: int,
                     duration_minutes: int = 60) -> List[Order]:
        """TWAP algorithm for even distribution over time"""
        # Calculate slice parameters
        num_slices = min(duration_minutes, 60)  # Max 1 slice per minute
        slice_size = quantity // num_slices
        slice_interval = duration_minutes / num_slices
        
        orders = []
        current_time = datetime.now()
        
        for i in range(num_slices):
            order = Order()
            order.action = side
            order.totalQuantity = slice_size + (1 if i < quantity % num_slices else 0)
            order.orderType = "LMT"
            order.lmtPrice = self._calculate_passive_price(symbol, side)
            order.goodAfterTime = current_time.strftime("%Y%m%d %H:%M:%S")
            
            orders.append(order)
            current_time += timedelta(minutes=slice_interval)
        
        return orders
    
    def arrival_price_algo(self, symbol: str, side: str, quantity: int,
                          max_risk: float = 0.01) -> List[Order]:
        """Arrival Price algorithm to minimize slippage from arrival price"""
        arrival_price = self._get_current_price(symbol)
        volatility = self._get_volatility(symbol)
        
        # Calculate risk budget
        risk_budget = arrival_price * max_risk
        
        # Determine aggressiveness based on volatility
        if volatility > 0.02:  # High volatility
            # Be more aggressive to avoid adverse selection
            urgency = 0.8
            time_limit = 300  # 5 minutes
        else:
            urgency = 0.5
            time_limit = 900  # 15 minutes
        
        # Generate aggressive initial order
        initial_size = int(quantity * 0.3)  # 30% immediately
        
        orders = []
        
        # Initial aggressive order
        order1 = Order()
        order1.action = side
        order1.totalQuantity = initial_size
        order1.orderType = "LMT"
        order1.lmtPrice = arrival_price * (1.001 if side == "BUY" else 0.999)
        orders.append(order1)
        
        # Follow-up orders
        remaining = quantity - initial_size
        slices = 5
        
        for i in range(slices):
            slice_quantity = remaining // slices
            delay = (i + 1) * (time_limit / slices)
            
            order = Order()
            order.action = side
            order.totalQuantity = slice_quantity
            order.orderType = "LMT"
            order.lmtPrice = self._calculate_arrival_limit(arrival_price, side, volatility, delay)
            order.goodAfterTime = (datetime.now() + timedelta(seconds=delay)).strftime("%Y%m%d %H:%M:%S")
            
            orders.append(order)
        
        return orders
    
    def target_close_algo(self, symbol: str, side: str, quantity: int,
                         offset_minutes: int = 10) -> List[Order]:
        """Target Close algorithm to execute near market close"""
        # Calculate start time (X minutes before close)
        market_close = self._get_market_close_time()
        start_time = market_close - timedelta(minutes=offset_minutes)
        
        # Use aggressive POV near close
        orders = []
        
        # Main order for last 10 minutes
        order = Order()
        order.action = side
        order.totalQuantity = int(quantity * 0.8)  # 80% in auction
        order.orderType = "MOC"  # Market on Close
        orders.append(order)
        
        # Pre-positioning orders
        pre_position_qty = int(quantity * 0.2)
        if pre_position_qty > 0:
            pre_order = Order()
            pre_order.action = side
            pre_order.totalQuantity = pre_position_qty
            pre_order.orderType = "LMT"
            pre_order.lmtPrice = self._get_current_price(symbol)
            pre_order.goodAfterTime = start_time.strftime("%Y%m%d %H:%M:%S")
            orders.append(pre_order)
        
        return orders
    
    def dark_pool_sweep(self, symbol: str, side: str, quantity: int) -> List[Order]:
        """Dark pool seeking algorithm"""
        orders = []
        
        # Create orders for different dark pools
        dark_pools = ['IATS', 'SIGMA', 'LEVEL', 'IBDARK']
        slice_quantity = quantity // len(dark_pools)
        
        for pool in dark_pools:
            order = Order()
            order.action = side
            order.totalQuantity = slice_quantity
            order.orderType = "LMT"
            order.lmtPrice = self._get_midpoint_price(symbol)
            order.exchange = pool
            order.tif = "IOC"
            
            orders.append(order)
        
        return orders
    
    def iceberg_order(self, symbol: str, side: str, 
                     total_quantity: int, display_size: int) -> Order:
        """Create an iceberg order"""
        order = Order()
        order.action = side
        order.totalQuantity = total_quantity
        order.orderType = "LMT"
        order.lmtPrice = self._calculate_passive_price(symbol, side)
        order.displaySize = display_size
        order.tif = "GTC"
        
        return order
    
    def sniper_algo(self, symbol: str, side: str, quantity: int,
                   trigger_price: float, max_slippage: float = 0.002) -> List[Order]:
        """Sniper algorithm for precise entry at specific price levels"""
        orders = []
        
        # Monitor order waiting for trigger
        monitor_order = Order()
        monitor_order.action = side
        monitor_order.totalQuantity = 100  # Small monitoring size
        monitor_order.orderType = "LMT"
        monitor_order.lmtPrice = trigger_price
        monitor_order.tif = "GTC"
        
        # Main execution order (triggered after monitor fills)
        main_order = Order()
        main_order.action = side
        main_order.totalQuantity = quantity - 100
        main_order.orderType = "LMT"
        main_order.lmtPrice = trigger_price * (1 + max_slippage if side == "BUY" else 1 - max_slippage)
        main_order.parentId = 1  # Link to monitor order
        main_order.tif = "IOC"
        
        orders.extend([monitor_order, main_order])
        return orders
    
    def guerrilla_algo(self, symbol: str, side: str, quantity: int) -> List[Order]:
        """Guerrilla algorithm - rapid fire small orders"""
        orders = []
        
        # Random small sizes
        min_size = 100
        max_size = 500
        
        remaining = quantity
        while remaining > 0:
            size = min(random.randint(min_size, max_size), remaining)
            
            order = Order()
            order.action = side
            order.totalQuantity = size
            order.orderType = "LMT"
            order.lmtPrice = self._calculate_aggressive_price(symbol, side)
            order.tif = "IOC"
            
            # Random delay
            delay = random.randint(1, 10)
            order.goodAfterTime = (datetime.now() + timedelta(seconds=delay)).strftime("%Y%m%d %H:%M:%S")
            
            orders.append(order)
            remaining -= size
        
        return orders
    
    def _calculate_participation_rate(self, quantity: int, adv: int, urgency: float) -> float:
        """Calculate optimal participation rate"""
        base_rate = quantity / adv
        return min(base_rate * (1 + urgency), 0.3)  # Cap at 30% of volume
    
    def _get_current_price(self, symbol: str) -> float:
        """Get current market price"""
        # Placeholder - would use IB API
        return 100.0
    
    def _get_volatility(self, symbol: str) -> float:
        """Get current volatility"""
        return 0.02
    
    def _get_average_daily_volume(self, symbol: str) -> int:
        """Get average daily volume"""
        return 1000000
    
    def _get_spread(self, symbol: str) -> float:
        """Get current bid-ask spread"""
        return 0.01
    
    def _is_favorable_market(self, symbol: str, side: str) -> bool:
        """Check if market conditions are favorable"""
        return np.random.random() > 0.5
    
    def _calculate_limit_price(self, symbol: str, side: str, urgency: float) -> float:
        """Calculate limit price based on urgency"""
        mid = self._get_current_price(symbol)
        spread = self._get_spread(symbol)
        
        if side == "BUY":
            return mid - spread * (1 - urgency)
        else:
            return mid + spread * (1 - urgency)
    
    def _calculate_slice_delay(self, volatility: float, participation_rate: float) -> int:
        """Calculate delay between order slices"""
        base_delay = 30  # seconds
        volatility_factor = 1 + (volatility - 0.02) * 10
        participation_factor = 1 / participation_rate
        
        return int(base_delay * volatility_factor * participation_factor)
    
    def _estimate_volume(self, symbol: str, start: datetime, end: datetime) -> int:
        """Estimate volume for time period"""
        adv = self._get_average_daily_volume(symbol)
        hours = (end - start).total_seconds() / 3600
        return int(adv * hours / 6.5)  # Assuming 6.5 hour trading day
    
    def _generate_time_slices(self, start: datetime, end: datetime, num_slices: int) -> List[datetime]:
        """Generate evenly spaced time slices"""
        duration = (end - start).total_seconds()
        interval = duration / num_slices
        
        return [start + timedelta(seconds=i * interval) for i in range(num_slices)]
    
    def _calculate_adaptive_price(self, symbol: str, side: str) -> float:
        """Calculate adaptive limit price"""
        mid = self._get_current_price(symbol)
        spread = self._get_spread(symbol)
        offset = spread * 0.25
        
        return mid - offset if side == "BUY" else mid + offset
    
    def _get_volume_curve(self, symbol: str) -> Dict[int, float]:
        """Get typical intraday volume distribution"""
        # Typical U-shaped volume curve
        return {
            9: 0.15,   # 9:30-10:00
            10: 0.08,  # 10:00-11:00
            11: 0.06,  # 11:00-12:00
            12: 0.05,  # 12:00-1:00
            13: 0.06,  # 1:00-2:00
            14: 0.08,  # 2:00-3:00
            15: 0.12,  # 3:00-4:00
        }
    
    def _create_time_buckets(self, start: datetime, end: datetime, 
                           bucket_minutes: int) -> List[Tuple[datetime, datetime]]:
        """Create time buckets for order distribution"""
        buckets = []
        current = start
        
        while current < end:
            bucket_end = min(current + timedelta(minutes=bucket_minutes), end)
            buckets.append((current, bucket_end))
            current = bucket_end
        
        return buckets
    
    def _calculate_vwap_price(self, symbol: str, side: str) -> float:
        """Calculate VWAP-targeted price"""
        # Would use actual VWAP calculation
        return self._calculate_passive_price(symbol, side)
    
    def _calculate_passive_price(self, symbol: str, side: str) -> float:
        """Calculate passive limit price"""
        bid, ask = self._get_bid_ask(symbol)
        
        if side == "BUY":
            return bid
        else:
            return ask
    
    def _calculate_arrival_limit(self, arrival_price: float, side: str,
                               volatility: float, time_elapsed: float) -> float:
        """Calculate limit price based on arrival price"""
        # Adjust for expected adverse selection
        drift = volatility * np.sqrt(time_elapsed / 3600)  # Convert to hours
        
        if side == "BUY":
            return arrival_price * (1 + drift)
        else:
            return arrival_price * (1 - drift)
    
    def _get_market_close_time(self) -> datetime:
        """Get market close time"""
        today = datetime.now().date()
        return datetime.combine(today, datetime.strptime("16:00", "%H:%M").time())
    
    def _get_midpoint_price(self, symbol: str) -> float:
        """Get midpoint price"""
        bid, ask = self._get_bid_ask(symbol)
        return (bid + ask) / 2
    
    def _calculate_aggressive_price(self, symbol: str, side: str) -> float:
        """Calculate aggressive price for immediate execution"""
        bid, ask = self._get_bid_ask(symbol)
        spread = ask - bid
        
        if side == "BUY":
            return ask + spread * 0.1  # Cross spread aggressively
        else:
            return bid - spread * 0.1
    
    def _get_bid_ask(self, symbol: str) -> Tuple[float, float]:
        """Get current bid and ask prices"""
        mid = self._get_current_price(symbol)
        spread = self._get_spread(symbol)
        
        return (mid - spread/2, mid + spread/2)
