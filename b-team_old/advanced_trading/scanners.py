"""
Market Scanner Module
=====================

Advanced market scanning capabilities
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
import re


class MarketScanner:
    """Advanced market scanning tools"""
    
    def __init__(self, ib_client):
        self.ib = ib_client
        self.scan_results = {}
        self.custom_scans = {}
        
    def scan_unusual_options_activity(self, min_volume_ratio: float = 2.0,
                                    min_oi_ratio: float = 0.25) -> pd.DataFrame:
        """Scan for unusual options activity"""
        results = []
        
        # Get all optionable symbols
        symbols = self._get_optionable_symbols()
        
        for symbol in symbols:
            options_data = self._get_options_data(symbol)
            
            for option in options_data:
                # Calculate volume to OI ratio
                if option['open_interest'] > 0:
                    volume_oi_ratio = option['volume'] / option['open_interest']
                    
                    # Check if volume is unusual
                    if (option['volume'] > option['avg_volume'] * min_volume_ratio and
                        volume_oi_ratio > min_oi_ratio):
                        
                        results.append({
                            'symbol': symbol,
                            'strike': option['strike'],
                            'expiry': option['expiry'],
                            'type': option['type'],
                            'volume': option['volume'],
                            'open_interest': option['open_interest'],
                            'volume_ratio': option['volume'] / option['avg_volume'],
                            'volume_oi_ratio': volume_oi_ratio,
                            'implied_volatility': option['iv'],
                            'delta': option['delta']
                        })
        
        return pd.DataFrame(results).sort_values('volume_ratio', ascending=False)
    
    def scan_breakouts(self, lookback_days: int = 20,
                      volume_multiplier: float = 1.5) -> pd.DataFrame:
        """Scan for price and volume breakouts"""
        results = []
        
        symbols = self._get_all_symbols()
        
        for symbol in symbols:
            try:
                data = self._get_historical_data(symbol, lookback_days)
                
                if len(data) < lookback_days:
                    continue
                
                # Current values
                current_price = data['close'].iloc[-1]
                current_volume = data['volume'].iloc[-1]
                
                # Historical metrics
                high_20d = data['high'].iloc[:-1].max()
                low_20d = data['low'].iloc[:-1].min()
                avg_volume = data['volume'].iloc[:-1].mean()
                
                # Check for breakout conditions
                is_breakout = False
                breakout_type = ""
                
                if current_price > high_20d:
                    is_breakout = True
                    breakout_type = "RESISTANCE"
                elif current_price < low_20d:
                    is_breakout = True
                    breakout_type = "SUPPORT"
                
                # Volume confirmation
                volume_surge = current_volume > avg_volume * volume_multiplier
                
                if is_breakout and volume_surge:
                    results.append({
                        'symbol': symbol,
                        'breakout_type': breakout_type,
                        'current_price': current_price,
                        'breakout_level': high_20d if breakout_type == "RESISTANCE" else low_20d,
                        'volume_ratio': current_volume / avg_volume,
                        'price_change': (current_price - data['close'].iloc[-2]) / data['close'].iloc[-2],
                        'range': high_20d - low_20d,
                        'atr': self._calculate_atr(data)
                    })
            except Exception as e:
                continue
        
        return pd.DataFrame(results).sort_values('volume_ratio', ascending=False)
    
    def scan_technical_patterns(self, patterns: List[str] = None) -> pd.DataFrame:
        """Scan for technical chart patterns"""
        if patterns is None:
            patterns = ['head_shoulders', 'double_top', 'double_bottom', 
                       'triangle', 'flag', 'wedge', 'cup_handle']
        
        results = []
        symbols = self._get_all_symbols()
        
        for symbol in symbols:
            try:
                data = self._get_historical_data(symbol, 100)
                
                for pattern in patterns:
                    if self._detect_pattern(data, pattern):
                        results.append({
                            'symbol': symbol,
                            'pattern': pattern,
                            'confidence': self._pattern_confidence(data, pattern),
                            'price': data['close'].iloc[-1],
                            'volume': data['volume'].iloc[-1],
                            'target': self._calculate_pattern_target(data, pattern)
                        })
            except:
                continue
        
        return pd.DataFrame(results).sort_values('confidence', ascending=False)
    
    def scan_fundamental_criteria(self, criteria: Dict) -> pd.DataFrame:
        """Scan based on fundamental criteria"""
        results = []
        symbols = self._get_all_symbols()
        
        for symbol in symbols:
            try:
                fundamentals = self._get_fundamentals(symbol)
                
                meets_criteria = True
                for metric, condition in criteria.items():
                    if metric in fundamentals:
                        if not self._evaluate_condition(fundamentals[metric], condition):
                            meets_criteria = False
                            break
                
                if meets_criteria:
                    results.append({
                        'symbol': symbol,
                        **fundamentals
                    })
            except:
                continue
        
        return pd.DataFrame(results)
    
    def create_custom_scanner(self, name: str, 
                            scan_function: Callable,
                            parameters: Dict = None) -> None:
        """Create a custom scanner"""
        self.custom_scans[name] = {
            'function': scan_function,
            'parameters': parameters or {}
        }
    
    def run_custom_scan(self, name: str, **kwargs) -> pd.DataFrame:
        """Run a custom scanner"""
        if name not in self.custom_scans:
            raise ValueError(f"Custom scan '{name}' not found")
        
        scan = self.custom_scans[name]
        params = {**scan['parameters'], **kwargs}
        
        return scan['function'](self, **params)
    
    def scan_correlation_opportunities(self, base_symbol: str,
                                     min_correlation: float = 0.8) -> pd.DataFrame:
        """Find correlated trading opportunities"""
        results = []
        symbols = self._get_all_symbols()
        
        base_data = self._get_historical_data(base_symbol, 60)
        base_returns = base_data['close'].pct_change().dropna()
        
        for symbol in symbols:
            if symbol == base_symbol:
                continue
            
            try:
                comp_data = self._get_historical_data(symbol, 60)
                comp_returns = comp_data['close'].pct_change().dropna()
                
                # Align the data
                aligned_returns = pd.DataFrame({
                    'base': base_returns,
                    'comp': comp_returns
                }).dropna()
                
                if len(aligned_returns) < 30:
                    continue
                
                correlation = aligned_returns.corr().iloc[0, 1]
                
                if abs(correlation) >= min_correlation:
                    # Check for divergence
                    recent_base = base_returns.iloc[-5:].mean()
                    recent_comp = comp_returns.iloc[-5:].mean()
                    historical_ratio = base_returns.mean() / comp_returns.mean()
                    current_ratio = recent_base / recent_comp
                    divergence = (current_ratio - historical_ratio) / historical_ratio
                    
                    results.append({
                        'symbol': symbol,
                        'correlation': correlation,
                        'base_symbol': base_symbol,
                        'divergence': divergence,
                        'trade_signal': 'LONG' if divergence < -0.1 else 'SHORT' if divergence > 0.1 else 'NEUTRAL'
                    })
            except:
                continue
        
        return pd.DataFrame(results).sort_values('correlation', ascending=False)
    
    def scan_momentum_stocks(self, lookback_days: int = 20,
                           min_rsi: float = 70) -> pd.DataFrame:
        """Scan for momentum stocks"""
        results = []
        symbols = self._get_all_symbols()
        
        for symbol in symbols:
            try:
                data = self._get_historical_data(symbol, lookback_days * 2)
                
                # Calculate momentum indicators
                rsi = self._calculate_rsi(data['close'])
                macd = self._calculate_macd(data['close'])
                
                # Price momentum
                returns_5d = (data['close'].iloc[-1] / data['close'].iloc[-6] - 1)
                returns_20d = (data['close'].iloc[-1] / data['close'].iloc[-21] - 1)
                
                # Volume momentum
                vol_ratio = data['volume'].iloc[-5:].mean() / data['volume'].iloc[-25:-5].mean()
                
                if rsi.iloc[-1] > min_rsi and returns_5d > 0 and returns_20d > 0:
                    results.append({
                        'symbol': symbol,
                        'rsi': rsi.iloc[-1],
                        'returns_5d': returns_5d,
                        'returns_20d': returns_20d,
                        'volume_ratio': vol_ratio,
                        'macd_signal': 'BULLISH' if macd['macd'].iloc[-1] > macd['signal'].iloc[-1] else 'BEARISH',
                        'price': data['close'].iloc[-1]
                    })
            except:
                continue
        
        return pd.DataFrame(results).sort_values('returns_5d', ascending=False)
    
    def scan_mean_reversion(self, z_score_threshold: float = 2.0) -> pd.DataFrame:
        """Scan for mean reversion opportunities"""
        results = []
        symbols = self._get_all_symbols()
        
        for symbol in symbols:
            try:
                data = self._get_historical_data(symbol, 60)
                
                # Calculate z-score from 20-day moving average
                ma20 = data['close'].rolling(20).mean()
                std20 = data['close'].rolling(20).std()
                z_score = (data['close'] - ma20) / std20
                
                current_z = z_score.iloc[-1]
                
                if abs(current_z) > z_score_threshold:
                    results.append({
                        'symbol': symbol,
                        'z_score': current_z,
                        'price': data['close'].iloc[-1],
                        'ma20': ma20.iloc[-1],
                        'distance_from_mean': (data['close'].iloc[-1] - ma20.iloc[-1]) / ma20.iloc[-1],
                        'signal': 'BUY' if current_z < -z_score_threshold else 'SELL'
                    })
            except:
                continue
        
        return pd.DataFrame(results).sort_values('z_score')
    
    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        high = data['high']
        low = data['low']
        close = data['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(period).mean()
        
        return atr.iloc[-1]
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_macd(self, prices: pd.Series) -> pd.DataFrame:
        """Calculate MACD"""
        exp1 = prices.ewm(span=12, adjust=False).mean()
        exp2 = prices.ewm(span=26, adjust=False).mean()
        macd = exp1 - exp2
        signal = macd.ewm(span=9, adjust=False).mean()
        
        return pd.DataFrame({'macd': macd, 'signal': signal})
    
    def _detect_pattern(self, data: pd.DataFrame, pattern: str) -> bool:
        """Detect specific chart pattern"""
        # Simplified pattern detection
        # Real implementation would use more sophisticated algorithms
        return np.random.random() > 0.8  # Placeholder
    
    def _pattern_confidence(self, data: pd.DataFrame, pattern: str) -> float:
        """Calculate pattern confidence score"""
        return np.random.random()  # Placeholder
    
    def _calculate_pattern_target(self, data: pd.DataFrame, pattern: str) -> float:
        """Calculate price target based on pattern"""
        return data['close'].iloc[-1] * 1.05  # Placeholder
    
    def _evaluate_condition(self, value: float, condition: Dict) -> bool:
        """Evaluate a condition"""
        operator = condition.get('operator', '>')
        threshold = condition.get('value', 0)
        
        if operator == '>':
            return value > threshold
        elif operator == '<':
            return value < threshold
        elif operator == '>=':
            return value >= threshold
        elif operator == '<=':
            return value <= threshold
        elif operator == '==':
            return value == threshold
        elif operator == 'between':
            return threshold[0] <= value <= threshold[1]
        
        return False
    
    def _get_optionable_symbols(self) -> List[str]:
        """Get list of optionable symbols"""
        # Placeholder - would query IB
        return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
    
    def _get_all_symbols(self) -> List[str]:
        """Get all tradeable symbols"""
        # Placeholder - would query IB
        return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'SPY', 'QQQ']
    
    def _get_options_data(self, symbol: str) -> List[Dict]:
        """Get options data for symbol"""
        # Placeholder
        return []
    
    def _get_historical_data(self, symbol: str, days: int) -> pd.DataFrame:
        """Get historical price data"""
        # Placeholder - would use IB API
        dates = pd.date_range(end=datetime.now(), periods=days)
        return pd.DataFrame({
            'open': np.random.randn(days).cumsum() + 100,
            'high': np.random.randn(days).cumsum() + 101,
            'low': np.random.randn(days).cumsum() + 99,
            'close': np.random.randn(days).cumsum() + 100,
            'volume': np.random.randint(1000000, 10000000, days)
        }, index=dates)
    
    def _get_fundamentals(self, symbol: str) -> Dict:
        """Get fundamental data"""
        # Placeholder
        return {
            'pe_ratio': np.random.uniform(10, 30),
            'market_cap': np.random.uniform(1e9, 1e12),
            'revenue_growth': np.random.uniform(-0.1, 0.3)
        }
