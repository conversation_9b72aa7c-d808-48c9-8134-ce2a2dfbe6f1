"""
Advanced Trading Features for IBKR TWS API
==========================================

This package implements advanced algorithmic trading capabilities including:
- Options trading automation
- Advanced order types
- Risk management tools
- Market microstructure analysis
- Scanner capabilities
- Historical data analytics
- Multi-asset class support
- Advanced execution algorithms
- Portfolio analytics
- Integration tools
"""

from .options_trading import OptionsTrader
from .advanced_orders import AdvancedOrderManager
from .risk_management import RiskManager
from .market_microstructure import MarketMicrostructureAnalyzer
from .scanners import MarketScanner
from .historical_analytics import HistoricalDataAnalyzer
from .multi_asset import MultiAssetTrader
from .execution_algos import ExecutionAlgorithms
from .portfolio_analytics import PortfolioAnalyzer
from .integrations import IntegrationManager

__all__ = [
    'OptionsTrader',
    'AdvancedOrderManager',
    'RiskManager',
    'MarketMicrostructureAnalyzer',
    'MarketScanner',
    'HistoricalDataAnalyzer',
    'MultiAssetTrader',
    'ExecutionAlgorithms',
    'PortfolioAnalyzer',
    'IntegrationManager'
]
