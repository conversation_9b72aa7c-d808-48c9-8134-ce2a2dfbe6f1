"""
Multi-Asset Trading Module
==========================

Support for trading across multiple asset classes
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from ibapi.contract import Contract
from ibapi.order import Order


class MultiAssetTrader:
    """Multi-asset class trading capabilities"""
    
    def __init__(self, ib_client):
        self.ib = ib_client
        self.asset_configs = self._initialize_asset_configs()
        
    def _initialize_asset_configs(self) -> Dict:
        """Initialize configurations for different asset classes"""
        return {
            'STOCK': {
                'contract_type': 'STK',
                'exchanges': ['SMART', 'ISLAND', 'NYSE', 'NASDAQ'],
                'min_tick': 0.01,
                'trading_hours': 'Regular'
            },
            'FOREX': {
                'contract_type': 'CASH',
                'exchanges': ['IDEALPRO'],
                'min_tick': 0.00001,
                'trading_hours': '24/5'
            },
            'FUTURES': {
                'contract_type': 'FUT',
                'exchanges': ['GLOBEX', 'ECBOT', 'NYMEX'],
                'min_tick': 'varies',
                'trading_hours': 'varies'
            },
            'OPTIONS': {
                'contract_type': 'OPT',
                'exchanges': ['SMART', 'CBOE', 'ISE'],
                'min_tick': 0.01,
                'trading_hours': 'Regular'
            },
            'CRYPTO': {
                'contract_type': 'CRYPTO',
                'exchanges': ['PAXOS'],
                'min_tick': 'varies',
                'trading_hours': '24/7'
            },
            'BONDS': {
                'contract_type': 'BOND',
                'exchanges': ['SMART', 'BONDDESK'],
                'min_tick': 'varies',
                'trading_hours': 'Regular'
            },
            'COMMODITIES': {
                'contract_type': 'CMDTY',
                'exchanges': ['NYMEX', 'COMEX', 'ICE'],
                'min_tick': 'varies',
                'trading_hours': 'varies'
            }
        }
    
    def create_forex_contract(self, base_currency: str, 
                            quote_currency: str = 'USD') -> Contract:
        """Create a forex contract"""
        contract = Contract()
        contract.symbol = base_currency
        contract.secType = 'CASH'
        contract.currency = quote_currency
        contract.exchange = 'IDEALPRO'
        return contract
    
    def create_futures_contract(self, symbol: str, expiry: str,
                              exchange: str = 'GLOBEX') -> Contract:
        """Create a futures contract"""
        contract = Contract()
        contract.symbol = symbol
        contract.secType = 'FUT'
        contract.exchange = exchange
        contract.currency = 'USD'
        contract.lastTradeDateOrContractMonth = expiry
        return contract
    
    def create_crypto_contract(self, symbol: str, 
                             currency: str = 'USD') -> Contract:
        """Create a cryptocurrency contract"""
        contract = Contract()
        contract.symbol = symbol
        contract.secType = 'CRYPTO'
        contract.exchange = 'PAXOS'
        contract.currency = currency
        return contract
    
    def create_bond_contract(self, cusip: str) -> Contract:
        """Create a bond contract"""
        contract = Contract()
        contract.cusip = cusip
        contract.secType = 'BOND'
        contract.exchange = 'SMART'
        contract.currency = 'USD'
        return contract
    
    def create_commodity_contract(self, symbol: str, 
                                secType: str = 'CMDTY',
                                exchange: str = 'NYMEX') -> Contract:
        """Create a commodity contract"""
        contract = Contract()
        contract.symbol = symbol
        contract.secType = secType
        contract.exchange = exchange
        contract.currency = 'USD'
        return contract
    
    def execute_forex_strategy(self, pairs: List[Tuple[str, str]],
                             strategy: str = 'carry_trade') -> List[Order]:
        """Execute forex trading strategies"""
        orders = []
        
        if strategy == 'carry_trade':
            # Get interest rate differentials
            for base, quote in pairs:
                rate_diff = self._get_interest_rate_differential(base, quote)
                if rate_diff > 0.02:  # 2% threshold
                    contract = self.create_forex_contract(base, quote)
                    order = self._create_forex_order('BUY', 100000)  # 1 lot
                    orders.append((contract, order))
        
        elif strategy == 'triangular_arbitrage':
            # Check for triangular arbitrage opportunities
            arb_opps = self._find_triangular_arbitrage(pairs)
            for opp in arb_opps:
                orders.extend(self._execute_triangular_arb(opp))
        
        return orders
    
    def execute_futures_spread(self, symbol: str,
                             near_month: str,
                             far_month: str,
                             spread_type: str = 'calendar') -> List[Order]:
        """Execute futures spread trades"""
        orders = []
        
        if spread_type == 'calendar':
            # Calendar spread
            near_contract = self.create_futures_contract(symbol, near_month)
            far_contract = self.create_futures_contract(symbol, far_month)
            
            # Check term structure
            if self._check_contango(symbol, near_month, far_month):
                # Sell near, buy far
                orders.append((near_contract, self._create_order('SELL', 1)))
                orders.append((far_contract, self._create_order('BUY', 1)))
        
        elif spread_type == 'inter_commodity':
            # Inter-commodity spread
            # Would implement spread between related commodities
            pass
        
        return orders
    
    def execute_crypto_arbitrage(self, symbols: List[str]) -> List[Order]:
        """Execute cryptocurrency arbitrage"""
        orders = []
        
        for symbol in symbols:
            # Check price across different exchanges
            prices = self._get_crypto_prices_multi_exchange(symbol)
            
            if prices:
                max_exchange = max(prices, key=prices.get)
                min_exchange = min(prices, key=prices.get)
                
                spread = (prices[max_exchange] - prices[min_exchange]) / prices[min_exchange]
                
                if spread > 0.002:  # 0.2% threshold
                    # Buy on cheap exchange, sell on expensive
                    buy_contract = self.create_crypto_contract(symbol)
                    sell_contract = self.create_crypto_contract(symbol)
                    
                    orders.append((buy_contract, self._create_order('BUY', 1)))
                    orders.append((sell_contract, self._create_order('SELL', 1)))
        
        return orders
    
    def create_multi_asset_portfolio(self, allocations: Dict[str, float]) -> List[Order]:
        """Create a diversified multi-asset portfolio"""
        orders = []
        total_capital = sum(allocations.values())
        
        for asset_class, allocation in allocations.items():
            weight = allocation / total_capital
            
            if asset_class == 'STOCKS':
                # Allocate to stock ETFs
                orders.extend(self._allocate_to_stocks(allocation))
            elif asset_class == 'BONDS':
                # Allocate to bond ETFs or individual bonds
                orders.extend(self._allocate_to_bonds(allocation))
            elif asset_class == 'COMMODITIES':
                # Allocate to commodity futures or ETFs
                orders.extend(self._allocate_to_commodities(allocation))
            elif asset_class == 'FOREX':
                # Allocate to forex pairs
                orders.extend(self._allocate_to_forex(allocation))
            elif asset_class == 'CRYPTO':
                # Allocate to cryptocurrencies
                orders.extend(self._allocate_to_crypto(allocation))
        
        return orders
    
    def hedge_currency_exposure(self, portfolio: Dict, 
                              target_currency: str = 'USD') -> List[Order]:
        """Hedge currency exposure in multi-asset portfolio"""
        orders = []
        currency_exposure = {}
        
        # Calculate currency exposure
        for asset, details in portfolio.items():
            currency = details.get('currency', 'USD')
            if currency != target_currency:
                exposure = details['value']
                currency_exposure[currency] = currency_exposure.get(currency, 0) + exposure
        
        # Create hedging orders
        for currency, exposure in currency_exposure.items():
            if abs(exposure) > 10000:  # Minimum hedge size
                contract = self.create_forex_contract(currency, target_currency)
                # Hedge by taking opposite position
                action = 'SELL' if exposure > 0 else 'BUY'
                quantity = abs(int(exposure / 10000)) * 10000  # Round to lots
                
                order = self._create_forex_order(action, quantity)
                orders.append((contract, order))
        
        return orders
    
    def calculate_cross_asset_correlation(self, assets: List[Dict],
                                        lookback_days: int = 60) -> pd.DataFrame:
        """Calculate correlation between different asset classes"""
        returns_data = {}
        
        for asset in assets:
            asset_type = asset['type']
            symbol = asset['symbol']
            
            # Get returns based on asset type
            if asset_type == 'STOCK':
                returns = self._get_stock_returns(symbol, lookback_days)
            elif asset_type == 'FOREX':
                returns = self._get_forex_returns(symbol, lookback_days)
            elif asset_type == 'FUTURES':
                returns = self._get_futures_returns(symbol, lookback_days)
            elif asset_type == 'CRYPTO':
                returns = self._get_crypto_returns(symbol, lookback_days)
            else:
                continue
            
            returns_data[f"{asset_type}_{symbol}"] = returns
        
        return pd.DataFrame(returns_data).corr()
    
    def optimize_multi_asset_allocation(self, assets: List[Dict],
                                      constraints: Dict = None) -> Dict:
        """Optimize allocation across multiple asset classes"""
        # Get historical returns and covariance
        returns_matrix = self._get_multi_asset_returns(assets)
        cov_matrix = returns_matrix.cov()
        mean_returns = returns_matrix.mean()
        
        # Apply optimization (simplified)
        n_assets = len(assets)
        
        if constraints is None:
            # Equal weight as default
            weights = np.array([1/n_assets] * n_assets)
        else:
            # Would implement proper optimization with constraints
            weights = self._optimize_weights(mean_returns, cov_matrix, constraints)
        
        # Calculate portfolio metrics
        portfolio_return = np.sum(mean_returns * weights) * 252
        portfolio_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix * 252, weights)))
        sharpe_ratio = portfolio_return / portfolio_vol
        
        return {
            'weights': dict(zip([a['symbol'] for a in assets], weights)),
            'expected_return': portfolio_return,
            'volatility': portfolio_vol,
            'sharpe_ratio': sharpe_ratio
        }
    
    def _get_interest_rate_differential(self, base: str, quote: str) -> float:
        """Get interest rate differential for carry trade"""
        # Placeholder - would fetch actual rates
        return np.random.uniform(-0.03, 0.05)
    
    def _find_triangular_arbitrage(self, pairs: List[Tuple[str, str]]) -> List[Dict]:
        """Find triangular arbitrage opportunities"""
        # Placeholder
        return []
    
    def _execute_triangular_arb(self, opportunity: Dict) -> List[Order]:
        """Execute triangular arbitrage"""
        return []
    
    def _check_contango(self, symbol: str, near: str, far: str) -> bool:
        """Check if futures are in contango"""
        # Placeholder
        return np.random.random() > 0.5
    
    def _get_crypto_prices_multi_exchange(self, symbol: str) -> Dict[str, float]:
        """Get crypto prices across exchanges"""
        # Placeholder
        base_price = 50000
        return {
            'PAXOS': base_price * np.random.uniform(0.998, 1.002),
            'COINBASE': base_price * np.random.uniform(0.998, 1.002),
            'BINANCE': base_price * np.random.uniform(0.998, 1.002)
        }
    
    def _create_order(self, action: str, quantity: int) -> Order:
        """Create basic order"""
        order = Order()
        order.action = action
        order.totalQuantity = quantity
        order.orderType = "MKT"
        return order
    
    def _create_forex_order(self, action: str, quantity: int) -> Order:
        """Create forex order"""
        order = Order()
        order.action = action
        order.totalQuantity = quantity
        order.orderType = "MKT"
        order.cashQty = quantity  # For forex
        return order
    
    def _allocate_to_stocks(self, amount: float) -> List[Tuple[Contract, Order]]:
        """Allocate to stock ETFs"""
        # Example allocation
        etfs = [
            ('SPY', 0.3),  # S&P 500
            ('QQQ', 0.2),  # Nasdaq
            ('IWM', 0.2),  # Russell 2000
            ('EFA', 0.2),  # International
            ('EEM', 0.1)   # Emerging Markets
        ]
        
        orders = []
        for symbol, weight in etfs:
            contract = Contract()
            contract.symbol = symbol
            contract.secType = 'STK'
            contract.exchange = 'SMART'
            contract.currency = 'USD'
            
            # Calculate shares
            shares = int((amount * weight) / 100)  # Placeholder price
            if shares > 0:
                order = self._create_order('BUY', shares)
                orders.append((contract, order))
        
        return orders
    
    def _allocate_to_bonds(self, amount: float) -> List[Tuple[Contract, Order]]:
        """Allocate to bond ETFs"""
        return []
    
    def _allocate_to_commodities(self, amount: float) -> List[Tuple[Contract, Order]]:
        """Allocate to commodities"""
        return []
    
    def _allocate_to_forex(self, amount: float) -> List[Tuple[Contract, Order]]:
        """Allocate to forex"""
        return []
    
    def _allocate_to_crypto(self, amount: float) -> List[Tuple[Contract, Order]]:
        """Allocate to crypto"""
        return []
    
    def _get_stock_returns(self, symbol: str, days: int) -> pd.Series:
        """Get stock returns"""
        return pd.Series(np.random.randn(days) * 0.02)
    
    def _get_forex_returns(self, symbol: str, days: int) -> pd.Series:
        """Get forex returns"""
        return pd.Series(np.random.randn(days) * 0.01)
    
    def _get_futures_returns(self, symbol: str, days: int) -> pd.Series:
        """Get futures returns"""
        return pd.Series(np.random.randn(days) * 0.03)
    
    def _get_crypto_returns(self, symbol: str, days: int) -> pd.Series:
        """Get crypto returns"""
        return pd.Series(np.random.randn(days) * 0.05)
    
    def _get_multi_asset_returns(self, assets: List[Dict]) -> pd.DataFrame:
        """Get returns for multiple assets"""
        returns = {}
        for asset in assets:
            if asset['type'] == 'STOCK':
                returns[asset['symbol']] = self._get_stock_returns(asset['symbol'], 252)
            # Add other asset types
        return pd.DataFrame(returns)
    
    def _optimize_weights(self, returns: pd.Series, cov: pd.DataFrame, 
                         constraints: Dict) -> np.array:
        """Optimize portfolio weights with constraints"""
        # Placeholder - would use scipy.optimize
        n = len(returns)
        return np.array([1/n] * n)
