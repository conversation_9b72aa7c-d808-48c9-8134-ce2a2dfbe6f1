"""
Advanced Order Types Module
===========================

Implements complex order types beyond basic market/limit orders
"""

from ibapi.order import Order
from ibapi.contract import Contract
from typing import List, Dict, Optional
import random


class AdvancedOrderManager:
    """Manages advanced order types"""
    
    def __init__(self, ib_client):
        self.ib = ib_client
        self.active_algos = {}
        
    def create_iceberg_order(self, action: str, totalQuantity: int, 
                           displaySize: int, price: float,
                           randomize: bool = True) -> Order:
        """Create an iceberg order with optional randomization"""
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = totalQuantity
        order.lmtPrice = price
        order.displaySize = displaySize
        
        if randomize:
            # Randomize display size within range
            order.displaySize = random.randint(
                max(100, int(displaySize * 0.8)),
                int(displaySize * 1.2)
            )
        
        return order
    
    def create_pegged_to_stock_order(self, action: str, quantity: int,
                                   peggedChangeAmount: float = 0.01) -> Order:
        """Create order pegged to stock price"""
        order = Order()
        order.action = action
        order.orderType = "PEG STK"
        order.totalQuantity = quantity
        order.auxPrice = peggedChangeAmount  # Offset from NBBO
        return order
    
    def create_conditional_order(self, parent_order: Order, 
                               condition_type: str,
                               trigger_value: float) -> Order:
        """Create conditional orders (One-Cancels-All, One-Triggers-All)"""
        order = Order()
        order.action = parent_order.action
        order.orderType = parent_order.orderType
        order.totalQuantity = parent_order.totalQuantity
        
        # Add condition based on type
        if condition_type == "PRICE_ABOVE":
            order.conditions = [self._create_price_condition(">", trigger_value)]
        elif condition_type == "PRICE_BELOW":
            order.conditions = [self._create_price_condition("<", trigger_value)]
        elif condition_type == "VOLUME_ABOVE":
            order.conditions = [self._create_volume_condition(trigger_value)]
        
        return order
    
    def create_relative_order(self, action: str, quantity: int,
                            offset: float) -> Order:
        """Create relative/pegged order"""
        order = Order()
        order.action = action
        order.orderType = "REL"
        order.totalQuantity = quantity
        order.auxPrice = offset  # Offset from NBBO
        return order
    
    def create_scale_order(self, action: str, totalQuantity: int,
                         priceAdjustValue: float, priceAdjustInterval: int,
                         priceCondition: str = "<=") -> Order:
        """Create scale order that adjusts with price movement"""
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = totalQuantity
        order.scaleInitLevelSize = totalQuantity // 5  # Initial component size
        order.scaleSubsLevelSize = totalQuantity // 10  # Subsequent components
        order.scalePriceIncrement = priceAdjustValue
        
        if priceCondition == "<=":
            order.scalePriceAdjustValue = priceAdjustValue
            order.scalePriceAdjustInterval = priceAdjustInterval
        
        return order
    
    def create_combo_order(self, legs: List[ComboLeg], action: str,
                         quantity: int, orderType: str = "MKT") -> Order:
        """Create multi-leg combo order"""
        order = Order()
        order.action = action
        order.orderType = orderType
        order.totalQuantity = quantity
        order.comboLegs = legs
        return order
    
    def create_trailing_stop_limit(self, action: str, quantity: int,
                                 trailAmount: float, trailStopPrice: float,
                                 limitOffset: float) -> Order:
        """Create trailing stop limit order"""
        order = Order()
        order.action = action
        order.orderType = "TRAIL LIMIT"
        order.totalQuantity = quantity
        order.auxPrice = trailAmount  # Trail amount
        order.trailStopPrice = trailStopPrice
        order.lmtPriceOffset = limitOffset
        return order
    
    def create_midprice_order(self, action: str, quantity: int) -> Order:
        """Create midpoint order"""
        order = Order()
        order.action = action
        order.orderType = "MIDPRICE"
        order.totalQuantity = quantity
        return order
    
    def create_snap_to_market_order(self, action: str, quantity: int,
                                  auxPrice: float) -> Order:
        """Create snap-to-market order"""
        order = Order()
        order.action = action
        order.orderType = "SNAP MKT"
        order.totalQuantity = quantity
        order.auxPrice = auxPrice  # Snap offset
        return order
    
    def create_snap_to_midpoint_order(self, action: str, quantity: int,
                                    auxPrice: float) -> Order:
        """Create snap-to-midpoint order"""
        order = Order()
        order.action = action
        order.orderType = "SNAP MID"
        order.totalQuantity = quantity
        order.auxPrice = auxPrice  # Snap offset
        return order
    
    def create_box_top_order(self, action: str, quantity: int) -> Order:
        """Create box top order for best execution"""
        order = Order()
        order.action = action
        order.orderType = "BOX TOP"
        order.totalQuantity = quantity
        return order
    
    def create_passive_relative_order(self, action: str, quantity: int,
                                    offset: float) -> Order:
        """Create passive relative order"""
        order = Order()
        order.action = action
        order.orderType = "PASSV REL"
        order.totalQuantity = quantity
        order.auxPrice = offset
        return order
    
    def _create_price_condition(self, operator: str, value: float) -> Dict:
        """Helper to create price conditions"""
        return {
            'type': 'Price',
            'operator': operator,
            'value': value
        }
    
    def _create_volume_condition(self, volume: int) -> Dict:
        """Helper to create volume conditions"""
        return {
            'type': 'Volume',
            'operator': '>',
            'value': volume
        }
    
    def create_one_cancels_all(self, orders: List[Order]) -> List[Order]:
        """Create OCA group - when one fills, others cancel"""
        oca_group = f"OCA_{random.randint(10000, 99999)}"
        for order in orders:
            order.ocaGroup = oca_group
            order.ocaType = 1  # Cancel all remaining orders with block
        return orders
    
    def create_speed_of_execution_order(self, action: str, quantity: int,
                                      urgency: str = "HIGH") -> Order:
        """Create order optimized for speed of execution"""
        order = Order()
        order.action = action
        order.orderType = "MKT"
        order.totalQuantity = quantity
        order.sweepToFill = True  # Sweep multiple venues
        order.algoStrategy = "Urgent" if urgency == "HIGH" else "Normal"
        return order


class ComboLeg:
    """Represents a leg in a combo order"""
    def __init__(self, conId: int, ratio: int, action: str, exchange: str):
        self.conId = conId
        self.ratio = ratio
        self.action = action
        self.exchange = exchange
