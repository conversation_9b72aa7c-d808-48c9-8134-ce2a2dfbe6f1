# Updated main MCP server initialization to include algorithmic trading services

# Add this section after the OrderManagementService initialization in the lifespan function:

"""
# Initialize algorithmic trading services
try:
    from ibkr_mcp_server.app.services.algo import (
        AlgorithmicTradingService,
        MarketDataStreamingService,
        PortfolioRebalancingService,
        AdvancedOrderRoutingService,
        StrategyExecutionService
    )
    
    # Create service instances
    algo_service = AlgorithmicTradingService(ibkr_service, oms_instance)
    market_data_service = MarketDataStreamingService(ibkr_service)
    rebalancing_service = PortfolioRebalancingService(ibkr_service, oms_instance, algo_service)
    routing_service = AdvancedOrderRoutingService(ibkr_service, oms_instance)
    strategy_service = StrategyExecutionService(
        ibkr_service, oms_instance, algo_service, market_data_service
    )
    
    # Initialize the tools module with services
    from ibkr_mcp_server.app.mcp.tools import algorithmic_trading_tools
    algorithmic_trading_tools.initialize_algo_services(
        ibkr_service=ibkr_service,
        oms=oms_instance,
        _algo_service=algo_service,
        _market_data_service=market_data_service,
        _rebalancing_service=rebalancing_service,
        _routing_service=routing_service,
        _strategy_service=strategy_service
    )
    
    logger.info("Algorithmic trading services initialized")
except Exception as e:
    logger.error(f"Failed to initialize algorithmic trading services: {str(e)}")
"""

# Add these tool registrations after the existing tools:

"""
# Algorithmic Trading Tools

@mcp.tool()
async def execute_algo_strategy(
    strategy_type: str,
    symbols: List[str],
    max_position_size: float,
    risk_limit: float,
    account: str,
    custom_params: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import execute_algo_strategy as func
    return await func(strategy_type, symbols, max_position_size, risk_limit, account, custom_params)

@mcp.tool()
async def subscribe_streaming_data(
    symbol: str,
    data_types: List[str],
    generic_tick_list: str = ""
) -> Dict[str, Any]:
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import subscribe_streaming_data as func
    return await func(symbol, data_types, generic_tick_list)

@mcp.tool()
async def rebalance_portfolio(
    strategy: str,
    target_allocations: List[Dict[str, Any]],
    account: str,
    rebalance_threshold: float = 0.05,
    force: bool = False
) -> Dict[str, Any]:
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import rebalance_portfolio as func
    return await func(strategy, target_allocations, account, rebalance_threshold, force)

@mcp.tool()
async def route_smart_order(
    algorithm: str,
    symbol: str,
    action: str,
    quantity: float,
    account: str,
    urgency: str = "Normal",
    custom_params: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import route_smart_order as func
    return await func(algorithm, symbol, action, quantity, account, urgency, custom_params)

@mcp.tool()
async def start_trading_strategy(
    strategy_name: str,
    strategy_id: str,
    account: str,
    symbols: List[str],
    parameters: Dict[str, Any],
    risk_limits: Dict[str, float]
) -> Dict[str, Any]:
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import start_trading_strategy as func
    return await func(strategy_name, strategy_id, account, symbols, parameters, risk_limits)

@mcp.tool()
async def optimize_portfolio(
    symbols: List[str],
    optimization_method: str = "maximum_sharpe",
    constraints: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import optimize_portfolio as func
    return await func(symbols, optimization_method, constraints)

@mcp.tool()
async def analyze_market_microstructure(
    symbol: str,
    size: int
) -> Dict[str, Any]:
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import analyze_market_microstructure as func
    return await func(symbol, size)

@mcp.tool()
async def get_active_algo_strategies() -> Dict[str, Any]:
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import get_active_algo_strategies as func
    return await func()
"""

# Add to the documentation resource:

"""
## Advanced Algorithmic Trading Tools

- **execute_algo_strategy**: Execute algorithmic trading strategies (stat arb, market making, etc.)
- **subscribe_streaming_data**: Subscribe to real-time streaming market data
- **rebalance_portfolio**: Automated portfolio rebalancing
- **route_smart_order**: Advanced order routing algorithms
- **start_trading_strategy**: Start custom trading strategies
- **optimize_portfolio**: Portfolio optimization (Sharpe, min variance, etc.)
- **analyze_market_microstructure**: Market microstructure analysis
- **get_active_algo_strategies**: Get list of active strategies
"""
