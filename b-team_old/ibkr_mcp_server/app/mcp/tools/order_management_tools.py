# /Users/<USER>/projects/b-team/ibkr_mcp_server/app/mcp/tools/order_management_tools.py
import logging
from typing import Dict, Any

# Access the global oms_instance through a getter function to avoid circular imports
def get_oms_instance():
    """Get the OrderManagementService instance from the global scope"""
    try:
        # Import here to avoid circular dependency
        import ibkr_mcp_server.app.mcp.ibkr_mcp_server as server_module
        if server_module.oms_instance is None:
            logger.warning("OrderManagementService not yet initialized. Tools may not function correctly.")
        return server_module.oms_instance
    except ImportError as e:
        logger.error(f"Error importing server module: {e}")
        return None

logger = logging.getLogger('mcp-order-tools')

# These functions are registered as tools in ibkr_mcp_server.py
# The @mcp.tool decorator is not needed here.

async def cancel_order(order_id: int) -> Dict[str, Any]:
    """
    Cancel an existing order by its ID.
    Args:
        order_id: The unique ID of the order to be cancelled.
    Returns:
        A dictionary containing the status of the cancellation request,
        the order ID, a message, and a timestamp.
        Example: {"status": "success", "order_id": 123, "message": "Order 123 cancelled successfully."}
    """
    logger.info(f"MCP Tool: cancel_order called for order_id: {order_id}")
    if not isinstance(order_id, int) or order_id <= 0:
        return {"status": "error", "message": "Invalid order_id provided. Must be a positive integer."}
    
    oms_instance = get_oms_instance()
    if oms_instance:
        return await oms_instance.cancel_order(order_id)
    else:
        logger.error("oms_instance not available in cancel_order tool.")
        return {"status": "error", "message": "Order Management Service not initialized."}

async def modify_order(order_id: int, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    Modify an existing order with new parameters.
    Args:
        order_id: The ID of the order to modify.
        parameters: A dictionary containing the order parameters to change.
                    Supported keys: 'quantity' (float), 'price' (float for LMT orders),
                    'stop_price' (float for STP or TRAIL orders).
                    Example: {"quantity": 100, "price": 150.25}
    Returns:
        A dictionary containing the status of the modification request,
        the order ID, a message, the modified parameters, and a timestamp.
    """
    logger.info(f"MCP Tool: modify_order called for order_id: {order_id} with parameters: {parameters}")
    if not isinstance(order_id, int) or order_id <= 0:
        return {"status": "error", "message": "Invalid order_id provided. Must be a positive integer."}
    if not isinstance(parameters, dict):
        return {"status": "error", "message": "Parameters must be a dictionary."}

    oms_instance = get_oms_instance()
    if oms_instance:
        return await oms_instance.modify_order(order_id, parameters)
    else:
        logger.error("oms_instance not available in modify_order tool.")
        return {"status": "error", "message": "Order Management Service not initialized."}

async def get_order_status(order_id: int) -> Dict[str, Any]:
    """
    Get the current status of a specific order by its ID.
    Args:
        order_id: The ID of the order to check.
    Returns:
        A dictionary containing detailed status information for the order,
        including status, filled quantity, remaining quantity, average fill price, etc.
    """
    logger.info(f"MCP Tool: get_order_status called for order_id: {order_id}")
    if not isinstance(order_id, int) or order_id <= 0:
        return {"status": "error", "message": "Invalid order_id provided. Must be a positive integer."}

    oms_instance = get_oms_instance()
    if oms_instance:
        return await oms_instance.get_order_status(order_id)
    else:
        logger.error("oms_instance not available in get_order_status tool.")
        return {"status": "error", "message": "Order Management Service not initialized."}

async def get_active_orders() -> Dict[str, Any]:
    """
    Retrieve a list of all currently active (open) orders.
    Returns:
        A dictionary containing a list of active orders, each with details
        such as order ID, symbol, action, quantity, status, etc.
    """
    logger.info(f"MCP Tool: get_active_orders called.")
    oms_instance = get_oms_instance()
    if oms_instance:
        return await oms_instance.get_active_orders()
    else:
        logger.error("oms_instance not available in get_active_orders tool.")
        return {"status": "error", "message": "Order Management Service not initialized."}

# Add more critical order management tools here as needed, e.g.:
# - get_order_history (might need different handling than live status)
# - get_trade_reports_for_order
