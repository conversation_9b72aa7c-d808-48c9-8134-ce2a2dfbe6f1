"""
Algorithmic Trading MCP Tools

This module provides MCP tool implementations for advanced algorithmic
trading capabilities.
"""
import logging
from typing import Dict, List, Any, Optional
from decimal import Decimal

from ibkr_mcp_server.app.services.algo import (
    AlgorithmicTradingService,
    StrategyType,
    StrategyParameters,
    MarketDataStreamingService,
    DataType,
    PortfolioRebalancingService,
    RebalancingStrategy,
    RebalancingConfig,
    AssetAllocation,
    AdvancedOrderRoutingService,
    RoutingAlgorithm,
    RoutingConfig,
    StrategyExecutionService
)

logger = logging.getLogger('algo-trading-tools')

# Global service instances (initialized by main MCP server)
algo_service: Optional[AlgorithmicTradingService] = None
market_data_service: Optional[MarketDataStreamingService] = None
rebalancing_service: Optional[PortfolioRebalancingService] = None
routing_service: Optional[AdvancedOrderRoutingService] = None
strategy_service: Optional[StrategyExecutionService] = None


# Algorithmic Trading Tools

async def execute_algo_strategy(
    strategy_type: str,
    symbols: List[str],
    max_position_size: float,
    risk_limit: float,
    account: str,
    custom_params: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Execute an algorithmic trading strategy
    
    Available strategies:
    - statistical_arbitrage: Exploit pricing inefficiencies between related securities
    - market_making: Provide liquidity with bid/ask spreads
    - trend_following: Trade in direction of established trends
    - mean_reversion: Trade expecting price to revert to mean
    - pairs_trading: Trade correlated pairs
    - momentum: Trade based on price momentum
    - vwap: Volume Weighted Average Price execution
    - twap: Time Weighted Average Price execution
    - smart_order_routing: Intelligent order routing
    - iceberg: Hidden size orders
    
    Args:
        strategy_type: Type of strategy to execute
        symbols: List of symbols to trade
        max_position_size: Maximum position size in dollars
        risk_limit: Maximum risk/loss limit
        account: Account ID
        custom_params: Strategy-specific parameters
    
    Returns:
        Strategy execution result
    """
    if not algo_service:
        return {"status": "error", "message": "Algorithmic trading service not initialized"}
    
    try:
        # Create strategy parameters
        strategy = StrategyType(strategy_type)
        params = StrategyParameters(
            strategy_type=strategy,
            symbols=symbols,
            max_position_size=Decimal(str(max_position_size)),
            risk_limit=Decimal(str(risk_limit)),
            account=account,
            custom_params=custom_params or {}
        )
        
        # Execute strategy
        result = await algo_service.execute_strategy(params)
        
        return {
            "status": "success",
            "strategy_id": result.strategy_id,
            "orders_placed": result.orders_placed,
            "pnl": float(result.pnl) if result.pnl else None,
            "metrics": result.metrics,
            "errors": result.errors
        }
        
    except Exception as e:
        logger.error(f"Failed to execute algo strategy: {str(e)}")
        return {"status": "error", "message": str(e)}


async def stop_algo_strategy(strategy_id: str) -> Dict[str, Any]:
    """
    Stop a running algorithmic trading strategy
    
    Args:
        strategy_id: ID of the strategy to stop
    
    Returns:
        Stop result
    """
    if not algo_service:
        return {"status": "error", "message": "Algorithmic trading service not initialized"}
    
    try:
        result = await algo_service.stop_strategy(strategy_id)
        return result
    except Exception as e:
        logger.error(f"Failed to stop strategy: {str(e)}")
        return {"status": "error", "message": str(e)}


# Market Data Streaming Tools

async def subscribe_streaming_data(
    symbol: str,
    data_types: List[str],
    generic_tick_list: str = ""
) -> Dict[str, Any]:
    """
    Subscribe to streaming market data
    
    Data types:
    - TRADES: Trade data
    - BID_ASK: Bid/ask quotes
    - MID_POINT: Midpoint prices
    - HISTORICAL: Historical data with updates
    - OPTION_COMPUTATION: Option calculations
    - REAL_TIME_BARS: 5-second bars
    - TICK_BY_TICK: Tick-by-tick data
    
    Args:
        symbol: Symbol to subscribe to
        data_types: List of data types
        generic_tick_list: IB generic tick list
    
    Returns:
        Subscription details
    """
    if not market_data_service:
        return {"status": "error", "message": "Market data service not initialized"}
    
    try:
        # Convert string data types to enum
        data_type_enums = [DataType(dt) for dt in data_types]
        
        result = await market_data_service.subscribe_market_data(
            symbol=symbol,
            data_types=data_type_enums,
            generic_tick_list=generic_tick_list
        )
        
        return result
    except Exception as e:
        logger.error(f"Failed to subscribe to market data: {str(e)}")
        return {"status": "error", "message": str(e)}


async def unsubscribe_streaming_data(subscription_id: str) -> Dict[str, Any]:
    """
    Unsubscribe from streaming market data
    
    Args:
        subscription_id: Subscription ID to cancel
    
    Returns:
        Unsubscribe result
    """
    if not market_data_service:
        return {"status": "error", "message": "Market data service not initialized"}
    
    try:
        result = await market_data_service.unsubscribe_market_data(subscription_id)
        return result
    except Exception as e:
        logger.error(f"Failed to unsubscribe: {str(e)}")
        return {"status": "error", "message": str(e)}


async def get_market_depth(symbol: str, num_rows: int = 5) -> Dict[str, Any]:
    """
    Get Level II market depth data
    
    Args:
        symbol: Symbol to get depth for
        num_rows: Number of depth levels (default: 5)
    
    Returns:
        Market depth data
    """
    if not market_data_service:
        return {"status": "error", "message": "Market data service not initialized"}
    
    try:
        result = await market_data_service.get_market_depth(symbol, num_rows)
        return result
    except Exception as e:
        logger.error(f"Failed to get market depth: {str(e)}")
        return {"status": "error", "message": str(e)}


# Portfolio Rebalancing Tools

async def rebalance_portfolio(
    strategy: str,
    target_allocations: List[Dict[str, Any]],
    account: str,
    rebalance_threshold: float = 0.05,
    force: bool = False
) -> Dict[str, Any]:
    """
    Rebalance portfolio using specified strategy
    
    Strategies:
    - calendar: Time-based rebalancing
    - threshold: Deviation-based rebalancing
    - tactical: Market conditions-based
    - risk_parity: Equal risk contribution
    - minimum_variance: Minimize portfolio variance
    - maximum_sharpe: Maximize Sharpe ratio
    
    Args:
        strategy: Rebalancing strategy
        target_allocations: List of target allocations
        account: Account ID
        rebalance_threshold: Threshold for rebalancing (default: 5%)
        force: Force rebalancing even if within threshold
    
    Returns:
        Rebalancing result
    """
    if not rebalancing_service:
        return {"status": "error", "message": "Rebalancing service not initialized"}
    
    try:
        # Convert allocations
        allocations = []
        for alloc in target_allocations:
            allocations.append(AssetAllocation(
                symbol=alloc["symbol"],
                target_weight=Decimal(str(alloc["weight"])),
                min_weight=Decimal(str(alloc.get("min_weight", 0))) if "min_weight" in alloc else None,
                max_weight=Decimal(str(alloc.get("max_weight", 1))) if "max_weight" in alloc else None
            ))
        
        # Create config
        config = RebalancingConfig(
            strategy=RebalancingStrategy(strategy),
            target_allocations=allocations,
            account=account,
            rebalance_threshold=Decimal(str(rebalance_threshold))
        )
        
        # Execute rebalancing
        result = await rebalancing_service.rebalance_portfolio(config, force)
        
        return {
            "status": "success",
            "trades_executed": result.trades_executed,
            "pre_weights": {k: float(v) for k, v in result.pre_rebalance_weights.items()},
            "post_weights": {k: float(v) for k, v in result.post_rebalance_weights.items()},
            "total_value": float(result.total_value),
            "transaction_costs": float(result.transaction_costs),
            "metrics": result.metrics
        }
        
    except Exception as e:
        logger.error(f"Failed to rebalance portfolio: {str(e)}")
        return {"status": "error", "message": str(e)}


async def optimize_portfolio(
    symbols: List[str],
    optimization_method: str = "maximum_sharpe",
    constraints: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Optimize portfolio allocation
    
    Methods:
    - maximum_sharpe: Maximize Sharpe ratio
    - minimum_variance: Minimize portfolio variance
    - risk_parity: Equal risk contribution
    
    Args:
        symbols: List of symbols to include
        optimization_method: Optimization method
        constraints: Portfolio constraints
    
    Returns:
        Optimal allocations
    """
    if not rebalancing_service:
        return {"status": "error", "message": "Rebalancing service not initialized"}
    
    try:
        result = await rebalancing_service.optimize_portfolio(
            symbols, optimization_method, constraints
        )
        return result
    except Exception as e:
        logger.error(f"Failed to optimize portfolio: {str(e)}")
        return {"status": "error", "message": str(e)}


# Advanced Order Routing Tools

async def route_smart_order(
    algorithm: str,
    symbol: str,
    action: str,
    quantity: float,
    account: str,
    urgency: str = "Normal",
    custom_params: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Route order using advanced algorithms
    
    Algorithms:
    - smart: IB's Smart routing
    - directed: Direct to specific exchange
    - dark_pool: Dark pool seeking
    - sweep: Sweep multiple venues
    - midpoint: Midpoint matching
    - pegged: Pegged orders
    - liquidity_seeking: Seek liquidity
    - cost_aware: Minimize total cost
    
    Args:
        algorithm: Routing algorithm
        symbol: Symbol to trade
        action: BUY or SELL
        quantity: Order quantity
        account: Account ID
        urgency: Urgent, Normal, or Patient
        custom_params: Algorithm-specific parameters
    
    Returns:
        Routing result
    """
    if not routing_service:
        return {"status": "error", "message": "Routing service not initialized"}
    
    try:
        # Create routing config
        config = RoutingConfig(
            algorithm=RoutingAlgorithm(algorithm),
            symbol=symbol,
            action=action,
            quantity=Decimal(str(quantity)),
            account=account,
            urgency=urgency,
            custom_params=custom_params or {}
        )
        
        # Execute routing
        result = await routing_service.route_order(config)
        
        return {
            "status": "success",
            "order_id": result.order_id,
            "algorithm": result.routing_algorithm.value,
            "exchanges_used": result.exchanges_used,
            "avg_price": float(result.avg_price),
            "slippage": float(result.slippage),
            "execution_time": str(result.execution_time),
            "metrics": result.metrics
        }
        
    except Exception as e:
        logger.error(f"Failed to route order: {str(e)}")
        return {"status": "error", "message": str(e)}


async def analyze_market_microstructure(
    symbol: str,
    size: int
) -> Dict[str, Any]:
    """
    Analyze market microstructure for optimal routing
    
    Args:
        symbol: Symbol to analyze
        size: Order size
    
    Returns:
        Market analysis
    """
    if not routing_service:
        return {"status": "error", "message": "Routing service not initialized"}
    
    try:
        result = await routing_service.analyze_market_microstructure(symbol, size)
        return result
    except Exception as e:
        logger.error(f"Failed to analyze market: {str(e)}")
        return {"status": "error", "message": str(e)}


# Strategy Execution Tools

async def start_trading_strategy(
    strategy_name: str,
    strategy_id: str,
    account: str,
    symbols: List[str],
    parameters: Dict[str, Any],
    risk_limits: Dict[str, float]
) -> Dict[str, Any]:
    """
    Start a trading strategy
    
    Built-in strategies:
    - simple_momentum: Momentum trading
    - mean_reversion: Mean reversion trading
    - pairs_trading: Pairs trading
    
    Args:
        strategy_name: Name of strategy
        strategy_id: Unique strategy ID
        account: Account ID
        symbols: Symbols to trade
        parameters: Strategy parameters
        risk_limits: Risk limits (max_position_size, max_loss, max_orders)
    
    Returns:
        Start result
    """
    if not strategy_service:
        return {"status": "error", "message": "Strategy service not initialized"}
    
    try:
        # Convert risk limits
        risk_limits_decimal = {
            k: Decimal(str(v)) for k, v in risk_limits.items()
        }
        
        result = await strategy_service.start_strategy(
            strategy_name=strategy_name,
            strategy_id=strategy_id,
            account=account,
            symbols=symbols,
            parameters=parameters,
            risk_limits=risk_limits_decimal
        )
        
        return result
    except Exception as e:
        logger.error(f"Failed to start strategy: {str(e)}")
        return {"status": "error", "message": str(e)}


async def stop_trading_strategy(strategy_id: str) -> Dict[str, Any]:
    """
    Stop a running trading strategy
    
    Args:
        strategy_id: Strategy ID to stop
    
    Returns:
        Stop result with performance
    """
    if not strategy_service:
        return {"status": "error", "message": "Strategy service not initialized"}
    
    try:
        result = await strategy_service.stop_strategy(strategy_id)
        return result
    except Exception as e:
        logger.error(f"Failed to stop strategy: {str(e)}")
        return {"status": "error", "message": str(e)}


async def get_strategy_status(strategy_id: str) -> Dict[str, Any]:
    """
    Get strategy status and performance
    
    Args:
        strategy_id: Strategy ID
    
    Returns:
        Strategy status
    """
    if not strategy_service:
        return {"status": "error", "message": "Strategy service not initialized"}
    
    try:
        result = await strategy_service.get_strategy_status(strategy_id)
        return result
    except Exception as e:
        logger.error(f"Failed to get strategy status: {str(e)}")
        return {"status": "error", "message": str(e)}


async def get_active_algo_strategies() -> Dict[str, Any]:
    """
    Get list of all active algorithmic strategies
    
    Returns:
        List of active strategies
    """
    try:
        strategies = []
        
        # Get algo trading strategies
        if algo_service:
            algo_strategies = await algo_service.get_active_strategies()
            strategies.extend([
                {"type": "algorithmic", **s} for s in algo_strategies
            ])
        
        # Get execution strategies
        if strategy_service:
            exec_strategies = await strategy_service.get_active_strategies()
            strategies.extend([
                {"type": "execution", **s} for s in exec_strategies
            ])
        
        return {
            "status": "success",
            "strategies": strategies,
            "count": len(strategies)
        }
        
    except Exception as e:
        logger.error(f"Failed to get active strategies: {str(e)}")
        return {"status": "error", "message": str(e)}


# Service initialization function
def initialize_algo_services(
    ibkr_service,
    oms,
    _algo_service=None,
    _market_data_service=None,
    _rebalancing_service=None,
    _routing_service=None,
    _strategy_service=None
):
    """Initialize algorithmic trading services"""
    global algo_service, market_data_service, rebalancing_service
    global routing_service, strategy_service
    
    algo_service = _algo_service
    market_data_service = _market_data_service
    rebalancing_service = _rebalancing_service
    routing_service = _routing_service
    strategy_service = _strategy_service
    
    logger.info("Algorithmic trading services initialized")
