"""
Interactive Brokers MCP Server

This server implements the Model Context Protocol (MCP) to provide
Claude Desktop with access to Interactive Brokers' TWS API through
the existing IBKRService.
"""
from mcp.server.fastmcp import FastMCP, Context, Image
import asyncio
import os
import matplotlib.pyplot as plt
import io
import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Union, Any
import json
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from contextlib import asynccontextmanager

# Import the existing service
from ibkr_mcp_server.app.services.ibkr_service import (
    ibkr_service,  # Import the singleton instead of the class
    IBKRConnectionError,
    IBKRTimeoutError,
    IBKRDataError,
    IBKROrderError
)

# Import the order management service
from ibkr_mcp_server.app.services.order_management_service import OrderManagementService # Import the class

# Import algorithmic trading services
from ibkr_mcp_server.app.services.algo import (
    AlgorithmicTradingService,
    MarketDataStreamingService,
    PortfolioRebalancingService,
    AdvancedOrderRoutingService,
    StrategyExecutionService
)


# Configure logging with standardized timestamp format
# Logging is configured by setup_logging() later in the file
logger = logging.getLogger('ibkr-mcp')

# Server lifespan management
@asynccontextmanager
async def lifespan(server):
    """
    Manage the server lifespan
    """
    global oms_instance, algo_service, market_data_service, rebalancing_service, routing_service, strategy_service
    # Startup
    logger.info("IBKR MCP Server starting up")

    # Initialize the service if needed
    try:
        # Initialize the service
        await ibkr_service.initialize()
        logger.info("IBKR Service initialized")

        # Initialize OrderManagementService and link it to IBKRService
        oms_instance = OrderManagementService(ibkr_svc=ibkr_service)
        ibkr_service.set_order_management_service(oms_delegate=oms_instance)
        logger.info("OrderManagementService initialized and linked to IBKRService")

        # Initialize algorithmic trading services
        try:
            # Create service instances
            algo_service = AlgorithmicTradingService(ibkr_service, oms_instance)
            market_data_service = MarketDataStreamingService(ibkr_service)
            rebalancing_service = PortfolioRebalancingService(ibkr_service, oms_instance, algo_service)
            routing_service = AdvancedOrderRoutingService(ibkr_service, oms_instance)
            strategy_service = StrategyExecutionService(
                ibkr_service, oms_instance, algo_service, market_data_service
            )
            
            # Initialize the tools module with services
            from ibkr_mcp_server.app.mcp.tools import algorithmic_trading_tools
            algorithmic_trading_tools.initialize_algo_services(
                ibkr_service=ibkr_service,
                oms=oms_instance,
                _algo_service=algo_service,
                _market_data_service=market_data_service,
                _rebalancing_service=rebalancing_service,
                _routing_service=routing_service,
                _strategy_service=strategy_service
            )
            
            logger.info("Algorithmic trading services initialized")
        except Exception as e:
            logger.error(f"Failed to initialize algorithmic trading services: {str(e)}")
            # Don't re-raise to allow server to start anyway

        # Log server information - removed attribute access that causes errors
        logger.info("IBKR MCP Server initialization complete")

    except Exception as e:
        logger.error(f"Failed to initialize services: {str(e)}")
        # Don't re-raise the exception to allow the server to start anyway

    yield

    # Shutdown
    logger.info("IBKR MCP Server shutting down")

    # Disconnect from TWS if connected
    try:
        if ibkr_service.connected:
            logger.info("Disconnecting from TWS during shutdown")
            await ibkr_service.disconnect()
            logger.info("Successfully disconnected from TWS")
        else:
            logger.info("No active TWS connection to disconnect")

        # Perform any additional cleanup
        logger.info("Cleaning up resources")

        # Close any open matplotlib figures
        try:
            plt.close('all')
        except:
            pass

        logger.info("Cleanup completed")
    except Exception as e: # Catch specific exceptions if possible
        logger.error(f"Error during server shutdown: {str(e)}")

# Define startup and shutdown functions for compatibility with mcp_server_main.py
async def on_startup():
    """Compatibility function for mcp_server_main.py"""
    logger.info("on_startup called")
    # This is handled by the lifespan manager

async def on_shutdown():
    """Compatibility function for mcp_server_main.py"""
    logger.info("on_shutdown called")
    # This is handled by the lifespan manager

# --- Import Tools ---
# Order management tools will be imported locally to avoid naming conflicts

# Create MCP server
mcp = FastMCP(
    "IBKR Trading Assistant",
    dependencies=["ib_async", "matplotlib", "tenacity", "numpy"],
    version="2.0.0",  # Major version bump for algorithmic trading capabilities
    lifespan=lifespan  # Explicitly set the lifespan function
)

# Global OrderManagementService instance, to be initialized in lifespan
oms_instance: Optional[OrderManagementService] = None

# Global algorithmic trading service instances
algo_service: Optional[AlgorithmicTradingService] = None
market_data_service: Optional[MarketDataStreamingService] = None
rebalancing_service: Optional[PortfolioRebalancingService] = None
routing_service: Optional[AdvancedOrderRoutingService] = None
strategy_service: Optional[StrategyExecutionService] = None


# Implement a response caching mechanism to reduce duplicate calls
class ResponseCache:
    def __init__(self, max_size: int = 20, ttl_seconds: int = 60):
        self.cache = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        
    def get(self, key: str):
        """Get cached value if it exists and is not expired"""
        if key in self.cache:
            entry = self.cache[key]
            if (datetime.now(timezone.utc) - entry["timestamp"]).total_seconds() < self.ttl_seconds:
                logger.debug(f"Cache hit for {key}")
                return entry["value"]
            else:
                # Remove expired entry
                del self.cache[key]
        return None
        
    def set(self, key: str, value):
        """Set cache value with current timestamp"""
        # If cache is full, remove oldest entry
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k]["timestamp"])
            del self.cache[oldest_key]
            
        self.cache[key] = {
            "value": value,
            "timestamp": datetime.now(timezone.utc)
        }
        logger.debug(f"Cache set for {key}")

# Create global caches
tools_cache = ResponseCache()
resources_cache = ResponseCache()

# Connection tool
@mcp.tool()
async def connect_to_tws(host: str = "127.0.0.1", port: int = 7496, client_id: int = 0) -> Dict:
    """
    Connect to Interactive Brokers TWS or Gateway

    Args:
        host: The hostname or IP address of the TWS/Gateway (default: 127.0.0.1)
        port: The port number (7496 for live trading, 7497 for paper trading)
        client_id: A unique client ID (default: 0)

    Returns:
        A dictionary with connection status
    """
    
    if port is None:
        logger.error("Port is required but not provided")
        return {"status": "error", "message": "Port is required (7496 for live, 7497 for paper)"}
        
    try:
        # Override service connection parameters
        ibkr_service.host = host
        ibkr_service.port = port # Port for the connection
        ibkr_service.client_id = client_id

        # Connect using the service
        logger.info(f"Attempting to connect to TWS at {host}:{port} with client ID {client_id}")

        # Attempt connection via the service
        try:
            # The client_id is set on ibkr_service instance above,
            # ibkr_service.connect() will use these instance attributes.
            # The readonly flag can be set here if needed, e.g., readonly=False for trading.
            await ibkr_service.connect(readonly=False) # Example: connect for trading
        except Exception as e: # Catch specific connection errors if ibkr_service.connect raises them
            logger.error(f"Connection attempt failed: {e}")
            return {"status": "error", "message": str(e)}
        
        return {
            "status": "connected",
            "message": f"Connected to TWS at {host}:{port}",
            # Fetch connection_time after successful connection
            "connectionTime": ibkr_service.connection_time.strftime("%Y-%m-%d %H:%M:%S") if ibkr_service.connection_time else "Unknown",
            "clientId": client_id
        }
    except IBKRTimeoutError as e:
        logger.error(f"Connection timeout: {str(e)}")
        return {
            "status": "error",
            "message": f"Connection timeout: {str(e)}",
            "errorType": "timeout",
            "recommendation": "Check if TWS/Gateway is running and accepting connections"
        }
    except IBKRConnectionError as e:
        logger.error(f"Connection error: {str(e)}")
        return {
            "status": "error",
            "message": f"Connection error: {str(e)}",
            "errorType": "connection",
            "recommendation": "Verify TWS/Gateway is running and properly configured"
        }
    except Exception as e:
        logger.error(f"Unexpected error during connection: {str(e)}")
        return {
            "status": "error",
            "message": f"Failed to connect: {str(e)}",
            "errorType": "unknown"
        }

# Portfolio tool
@mcp.tool()
async def get_portfolio() -> Dict: # Removed account: str = None as it's not used by ib_async positions()
    """
    Get the current portfolio positions

    Returns:
        A dictionary with portfolio positions
    """
    try:            
        # Check cache first
        cache_key = "portfolio"
        cached_result = tools_cache.get(cache_key)
        if cached_result:
            return cached_result

        # Ensure connection with proper error handling
        # Get portfolio data using the service
        logger.info("Fetching portfolio data")
        portfolio_data = await ibkr_service.get_portfolio()

        # Add timestamp to the response
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        result = {
            "status": "success",
            "data": portfolio_data,
            "timestamp": timestamp,
            "count": len(portfolio_data)
        }
        
        # Cache the result
        tools_cache.set(cache_key, result)
        
        return result
    except IBKRConnectionError as e:
        logger.error(f"Connection error while getting portfolio: {str(e)}")
        return {
            "status": "error",
            "errorType": "connection",
            "message": f"Connection error: {str(e)}",
            "recommendation": "Please reconnect to TWS/Gateway"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while getting portfolio: {str(e)}")
        return {
            "status": "error",
            "errorType": "timeout",
            "message": f"Timeout error: {str(e)}",
            "recommendation": "Check if TWS/Gateway is responsive"
        }
    except IBKRDataError as e:
        logger.error(f"Data error while getting portfolio: {str(e)}")
        return {
            "status": "error",
            "errorType": "data",
            "message": f"Data error: {str(e)}",
            "recommendation": "Verify account has positions and permissions"
        }
    except Exception as e:
        logger.error(f"Unexpected error while getting portfolio: {str(e)}")
        return {
            "status": "error",
            "errorType": "unknown",
            "message": f"Failed to get portfolio: {str(e)}"
        }

# Market data tool
@mcp.tool()
async def get_market_data(symbol: str, exchange: str = "SMART") -> Dict:
    """
    Get market data for a symbol

    Args:
        symbol: The ticker symbol
        exchange: The exchange (default: SMART)

    Returns:
        A dictionary with market data
    """
    if not symbol:
        logger.error("Symbol is required but not provided")
        return {
            "status": "error",
            "message": "Symbol is required"
        }

    try:            
        # Check cache first
        cache_key = f"market_data_{symbol}_{exchange}"
        cached_result = tools_cache.get(cache_key)
        if cached_result:
            return cached_result

        # Ensure connection with proper error handling
        # Get market data using the service
        logger.info(f"Fetching market data for {symbol} on {exchange}")
        market_data = await ibkr_service.get_market_data(symbol, exchange)

        # Add timestamp to the response
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        result = {
            "status": "success",
            "data": market_data,
            "symbol": symbol,
            "exchange": exchange,
            "timestamp": timestamp
        }
        
        # Cache the result
        tools_cache.set(cache_key, result)
        
        return result
    except IBKRConnectionError as e:
        logger.error(f"Connection error while getting market data for {symbol}: {str(e)}")
        return {
            "status": "error",
            "errorType": "connection",
            "message": f"Connection error: {str(e)}",
            "recommendation": "Please reconnect to TWS/Gateway"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while getting market data for {symbol}: {str(e)}")
        return {
            "status": "error",
            "errorType": "timeout",
            "message": f"Timeout error: {str(e)}",
            "recommendation": "Check if TWS/Gateway is responsive"
        }
    except IBKRDataError as e:
        logger.error(f"Data error while getting market data for {symbol}: {str(e)}")
        return {
            "status": "error",
            "errorType": "data",
            "message": f"Data error: {str(e)}",
            "recommendation": "Verify symbol exists and you have market data permissions"
        }
    except Exception as e:
        logger.error(f"Unexpected error while getting market data for {symbol}: {str(e)}")
        return {
            "status": "error",
            "errorType": "unknown",
            "message": f"Failed to get market data: {str(e)}"
        }

# Add a prompt for Claude Desktop
@mcp.prompt()
def ibkr_trading_prompt() -> str:
    """
    Prompt for interacting with Interactive Brokers
    """
    return """
    You are an advanced Interactive Brokers trading assistant with algorithmic trading capabilities. You can help with:

    1. Connecting to TWS or IB Gateway
    2. Retrieving portfolio information and market data
    3. Analyzing trading opportunities and market microstructure
    4. Placing various types of orders (market, limit, bracket, etc.)
    5. Executing algorithmic trading strategies:
       - Statistical arbitrage
       - Market making
       - Trend following
       - Mean reversion
       - Pairs trading
       - Momentum strategies
       - VWAP/TWAP execution
       - Smart order routing
    6. Portfolio optimization and rebalancing
    7. Real-time market data streaming
    8. Level II market depth analysis

    To get started, help the user connect to TWS using the connect_to_tws tool.
    For paper trading, use port 7497. For live trading, use port 7496.

    Always provide clear explanations of market data and portfolio information.
    When discussing orders or strategies, explain their implications and risks.
    """

# Advanced order types based on Orders.md
@mcp.tool()
async def place_auction_order(symbol: str, action: str, quantity: float, price: float, account: str, exchange: str = "SMART") -> Dict:
    """ # Added account parameter
    Place an Auction order

    An Auction order is entered during pre-market for execution at the Calculated Opening Price (COP).

    Args:
        symbol: The ticker symbol
        action: "BUY" or "SELL"
        quantity: Number of shares/contracts
        price: Limit price
        account: The account ID to place the order in
        exchange: Exchange (default: SMART)

    Returns:
        Order status information
    """
    try:
        # Create an auction order using our service method (Corrected service method call)
        logger.info(f"Placing auction order for {symbol}: {action} {quantity} @ {price} in account {account}") # Corrected log message
        result = await ibkr_service.place_auction_order( # Corrected service method call
            symbol=symbol,
            action=action,
            quantity=quantity,
            price=price,
            account=account, # Pass account
            exchange=exchange
        )
        
        return result
    except IBKRConnectionError as e:
        logger.error(f"Connection error while placing auction order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Connection error: {str(e)}", "errorType": "connection"}
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while placing auction order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Timeout error: {str(e)}", "errorType": "timeout"}
    except IBKROrderError as e:
        logger.error(f"Order error while placing auction order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Order error: {str(e)}", "errorType": "order"}
    except Exception as e:
        logger.error(f"Unexpected error while placing auction order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Failed to place auction order: {str(e)}"}

@mcp.tool()
async def place_adaptive_order(symbol: str, action: str, quantity: float, price: float, account: str, priority: str = "Normal", exchange: str = "SMART") -> Dict:
    print("=== METHOD CALLED!!! ===")
    """
    Place an Adaptive Algorithm order

    The Adaptive Algo combines IB's Smartrouting with user-defined priority settings
    for better execution prices on average than regular limit or market orders.

    Args:
        symbol: The ticker symbol
        action: "BUY" or "SELL"
        quantity: Number of shares/contracts
        price: Limit price
        account: The account ID to place the order in
        priority: Priority setting ("Urgent", "Normal", or "Patient")
        exchange: Exchange (default: SMART)

    Returns:
        Order status information
    """
    try:        
        # Create an adaptive order using our service method
        logger.info(f"Placing adaptive order for {symbol}: {action} {quantity} @ {price} with {priority} priority in account {account}")
        result = await ibkr_service.place_adaptive_order(
            symbol=symbol,
            action=action,
            quantity=quantity,
            price=price,
            exchange=exchange,
            account=account # Pass account
        )
        
        return result
    except IBKRConnectionError as e:
        logger.error(f"Connection error while placing adaptive order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Connection error: {str(e)}", "errorType": "connection"}
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while placing adaptive order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Timeout error: {str(e)}", "errorType": "timeout"}
    except IBKROrderError as e:
        logger.error(f"Order error while placing adaptive order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Order error: {str(e)}", "errorType": "order"}
    except Exception as e:
        logger.error(f"Unexpected error while placing adaptive order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Failed to place adaptive order: {str(e)}"}

@mcp.tool()
async def place_bracket_order(symbol: str, action: str, quantity: float, entry_price: float, # Added account parameter
                             profit_price: float, stop_price: float, account: str, exchange: str = "SMART") -> Dict:
    """
    Place a Bracket Order

    Bracket Orders help limit loss and lock in profit by "bracketing" an order with
    two opposite-side orders. A BUY order is bracketed by a high-side sell limit order
    and a low-side sell stop order.

    Args:
        symbol: The ticker symbol
        action: "BUY" or "SELL"
        quantity: Number of shares/contracts
        entry_price: Entry order price
        profit_price: Profit target price
        stop_price: Stop loss price
        account: The account ID to place the order in
        exchange: Exchange (default: SMART)

    Returns:
        Order status information
    """
    try:        
        # Create a bracket order using our service method
        logger.info(f"Placing bracket order for {symbol}: {action} {quantity} @ {entry_price} with profit at {profit_price} and stop at {stop_price}")
        result = await ibkr_service.place_bracket_order(
            symbol=symbol,
            action=action,
            quantity=quantity,
            entry_price=entry_price,
            profit_price=profit_price,
            stop_price=stop_price,
            exchange=exchange,
            account=account # Pass account
        )
        
        return result
    except IBKRConnectionError as e:
        logger.error(f"Connection error while placing bracket order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Connection error: {str(e)}", "errorType": "connection"}
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while placing bracket order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Timeout error: {str(e)}", "errorType": "timeout"}
    except IBKROrderError as e:
        logger.error(f"Order error while placing bracket order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Order error: {str(e)}", "errorType": "order"}
    except Exception as e:
        logger.error(f"Unexpected error while placing bracket order for {symbol}: {str(e)}")
        return {"status": "error", "message": f"Failed to place bracket order: {str(e)}"}

@mcp.tool()
async def get_account_summary(tags: List[str] = None) -> Dict: # Removed account: str = None as it's not used by ib_async accountSummaryAsync()
    """
    Get account summary information

    Args:
        tags: List of account tags to retrieve (default: all tags)
    Returns:
        Account summary information
    """
    try:            
        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info("Not connected to TWS, attempting to connect for account summary request")
            await ibkr_service.connect()
            # Allow time for connection to fully establish
            await asyncio.sleep(1)

        # Get account summary using the service
        logger.info(f"Fetching account summary with tags: {tags}")
        result = await ibkr_service.get_account_summary(tags)
        
        response = {
            "status": "success",
            "data": result,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return response
    except IBKRConnectionError as e:
        logger.error(f"Connection error while getting account summary: {str(e)}")
        return {"status": "error", "message": f"Connection error: {str(e)}", "errorType": "connection"}
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while getting account summary: {str(e)}")
        return {"status": "error", "message": f"Timeout error: {str(e)}", "errorType": "timeout"}
    except IBKRDataError as e:
        logger.error(f"Data error while getting account summary: {str(e)}")
        return {"status": "error", "message": f"Data error: {str(e)}", "errorType": "data"}
    except Exception as e:
        logger.error(f"Unexpected error while getting account summary: {str(e)}")
        return {"status": "error", "message": f"Failed to get account summary: {str(e)}"}

@mcp.tool()
async def get_pnl_data(account_id: str, contract_id: int = None) -> Dict: # Made account_id required as per IBKR API
    """
    Get P&L data for an account or specific position

    Args:
        account_id: Account ID
        contract_id: Contract ID for position-specific P&L (optional)
    Returns:
        P&L information
    """
    try:            
        if not ibkr_service.connected:
            logger.info("Not connected to TWS, attempting to connect for P&L data request")
            await ibkr_service.connect()

        # This is a placeholder as the actual implementation might depend on further service development
        if contract_id:
            logger.info(f"Fetching position-specific P&L for contract ID: {contract_id}")
            # Implement this method in the service if needed
            # result = await ibkr_service.get_position_pnl(account_id, contract_id)
            return {
                "status": "error",
                "message": "Position-specific P&L not implemented yet",
                "errorType": "not_implemented"
            }
        else:
            logger.info(f"Fetching account-level P&L for account: {account_id or 'default'}")
            # Implement this method in the service if needed
            # result = await ibkr_service.get_account_pnl(account_id)
            return {
                "status": "error",
                "message": "Account-level P&L not implemented yet",
                "errorType": "not_implemented"
            }
    except Exception as e:
        logger.error(f"Unexpected error while getting P&L data: {str(e)}")
        return {"status": "error", "message": f"Failed to get P&L data: {str(e)}"}

@mcp.tool()
async def search_contracts(pattern: str) -> Dict: # Removed account: str = None as it's not used by ib_async reqContractDetailsAsync()
    """
    Search for stock contracts matching a pattern

    The input can be either the first few letters of the ticker symbol
    or a character sequence matching a word in the security name.    Args:
        pattern: Search pattern (e.g., "AAPL" or "Apple")
    Returns:
        List of matching contracts
    """
    try:            
        # Check cache first
        cache_key = f"search_contracts_{pattern}"
        cached_result = tools_cache.get(cache_key)
        if cached_result:
            return cached_result

        if not ibkr_service.connected:
            logger.info(f"Not connected to TWS, attempting to connect for contract search: {pattern}")
            await ibkr_service.connect()

        # Search for contracts using the service
        logger.info(f"Searching for contracts with pattern: {pattern}")
        result = await ibkr_service.search_contracts(pattern)
        
        # Cache the result
        tools_cache.set(cache_key, result)
        
        return result
    except IBKRConnectionError as e:
        logger.error(f"Connection error while searching contracts for {pattern}: {str(e)}")
        return {"status": "error", "message": f"Connection error: {str(e)}", "errorType": "connection"}
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error while searching contracts for {pattern}: {str(e)}")
        return {"status": "error", "message": f"Timeout error: {str(e)}", "errorType": "timeout"}
    except IBKRDataError as e:
        logger.error(f"Data error while searching contracts for {pattern}: {str(e)}")
        return {"status": "error", "message": f"Data error: {str(e)}", "errorType": "data"}
    except Exception as e:
        logger.error(f"Unexpected error while searching contracts for {pattern}: {str(e)}")
        return {"status": "error", "message": f"Failed to search contracts: {str(e)}"}

# MCP Resources for data access

# Base portfolio resource (all positions) # Removed account: str = None
@mcp.resource("ibkr://portfolio")
async def portfolio_resource() -> Dict:
    """
    Resource providing all portfolio positions
    """
    try:            
        # Check cache first
        cache_key = "resource_portfolio"
        cached_result = resources_cache.get(cache_key)
        if cached_result:
            return cached_result

        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info("Not connected to TWS, attempting to connect for portfolio resource")
            await ibkr_service.connect()

        # Get portfolio data
        logger.info("Fetching portfolio data for resource")
        portfolio = await ibkr_service.get_portfolio()

        # Format for display
        formatted_portfolio = []
        for position in portfolio:
            formatted_portfolio.append({
                "symbol": position.get("symbol", ""),
                "position": position.get("position", 0),
                "marketPrice": position.get("marketPrice", 0),
                "marketValue": position.get("marketValue", 0),
                "averageCost": position.get("averageCost", 0),
                "unrealizedPNL": position.get("unrealizedPNL", 0),
                "realizedPNL": position.get("realizedPNL", 0)
            })

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        result = {
            "positions": formatted_portfolio,
            "timestamp": timestamp,
            "count": len(formatted_portfolio),
            "status": "success"
        }
        
        # Cache the result
        resources_cache.set(cache_key, result)
        
        return result
    except IBKRConnectionError as e:
        logger.error(f"Connection error in portfolio resource: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in portfolio resource: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Portfolio resource error: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Parameterized portfolio resource (specific position)
@mcp.resource("ibkr://portfolio/{symbol}") # Removed account: str = None
async def portfolio_position_resource(symbol: str) -> Dict:
    """
    Resource providing data for a specific portfolio position

    Args:
        symbol: The ticker symbol to retrieve position data for
    """
    try:            
        # Check cache first
        cache_key = f"resource_portfolio_{symbol}"
        cached_result = resources_cache.get(cache_key)
        if cached_result:
            return cached_result

        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info(f"Not connected to TWS, attempting to connect for position resource: {symbol}")
            await ibkr_service.connect()

        # Get portfolio data
        logger.info(f"Fetching portfolio data for symbol: {symbol}")
        portfolio = await ibkr_service.get_portfolio()

        # Find the specific position
        position = None
        for pos in portfolio:
            if pos.get("symbol", "").upper() == symbol.upper():
                position = pos
                break

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if position:
            # Format the position data
            formatted_position = {
                "symbol": position.get("symbol", ""),
                "position": position.get("position", 0),
                "marketPrice": position.get("marketPrice", 0),
                "marketValue": position.get("marketValue", 0),
                "averageCost": position.get("averageCost", 0),
                "unrealizedPNL": position.get("unrealizedPNL", 0),
                "realizedPNL": position.get("realizedPNL", 0)
            }

            result = {
                "position": formatted_position,
                "timestamp": timestamp,
                "status": "success"
            }
            
            # Cache the result
            resources_cache.set(cache_key, result)
            
            return result
        else:
            return {
                "error": f"Position not found for symbol: {symbol}",
                "errorType": "not_found",
                "status": "error",
                "timestamp": timestamp
            }
    except IBKRConnectionError as e:
        logger.error(f"Connection error in position resource for {symbol}: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in position resource for {symbol}: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Position resource error for {symbol}: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Base account summary resource
@mcp.resource("ibkr://account-summary") # Removed account: str = None
async def account_summary_resource() -> Dict:
    """
    Resource providing complete account summary data
    """
    try:            
        # Check cache first
        cache_key = "resource_account_summary"
        cached_result = resources_cache.get(cache_key)
        if cached_result:
            return cached_result

        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info("Not connected to TWS, attempting to connect for account summary resource")
            await ibkr_service.connect()

        # Get account summary
        logger.info("Fetching account summary for resource")
        summary = await ibkr_service.get_account_summary()

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Return formatted data
        result = {
            "accountSummary": summary,
            "timestamp": timestamp,
            "status": "success"
        }
        
        # Cache the result
        resources_cache.set(cache_key, result)
        
        return result
    except IBKRConnectionError as e:
        logger.error(f"Connection error in account summary resource: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in account summary resource: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Account summary resource error: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Parameterized account summary resource (specific value)
@mcp.resource("ibkr://account-summary/{tag}") # Removed account: str = None
async def account_value_resource(tag: str) -> Dict:
    """
    Resource providing a specific account summary value

    Args:
        tag: The account summary tag to retrieve (e.g., "NetLiquidation", "BuyingPower")
    """
    try:            
        # Check cache first
        cache_key = f"resource_account_summary_{tag}"
        cached_result = resources_cache.get(cache_key)
        if cached_result:
            return cached_result

        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info(f"Not connected to TWS, attempting to connect for account value resource: {tag}")
            await ibkr_service.connect()

        # Get account summary
        logger.info(f"Fetching account summary value for tag: {tag}")
        summary = await ibkr_service.get_account_summary()

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Find the specific value
        if tag in summary:
            result = {
                "tag": tag,
                "value": summary[tag],
                "timestamp": timestamp,
                "status": "success"
            }
            
            # Cache the result
            resources_cache.set(cache_key, result)
            
            return result
        else:
            return {
                "error": f"Account value not found for tag: {tag}",
                "errorType": "not_found",
                "status": "error",
                "timestamp": timestamp
            }
    except IBKRConnectionError as e:
        logger.error(f"Connection error in account value resource for {tag}: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in account value resource for {tag}: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Account value resource error for {tag}: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Market data resource with symbol parameter
@mcp.resource("ibkr://market-data/{symbol}") # Removed exchange: str = "SMART" as it's not part of the resource path
async def market_data_resource(symbol: str) -> Dict:
    """
    Resource providing market data for a specific symbol

    Args:
        symbol: The ticker symbol to retrieve market data for
    """
    try:            
        # Check cache first
        cache_key = f"resource_market_data_{symbol}"
        cached_result = resources_cache.get(cache_key)
        if cached_result:
            return cached_result

        # Ensure connection with proper error handling
        if not ibkr_service.connected:
            logger.info(f"Not connected to TWS, attempting to connect for market data resource: {symbol}")
            await ibkr_service.connect()

        # Get market data
        logger.info(f"Fetching market data for symbol: {symbol}")
        market_data = await ibkr_service.get_market_data(symbol)

        # Add timestamp and metadata
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if market_data:
            result = {
                "symbol": symbol,
                "data": market_data,
                "timestamp": timestamp,
                "status": "success"
            }
            
            # Cache the result
            resources_cache.set(cache_key, result)
            
            return result
        else:
            return {
                "error": f"Market data not available for symbol: {symbol}",
                "errorType": "not_found",
                "status": "error",
                "timestamp": timestamp
            }
    except IBKRConnectionError as e:
        logger.error(f"Connection error in market data resource for {symbol}: {str(e)}")
        return {
            "error": f"Connection error: {str(e)}",
            "errorType": "connection",
            "status": "error"
        }
    except IBKRTimeoutError as e:
        logger.error(f"Timeout error in market data resource for {symbol}: {str(e)}")
        return {
            "error": f"Timeout error: {str(e)}",
            "errorType": "timeout",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Market data resource error for {symbol}: {str(e)}")
        return {
            "error": str(e),
            "errorType": "unknown",
            "status": "error"
        }

# Advanced tool with progress reporting
@mcp.tool()
async def analyze_portfolio(risk_level: str = "medium", ctx: Context = None) -> Dict: # Removed account: str = None
    """
    Analyze portfolio for risk and opportunities

    Args:
        risk_level: Risk tolerance level ("low", "medium", "high")
        ctx: MCP Context for progress reporting
    Returns:
        Portfolio analysis results
    """
    try:            
        if not ibkr_service.connected:
            logger.info("Not connected to TWS, attempting to connect for portfolio analysis")
            await ibkr_service.connect()

        # Get portfolio data
        if ctx:
            ctx.info("Fetching portfolio data...")
            await ctx.report_progress(0, 4)

        portfolio = await ibkr_service.get_portfolio()

        if ctx:
            ctx.info("Analyzing positions...")
            await ctx.report_progress(1, 4)

        # Simulate analysis (replace with actual analysis logic)
        await asyncio.sleep(1)

        if ctx:
            ctx.info("Calculating risk metrics...")
            await ctx.report_progress(2, 4)

        # Simulate risk calculation
        await asyncio.sleep(1)

        if ctx:
            ctx.info("Generating recommendations...")
            await ctx.report_progress(3, 4)

        # Generate recommendations based on risk level
        recommendations = []
        if risk_level == "low":
            recommendations = ["Consider diversifying with bonds", "Review stop losses"]
        elif risk_level == "medium":
            recommendations = ["Balance growth and value stocks", "Consider sector rotation"]
        else:  # high
            recommendations = ["Look for high-growth opportunities", "Consider options strategies"]

        if ctx:
            await ctx.report_progress(4, 4)

        return {
            "status": "success",
            "analysis": {
                "riskLevel": risk_level,
                "positionCount": len(portfolio),
                "recommendations": recommendations
            }
        }
    except Exception as e:
        logger.error(f"Portfolio analysis error: {str(e)}")
        return {"status": "error", "message": f"Failed to analyze portfolio: {str(e)}"}

# Connection management with proper error handling
@mcp.tool()
async def check_connection_status() -> Dict:
    """
    Check the current connection status to TWS/IB Gateway

    Returns:
        Connection status information
    """
    try:
        # Use ibkr_service.connected for the service's managed connection state
        # and ibkr_service.ib.isConnected() for the raw ib_async client state.
        is_ib_async_connected = ibkr_service.ib.isConnected() if ibkr_service.ib else False
        is_service_ready = ibkr_service.connected

        connection_time = getattr(ibkr_service, "connection_time", None)
        client_id = getattr(ibkr_service, "client_id", None)

        return {
            "status": "success",
            "connected": is_ib_async_connected, # Actual state of the ib_async client
            "connectionReady": is_service_ready, # Service's perception of readiness
            "connectionTime": connection_time.strftime("%Y-%m-%d %H:%M:%S") if connection_time else None,
            "clientId": client_id,
            "host": getattr(ibkr_service, "host", None),
            "port": getattr(ibkr_service, "port", None)
        }
    except Exception as e:
        logger.error(f"Connection status error: {str(e)}")
        return {"status": "error", "message": f"Failed to check connection: {str(e)}"}

@mcp.tool()
async def disconnect_from_tws() -> Dict:
    """
    Disconnect from TWS/IB Gateway

    Returns:
        Disconnection status
    """
    try:
        # Disconnect using the service
        await ibkr_service.disconnect()

        return {
            "status": "success",
            "message": "Successfully disconnected from TWS/IB Gateway"
        }
    except Exception as e:
        logger.error(f"Disconnection error: {str(e)}")
        return {"status": "error", "message": f"Failed to disconnect: {str(e)}"}

# Enhanced error handling tool
@mcp.tool()
async def get_error_info(error_code: int) -> Dict:
    """
    Get information about a specific TWS API error code

    Args:
        error_code: The TWS API error code

    Returns:
        Information about the error
    """
    # Mapping of common TWS API error codes and their descriptions
    error_codes = {
        100: "Max rate of messages per second has been exceeded",
        101: "Max number of tickers has been reached",
        103: "Duplicate order ID",
        104: "Can't modify a filled order",
        105: "Order being modified does not match original order",
        106: "Can't transmit order ID",
        107: "Can't transmit incomplete order",
        109: "Price is out of the acceptable range",
        110: "Price does not conform to the minimum price variation for this contract",
        111: "TIF type is not valid for this order",
        200: "No security definition found",
        201: "Order rejected - Reason:",
        202: "Order cancelled - Reason:",
        300: "Can't find EId with ticker Id",
        301: "Can't find ticker with EId",
        302: "Can't find order with EId",
        10000: "TWS is not connected to the server",
        10001: "TWS is not running",
        10002: "TWS is not connected to IB server",
        10006: "Missing parent order",
    }

    if error_code in error_codes:
        description = error_codes[error_code]
    else:
        description = "Unknown error code"

    return {
        "status": "success",
        "errorCode": error_code,
        "description": description
    }

# Enhanced logging setup
def setup_logging():
    """
    Set up enhanced logging for the MCP server
    """
    # Configure logging
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    log_datefmt = '%Y-%m-%dT%H:%M:%S.%03dZ'  # ISO 8601 format with milliseconds and UTC indicator
    
    # Create logs directory in user's home directory
    import os
    from pathlib import Path
    
    # Use home directory for logs
    home_dir = Path.home()
    log_dir = home_dir / "ibkr_mcp_logs"
    
    # Create directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)
    
    # Full path to log file
    log_file = log_dir / "ibkr_mcp_server.log"
    
    # Set up logging with consistent timestamp format
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt=log_datefmt,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(str(log_file))
        ]
    )

    # Create a logger for this module
    global logger
    logger = logging.getLogger('ibkr_mcp')
    logger.setLevel(logging.INFO)

    # Log startup
    logger.info(f"IBKR MCP Server logging initialized (log file: {log_file})")

# Initialize logging
setup_logging()

# Main server configuration
if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="IBKR MCP Server")
    parser.add_argument("--host", default="127.0.0.1", help="TWS/IB Gateway host")
    parser.add_argument("--port", type=int, default=7496, help="TWS/IB Gateway port (7496 for live, 7497 for paper)")
    parser.add_argument("--client-id", type=int, default=1, help="Client ID for TWS connection")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--auto-connect", action="store_true", help="Automatically connect to TWS on startup")
    args = parser.parse_args()

    # Configure logging level based on debug flag
    if args.debug:
        logging.getLogger('ibkr_mcp').setLevel(logging.DEBUG)
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize the IBKR service with command line arguments
    ibkr_service.host = args.host
    ibkr_service.port = args.port
    ibkr_service.client_id = args.client_id

    # Auto-connect if specified
    if args.auto_connect:# Consider adding readonly=False here if auto-connect implies trading intent
        asyncio.create_task(ibkr_service.connect(readonly=False)) # Example: auto-connect for trading

    # Log server startup
    logging.info(f"Starting IBKR MCP Server (connecting to TWS at {args.host}:{args.port})")

    # Run the MCP server
    mcp.run()

# Documentation resources

# Base documentation resource
@mcp.resource("docs://overview")
def get_overview_docs() -> str:
    """Provide an overview of the IBKR MCP server"""
    return """
    # IBKR MCP Server

    This server provides access to Interactive Brokers' TWS API through the Model Context Protocol (MCP).

    ## Available Tools

    ### Connection & Basic Tools
    - **connect_to_tws**: Connect to TWS/IB Gateway
    - **disconnect_from_tws**: Disconnect from TWS/IB Gateway
    - **check_connection_status**: Check the current connection status
    - **get_error_info**: Get information about TWS API error codes

    ### Portfolio & Market Data
    - **get_portfolio**: Get portfolio positions
    - **get_market_data**: Get real-time market data for a symbol
    - **get_account_summary**: Get account summary information
    - **search_contracts**: Search for contracts by symbol
    - **generate_price_chart**: Generate a price chart for a symbol
    - **analyze_portfolio**: Analyze portfolio risk and opportunities

    ### Order Management
    - **place_auction_order**: Place an auction order
    - **place_adaptive_order**: Place an adaptive algorithm order
    - **place_bracket_order**: Place a bracket order
    - **cancel_order**: Cancel an existing order
    - **modify_order**: Modify an existing order
    - **get_order_status**: Get order status
    - **get_active_orders**: Get all active orders

    ### Advanced Algorithmic Trading
    - **execute_algo_strategy**: Execute algorithmic trading strategies (stat arb, market making, etc.)
    - **subscribe_streaming_data**: Subscribe to real-time streaming market data
    - **unsubscribe_streaming_data**: Unsubscribe from streaming market data
    - **get_market_depth**: Get Level II market depth data
    - **rebalance_portfolio**: Automated portfolio rebalancing with multiple strategies
    - **optimize_portfolio**: Portfolio optimization (Sharpe, min variance, risk parity)
    - **route_smart_order**: Advanced order routing algorithms
    - **start_trading_strategy**: Start custom trading strategies
    - **stop_trading_strategy**: Stop a running trading strategy
    - **stop_algo_strategy**: Stop a running algorithmic strategy
    - **get_strategy_status**: Get strategy status and performance
    - **analyze_market_microstructure**: Market microstructure analysis
    - **get_active_algo_strategies**: Get list of active strategies

    ## Available Resources

    - **ibkr://portfolio**: All portfolio positions
    - **ibkr://portfolio/{symbol}**: Specific portfolio position
    - **ibkr://account-summary**: Complete account summary
    - **ibkr://account-summary/{tag}**: Specific account value
    - **ibkr://market-data/{symbol}**: Market data for a specific symbol
    - **docs://overview**: This overview documentation

    ## Getting Started

    1. Start TWS or IB Gateway
    2. Ensure the IBKR MCP Server is running.
       The server will attempt to initialize `IBKRService` and `OrderManagementService`.
       The `OrderManagementService` instance (`oms_instance`) will be made available for
       order management tools.
    2. Use the `connect_to_tws` tool to establish a connection
    3. Use the available tools to interact with your IBKR account
    """

# Manual initialization tool for debugging
@mcp.tool()
async def debug_initialize_oms() -> Dict[str, Any]:
    """
    Manually initialize OrderManagementService for debugging
    """
    global oms_instance
    try:
        if oms_instance is None:
            logger.info("Manually initializing OrderManagementService")
            oms_instance = OrderManagementService(ibkr_svc=ibkr_service)
            ibkr_service.set_order_management_service(oms_delegate=oms_instance)
            logger.info("OrderManagementService manually initialized")
            return {"status": "success", "message": "OrderManagementService initialized"}
        else:
            return {"status": "success", "message": "OrderManagementService already initialized"}
    except Exception as e:
        logger.error(f"Failed to manually initialize OMS: {e}")
        return {"status": "error", "message": f"Failed to initialize: {str(e)}"}

# Register the order management tools with proper MCP decorators
@mcp.tool()
async def cancel_order(order_id: int) -> Dict[str, Any]:
    """
    Cancel an existing order by its ID.
    
    Args:
        order_id: The unique ID of the order to be cancelled.
        
    Returns:
        A dictionary containing the status of the cancellation request
    """
    # Import here to avoid circular imports at module level
    from .tools.order_management_tools import cancel_order as cancel_order_func
    return await cancel_order_func(order_id)

@mcp.tool()
async def modify_order(order_id: int, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    Modify an existing order with new parameters.
    
    Args:
        order_id: The ID of the order to modify.
        parameters: A dictionary containing the order parameters to change.
                   Supported keys: 'quantity', 'price', 'stop_price'
                   
    Returns:
        A dictionary containing the status of the modification request
    """
    from .tools.order_management_tools import modify_order as modify_order_func
    return await modify_order_func(order_id, parameters)

@mcp.tool()
async def get_order_status(order_id: int) -> Dict[str, Any]:
    """
    Get the current status of a specific order by its ID.
    
    Args:
        order_id: The ID of the order to check.
        
    Returns:
        A dictionary containing detailed status information for the order
    """
    from .tools.order_management_tools import get_order_status as get_order_status_func
    return await get_order_status_func(order_id)

@mcp.tool()
async def get_active_orders() -> Dict[str, Any]:
    """
    Retrieve a list of all currently active (open) orders.
    
    Returns:
        A dictionary containing a list of active orders with details
    """
    from .tools.order_management_tools import get_active_orders as get_active_orders_func
    return await get_active_orders_func()

@mcp.tool()
async def check_service_status() -> Dict[str, Any]:
    """
    Check the status of all services including OrderManagementService and Algorithmic Trading Services
    """
    global oms_instance, algo_service, market_data_service, rebalancing_service, routing_service, strategy_service
    
    ibkr_connected = ibkr_service.connected
    oms_initialized = oms_instance is not None
    algo_initialized = algo_service is not None
    
    if not oms_initialized and ibkr_connected:
        # Try to initialize OMS if IBKR is connected but OMS isn't initialized
        try:
            oms_instance = OrderManagementService(ibkr_svc=ibkr_service)
            ibkr_service.set_order_management_service(oms_delegate=oms_instance)
            oms_initialized = True
            logger.info("OrderManagementService initialized on-demand")
        except Exception as e:
            logger.error(f"Failed to initialize OMS on-demand: {e}")
    
    return {
        "status": "success",
        "ibkr_service": {
            "initialized": ibkr_service is not None,
            "connected": ibkr_connected,
            "readonly": getattr(ibkr_service, "readonly", None),
            "host": getattr(ibkr_service, "host", None),
            "port": getattr(ibkr_service, "port", None)
        },
        "order_management_service": {
            "initialized": oms_initialized
        },
        "algorithmic_trading_services": {
            "algo_service": algo_service is not None,
            "market_data_service": market_data_service is not None,
            "rebalancing_service": rebalancing_service is not None,
            "routing_service": routing_service is not None,
            "strategy_service": strategy_service is not None
        }
    }

# Algorithmic Trading Tools

@mcp.tool()
async def execute_algo_strategy(
    strategy_type: str,
    symbols: List[str],
    max_position_size: float,
    risk_limit: float,
    account: str,
    custom_params: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Execute an algorithmic trading strategy
    
    Available strategies:
    - statistical_arbitrage: Exploit pricing inefficiencies
    - market_making: Provide liquidity with bid/ask spreads
    - trend_following: Trade in direction of trends
    - mean_reversion: Trade expecting price to revert to mean
    - pairs_trading: Trade correlated pairs
    - momentum: Trade based on price momentum
    - vwap: Volume Weighted Average Price execution
    - twap: Time Weighted Average Price execution
    - smart_order_routing: Intelligent order routing
    - iceberg: Hidden size orders
    
    Args:
        strategy_type: Type of strategy to execute
        symbols: List of symbols to trade
        max_position_size: Maximum position size in dollars
        risk_limit: Maximum risk/loss limit
        account: Account ID
        custom_params: Strategy-specific parameters
    
    Returns:
        Strategy execution result
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import execute_algo_strategy as func
    return await func(strategy_type, symbols, max_position_size, risk_limit, account, custom_params)

@mcp.tool()
async def subscribe_streaming_data(
    symbol: str,
    data_types: List[str],
    generic_tick_list: str = ""
) -> Dict[str, Any]:
    """
    Subscribe to streaming market data
    
    Data types:
    - TRADES: Trade data
    - BID_ASK: Bid/ask quotes
    - MID_POINT: Midpoint prices
    - HISTORICAL: Historical data with updates
    - OPTION_COMPUTATION: Option calculations
    - REAL_TIME_BARS: 5-second bars
    - TICK_BY_TICK: Tick-by-tick data
    
    Args:
        symbol: Symbol to subscribe to
        data_types: List of data types
        generic_tick_list: IB generic tick list
    
    Returns:
        Subscription details
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import subscribe_streaming_data as func
    return await func(symbol, data_types, generic_tick_list)

@mcp.tool()
async def rebalance_portfolio(
    strategy: str,
    target_allocations: List[Dict[str, Any]],
    account: str,
    rebalance_threshold: float = 0.05,
    force: bool = False
) -> Dict[str, Any]:
    """
    Rebalance portfolio using specified strategy
    
    Strategies:
    - calendar: Time-based rebalancing
    - threshold: Deviation-based rebalancing
    - tactical: Market conditions-based
    - risk_parity: Equal risk contribution
    - minimum_variance: Minimize portfolio variance
    - maximum_sharpe: Maximize Sharpe ratio
    
    Args:
        strategy: Rebalancing strategy
        target_allocations: List of target allocations
        account: Account ID
        rebalance_threshold: Threshold for rebalancing (default: 5%)
        force: Force rebalancing even if within threshold
    
    Returns:
        Rebalancing result
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import rebalance_portfolio as func
    return await func(strategy, target_allocations, account, rebalance_threshold, force)

@mcp.tool()
async def route_smart_order(
    algorithm: str,
    symbol: str,
    action: str,
    quantity: float,
    account: str,
    urgency: str = "Normal",
    custom_params: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Route order using advanced algorithms
    
    Algorithms:
    - smart: IB's Smart routing
    - directed: Direct to specific exchange
    - dark_pool: Dark pool seeking
    - sweep: Sweep multiple venues
    - midpoint: Midpoint matching
    - pegged: Pegged orders
    - liquidity_seeking: Seek liquidity
    - cost_aware: Minimize total cost
    
    Args:
        algorithm: Routing algorithm
        symbol: Symbol to trade
        action: BUY or SELL
        quantity: Order quantity
        account: Account ID
        urgency: Urgent, Normal, or Patient
        custom_params: Algorithm-specific parameters
    
    Returns:
        Routing result
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import route_smart_order as func
    return await func(algorithm, symbol, action, quantity, account, urgency, custom_params)

@mcp.tool()
async def start_trading_strategy(
    strategy_name: str,
    strategy_id: str,
    account: str,
    symbols: List[str],
    parameters: Dict[str, Any],
    risk_limits: Dict[str, float]
) -> Dict[str, Any]:
    """
    Start a trading strategy
    
    Built-in strategies:
    - simple_momentum: Momentum trading
    - mean_reversion: Mean reversion trading
    - pairs_trading: Pairs trading
    
    Args:
        strategy_name: Name of strategy
        strategy_id: Unique strategy ID
        account: Account ID
        symbols: Symbols to trade
        parameters: Strategy parameters
        risk_limits: Risk limits (max_position_size, max_loss, max_orders)
    
    Returns:
        Start result
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import start_trading_strategy as func
    return await func(strategy_name, strategy_id, account, symbols, parameters, risk_limits)

@mcp.tool()
async def optimize_portfolio(
    symbols: List[str],
    optimization_method: str = "maximum_sharpe",
    constraints: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Optimize portfolio allocation
    
    Methods:
    - maximum_sharpe: Maximize Sharpe ratio
    - minimum_variance: Minimize portfolio variance
    - risk_parity: Equal risk contribution
    
    Args:
        symbols: List of symbols to include
        optimization_method: Optimization method
        constraints: Portfolio constraints
    
    Returns:
        Optimal allocations
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import optimize_portfolio as func
    return await func(symbols, optimization_method, constraints)

@mcp.tool()
async def analyze_market_microstructure(
    symbol: str,
    size: int
) -> Dict[str, Any]:
    """
    Analyze market microstructure for optimal routing
    
    Args:
        symbol: Symbol to analyze
        size: Order size
    
    Returns:
        Market analysis
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import analyze_market_microstructure as func
    return await func(symbol, size)

@mcp.tool()
async def get_active_algo_strategies() -> Dict[str, Any]:
    """
    Get list of all active algorithmic strategies
    
    Returns:
        List of active strategies
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import get_active_algo_strategies as func
    return await func()

@mcp.tool()
async def stop_algo_strategy(strategy_id: str) -> Dict[str, Any]:
    """
    Stop a running algorithmic trading strategy
    
    Args:
        strategy_id: ID of the strategy to stop
    
    Returns:
        Stop result
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import stop_algo_strategy as func
    return await func(strategy_id)

@mcp.tool()
async def get_market_depth(symbol: str, num_rows: int = 5) -> Dict[str, Any]:
    """
    Get Level II market depth data
    
    Args:
        symbol: Symbol to get depth for
        num_rows: Number of depth levels (default: 5)
    
    Returns:
        Market depth data
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import get_market_depth as func
    return await func(symbol, num_rows)

@mcp.tool()
async def unsubscribe_streaming_data(subscription_id: str) -> Dict[str, Any]:
    """
    Unsubscribe from streaming market data
    
    Args:
        subscription_id: Subscription ID to cancel
    
    Returns:
        Unsubscribe result
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import unsubscribe_streaming_data as func
    return await func(subscription_id)

@mcp.tool()
async def stop_trading_strategy(strategy_id: str) -> Dict[str, Any]:
    """
    Stop a running trading strategy
    
    Args:
        strategy_id: Strategy ID to stop
    
    Returns:
        Stop result with performance
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import stop_trading_strategy as func
    return await func(strategy_id)

@mcp.tool()
async def get_strategy_status(strategy_id: str) -> Dict[str, Any]:
    """
    Get strategy status and performance
    
    Args:
        strategy_id: Strategy ID
    
    Returns:
        Strategy status
    """
    from ibkr_mcp_server.app.mcp.tools.algorithmic_trading_tools import get_strategy_status as func
    return await func(strategy_id)

# Enhanced prompts for Claude Desktop
@mcp.prompt("ibkr://prompts/trading-assistant")
def trading_assistant_prompt() -> str:
    """
    Main prompt for the trading assistant
    """
    return """
    You are an advanced Interactive Brokers trading assistant powered by Claude with sophisticated algorithmic trading capabilities. You can help with:

    1. Connecting to TWS or IB Gateway
    2. Retrieving portfolio and account information
    3. Getting market data, streaming data, and Level II depth
    4. Placing various types of orders (market, limit, stop, bracket, etc.)
    5. Executing algorithmic trading strategies:
       - Statistical arbitrage & pairs trading
       - Market making with spread management
       - Trend following & momentum strategies
       - Mean reversion trading
       - VWAP/TWAP execution algorithms
       - Smart order routing across venues
       - Iceberg orders for large positions
    6. Portfolio optimization and rebalancing:
       - Maximum Sharpe ratio optimization
       - Minimum variance portfolios
       - Risk parity allocation
       - Calendar and threshold-based rebalancing
    7. Market microstructure analysis
    8. Real-time strategy monitoring and management

    To get started, help the user connect to TWS using the connect_to_tws tool.
    For paper trading, use port 7497. For live trading, use port 7496.

    Always provide clear explanations of market data and portfolio information.
    When discussing orders or strategies, explain their implications and risks.

    IMPORTANT: Always confirm before placing any orders or starting strategies, especially for large amounts.
    """

@mcp.prompt("ibkr://prompts/order-confirmation")
def order_confirmation_prompt(order_details: Dict) -> str:
    """
    Prompt for confirming order placement
    """
    symbol = order_details.get("symbol", "")
    action = order_details.get("action", "")
    quantity = order_details.get("quantity", 0)
    price = order_details.get("price", 0)
    order_type = order_details.get("orderType", "")

    return f"""
    Please confirm the following order details before I place the order:

    Symbol: {symbol}
    Action: {action}
    Quantity: {quantity}
    Price: ${price}
    Order Type: {order_type}

    Is this correct? Please respond with "Yes, place the order" to confirm,
    or "No, cancel" to cancel the order.
    """

@mcp.prompt("ibkr://prompts/market-analysis")
def market_analysis_prompt(symbol: str) -> str:
    """
    Prompt for market analysis
    """
    return f"""
    I'll analyze the market data for {symbol} and provide insights on:

    1. Current price and recent price action
    2. Volume analysis
    3. Technical indicators (if available)
    4. Potential support and resistance levels
    5. Overall market context

    Please note that this analysis is for informational purposes only and
    not financial advice.
    """

# Chart generation tool
@mcp.tool()
async def generate_price_chart(symbol: str, timeframe: str = "1d") -> Image: # Removed account: str = None
    """
    Generate a price chart for a symbol

    Args:
        symbol: The ticker symbol
        timeframe: Timeframe (1d, 5d, 1m, 3m, 6m, 1y, 5y)
    Returns:
        An image of the price chart
    """
    if not symbol:
        logger.error("Symbol is required but not provided")
        return create_error_chart("Symbol is required")

    # Validate timeframe
    valid_timeframes = ["1d", "5d", "1m", "3m", "6m", "1y", "5y"]
    if timeframe not in valid_timeframes:
        logger.error(f"Invalid timeframe: {timeframe}")
        return create_error_chart(f"Invalid timeframe: {timeframe}. Valid options are: {', '.join(valid_timeframes)}")

    try:
        # Get historical data using the service
        logger.info(f"Fetching historical data for {symbol} with timeframe {timeframe}")
        
        try:
            historical_data = await ibkr_service.get_historical_data(symbol, timeframe)
        except AttributeError:
            # If the method doesn't exist yet, create mock data
            logger.warning("get_historical_data not implemented, using mock data")
            historical_data = create_mock_historical_data(symbol, timeframe)

        if not historical_data or len(historical_data) == 0:
            logger.warning(f"No historical data available for {symbol}")
            return create_error_chart(f"No historical data available for {symbol}")

        # Create a matplotlib figure
        plt.figure(figsize=(10, 6))
        plt.plot([bar['date'] for bar in historical_data],
                [bar['close'] for bar in historical_data])
        plt.title(f"{symbol} Price Chart ({timeframe})")
        plt.xlabel("Date")
        plt.ylabel("Price")
        plt.grid(True)

        # Add timestamp to the chart
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        plt.figtext(0.02, 0.02, f"Generated: {timestamp}", fontsize=8)

        # Save the figure to a bytes buffer
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=100)
        buf.seek(0)
        plt.close()  # Close the figure to free memory

        # Create an MCP Image with the correct parameter name
        image_data = buf.getvalue()
        return Image(data=image_data, format="png")

    except Exception as e:
        logger.error(f"Unexpected error while generating chart for {symbol}: {str(e)}")
        return create_error_chart(f"Error generating chart: {str(e)}")

def create_error_chart(error_message: str) -> Image:
    """Create an error chart with the given message"""
    plt.figure(figsize=(10, 6))
    plt.text(0.5, 0.5, error_message,
            horizontalalignment='center', verticalalignment='center',
            fontsize=12, color='red')
    plt.axis('off')

    # Add timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    plt.figtext(0.02, 0.02, f"Error generated: {timestamp}", fontsize=8)

    buf = io.BytesIO()
    plt.savefig(buf, format='png', dpi=100)
    buf.seek(0)
    plt.close()  # Close the figure to free memory

    return Image(data=buf.getvalue(), format="png")

def create_mock_historical_data(symbol: str, timeframe: str):
    """Create mock historical data for chart generation"""
    import numpy as np
    from datetime import datetime, timedelta
    
    # Determine number of data points based on timeframe
    if timeframe == "1d":
        days = 1
        points = 24
    elif timeframe == "5d":
        days = 5
        points = 5 * 24
    elif timeframe == "1m":
        days = 30
        points = 30
    elif timeframe == "3m":
        days = 90
        points = 90
    elif timeframe == "6m":
        days = 180
        points = 180
    elif timeframe == "1y":
        days = 365
        points = 365
    else:  # 5y
        days = 1825
        points = 1825
    
    # Generate dates
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    dates = [start_date + timedelta(days=i) for i in range(points)]
    
    # Generate mock price data
    np.random.seed(sum(ord(c) for c in symbol))  # Seed based on symbol
    base_price = 100 + (ord(symbol[0]) % 10) * 10  # Different starting price based on symbol
    
    # Create a somewhat realistic price movement
    changes = np.random.normal(0, 1, points)
    prices = [base_price]
    for change in changes:
        prices.append(max(1, prices[-1] * (1 + change * 0.01)))
    prices = prices[1:]  # Remove the initial seed
    
    # Format data
    historical_data = []
    for i, date in enumerate(dates):
        historical_data.append({
            'date': date.strftime("%Y-%m-%d"),
            'open': prices[i] * 0.99,
            'high': prices[i] * 1.02,
            'low': prices[i] * 0.98,
            'close': prices[i],
            'volume': int(np.random.uniform(1000, 10000))
        })
    
    return historical_data
