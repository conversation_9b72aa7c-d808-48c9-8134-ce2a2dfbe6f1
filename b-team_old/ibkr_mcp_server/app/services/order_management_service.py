# /Users/<USER>/projects/b-team/ibkr_mcp_server/app/services/order_management_service.py
import asyncio
import logging
from typing import Dict, Any, Callable, Optional
from datetime import datetime
from ib_async import IB, Contract, Order as IBOrder, OrderState as IBOrderState # Assuming these are the correct ib_async types
from decimal import Decimal

from ibkr_mcp_server.app.models.order_models import OrderStatus, OrderInfo
from ibkr_mcp_server.app.services.ibkr_service import IBKRService, IBKROrderError, IBKRConnectionError # Import existing service

logger = logging.getLogger('order-management-service') # Dedicated logger

class OrderManagementService:
    def __init__(self, ibkr_svc: IBKRService):
        self.ibkr_service = ibkr_svc # Reference to the main IBKRService
        self.ib: IB = ibkr_svc.ib # Convenience reference to the ib_async IB instance
        
        self._order_status_cache: Dict[int, OrderInfo] = {}
        self._active_orders_cache: Dict[int, OrderInfo] = {} # Stores OrderInfo objects
        self._order_event_callbacks: Dict[int, Callable[[OrderInfo], None]] = {}
        self._order_events: Dict[int, asyncio.Event] = {} # For async waiting

        self._open_orders_complete_event = asyncio.Event()

    async def _get_order_event(self, order_id: int) -> asyncio.Event:
        if order_id not in self._order_events:
            self._order_events[order_id] = asyncio.Event()
        return self._order_events[order_id]

    # --- Methods called by IBKRService (EWrapper delegates) ---
    def handle_order_status_update(self, order_id: int, status_str: str, filled: float,
                                   remaining: float, avg_fill_price: float, perm_id: int,
                                   parent_id: int, last_fill_price: float, client_id: int,
                                   why_held: str, mkt_cap_price: Optional[float]):
        logger.info(f"Handling order status update for OrderID {order_id}: {status_str}")
        order_info = self._order_status_cache.get(order_id, OrderInfo(order_id=order_id))
        order_info.update_status(status_str, filled, remaining, avg_fill_price, perm_id,
                                 parent_id, last_fill_price, client_id, why_held, mkt_cap_price)
        self._order_status_cache[order_id] = order_info

        # Also update active orders cache if it's a terminal state
        if order_info.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.INACTIVE]:
            self._active_orders_cache.pop(order_id, None)
        elif order_id in self._active_orders_cache: # Update existing active order
             self._active_orders_cache[order_id].status = order_info.status
             self._active_orders_cache[order_id].filled = order_info.filled
             self._active_orders_cache[order_id].remaining = order_info.remaining
             # ... update other relevant fields ...
             self._active_orders_cache[order_id].timestamp = datetime.now()


        if order_id in self._order_event_callbacks:
            try:
                self._order_event_callbacks[order_id](order_info)
            except Exception as e:
                logger.error(f"Error in order status callback for {order_id}: {e}")
        
        if order_id in self._order_events:
            self._order_events[order_id].set() # Signal anyone waiting on this order_id

    def handle_open_order(self, order_id: int, contract: Contract, ib_order: IBOrder, ib_order_state: IBOrderState):
        logger.info(f"Handling open order: OrderID {order_id}, Symbol: {contract.symbol}")
        order_info = OrderInfo(
            order_id=order_id,
            status=OrderStatus(ib_order_state.status), # Convert from string
            filled=float(ib_order.filledQuantity) if hasattr(ib_order, 'filledQuantity') and ib_order.filledQuantity is not None else 0.0, # ib_async uses Decimal
            remaining=float(ib_order.totalQuantity - (ib_order.filledQuantity or 0)),
            avg_fill_price=ib_order_state.avgFillPrice if hasattr(ib_order_state, 'avgFillPrice') else 0.0,
            perm_id=ib_order.permId,
            client_id=ib_order.clientId,
            contract=contract,
            order=ib_order,
            order_state=ib_order_state,
            timestamp=datetime.now()
        )
        self._active_orders_cache[order_id] = order_info
        self._order_status_cache[order_id] = order_info # Keep status cache consistent

        if order_id in self._order_event_callbacks:
            try:
                self._order_event_callbacks[order_id](order_info)
            except Exception as e:
                logger.error(f"Error in open_order callback for {order_id}: {e}")
        if order_id in self._order_events:
            self._order_events[order_id].set()


    def handle_open_order_end(self):
        logger.info("Handling open order end.")
        self._open_orders_complete_event.set()

    # --- Public API for MCP Tools ---
    async def cancel_order(self, order_id: int) -> Dict[str, Any]:
        logger.info(f"Attempting to cancel order {order_id}")
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}

        # Check if order is already known and in a terminal state
        if order_id in self._order_status_cache:
            cached_status = self._order_status_cache[order_id].status
            if cached_status == OrderStatus.CANCELLED:
                return {"status": "success", "order_id": order_id, "message": "Order already cancelled."}
            if cached_status == OrderStatus.FILLED:
                return {"status": "error", "order_id": order_id, "message": "Order already filled, cannot cancel."}
        
        event = await self._get_order_event(order_id)
        event.clear() # Clear previous event state if any

        final_status_info: Optional[OrderInfo] = None

        def status_callback(order_info: OrderInfo):
            nonlocal final_status_info
            if order_info.status in [OrderStatus.CANCELLED, OrderStatus.API_CANCELLED, OrderStatus.FILLED, OrderStatus.INACTIVE]:
                logger.info(f"Cancel operation for {order_id} received terminal status: {order_info.status.value}")
                final_status_info = order_info
                event.set()
        
        self._order_event_callbacks[order_id] = status_callback
        
        try:
            # ib_async's cancelOrder is synchronous but triggers async events.
            # We need to find the order object if we only have the ID.
            # A more robust way might be to always use ib.cancelOrder(order_id) if ib_async supports it directly
            # or fetch the order object first.
            # For now, let's assume direct cancellation by ID is possible or TWS handles it.
            # The ibapi EClient.cancelOrder takes orderId and an OrderCancel object (can be empty string for manualOrderCancelTime)
            self.ib.cancelOrder(order_id) # This is from ib_async, it should find the order by ID from its internal list
            logger.info(f"Cancel request sent for order {order_id}")

            await asyncio.wait_for(event.wait(), timeout=10.0)
            
            if final_status_info and final_status_info.status in [OrderStatus.CANCELLED, OrderStatus.API_CANCELLED]:
                message = f"Order {order_id} cancelled successfully."
                status = "success"
            elif final_status_info and final_status_info.status == OrderStatus.FILLED:
                message = f"Order {order_id} was filled before cancellation could complete."
                status = "error"
            else: # Timeout or other status
                message = f"Cancellation status for order {order_id} uncertain or not yet confirmed cancelled. Last known: {final_status_info.status.value if final_status_info else 'Unknown'}"
                status = "pending" # Or "error" depending on desired strictness

            return {"status": status, "order_id": order_id, "message": message, "timestamp": datetime.now().isoformat()}

        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for cancellation confirmation for order {order_id}")
            return {"status": "error", "order_id": order_id, "message": f"Timeout cancelling order {order_id}", "timestamp": datetime.now().isoformat()}
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}", exc_info=True)
            return {"status": "error", "order_id": order_id, "message": f"Error cancelling order: {str(e)}", "timestamp": datetime.now().isoformat()}
        finally:
            self._order_event_callbacks.pop(order_id, None)
            self._order_events.pop(order_id, None)

    async def modify_order(self, order_id: int, parameters: Dict[str, Any]) -> Dict[str, Any]:
        logger.info(f"Attempting to modify order {order_id} with params: {parameters}")
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}

        # Fetch the existing order details from active_orders_cache or request if not found
        existing_order_info = self._active_orders_cache.get(order_id)
        if not existing_order_info or not existing_order_info.order:
            logger.info(f"Order {order_id} not in active cache, requesting open orders.")
            await self.get_active_orders() # This will populate the cache
            existing_order_info = self._active_orders_cache.get(order_id)
            if not existing_order_info or not existing_order_info.order:
                return {'status': 'error', 'order_id': order_id, 'message': f'Order {order_id} not found or not active.', 'timestamp': datetime.now().isoformat()}
        
        contract: Contract = existing_order_info.contract
        order: IBOrder = existing_order_info.order # This is an ib_async.Order object

        # Apply modifications
        modified_fields = []
        if 'quantity' in parameters:
            # ib_async.Order.totalQuantity expects Decimal
            new_quantity = Decimal(str(parameters['quantity']))
            order.totalQuantity = new_quantity
            modified_fields.append("quantity")
        if 'price' in parameters:
            order.lmtPrice = float(parameters['price'])
            modified_fields.append("price")
        if 'stop_price' in parameters: # For STP LMT or TRAIL orders
            order.auxPrice = float(parameters['stop_price'])
            modified_fields.append("stop_price")
        # Add more modifiable parameters as needed (e.g., order.orderType)

        if not modified_fields:
            return {'status': 'success', 'order_id': order_id, 'message': 'No parameters provided to modify.', 'timestamp': datetime.now().isoformat()}

        event = await self._get_order_event(order_id)
        event.clear()
        final_status_info: Optional[OrderInfo] = None

        def status_callback(order_info: OrderInfo):
            nonlocal final_status_info
            # Submitted or PreSubmitted usually indicates acceptance of modification
            if order_info.status in [OrderStatus.SUBMITTED, OrderStatus.PRE_SUBMITTED]:
                logger.info(f"Modify operation for {order_id} received status: {order_info.status.value}")
                final_status_info = order_info
                event.set()
            elif order_info.status in [OrderStatus.CANCELLED, OrderStatus.FILLED, OrderStatus.INACTIVE]:
                logger.warning(f"Modify operation for {order_id} resulted in terminal status: {order_info.status.value}")
                final_status_info = order_info # Capture terminal state if modification leads to it
                event.set()

        self._order_event_callbacks[order_id] = status_callback

        try:
            # ib_async.placeOrder can also modify if orderId is the same and permId matches
            trade = await self.ib.placeOrderAsync(contract, order)
            logger.info(f"Modification request sent for order {order_id}. New/Updated OrderId: {trade.order.orderId if trade else 'Unknown'}")
            
            await asyncio.wait_for(event.wait(), timeout=10.0)

            if final_status_info and final_status_info.status in [OrderStatus.SUBMITTED, OrderStatus.PRE_SUBMITTED]:
                message = f"Order {order_id} modified successfully. Modified fields: {', '.join(modified_fields)}."
                status = "success"
            else:
                message = f"Modification status for order {order_id} uncertain. Last known: {final_status_info.status.value if final_status_info else 'Unknown'}"
                status = "pending" # Or "error"

            return {"status": status, "order_id": order_id, "message": message, "modified_parameters": parameters, "timestamp": datetime.now().isoformat()}

        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for modification confirmation for order {order_id}")
            return {"status": "error", "order_id": order_id, "message": f"Timeout modifying order {order_id}", "timestamp": datetime.now().isoformat()}
        except Exception as e:
            logger.error(f"Error modifying order {order_id}: {e}", exc_info=True)
            return {"status": "error", "order_id": order_id, "message": f"Error modifying order: {str(e)}", "timestamp": datetime.now().isoformat()}
        finally:
            self._order_event_callbacks.pop(order_id, None)
            self._order_events.pop(order_id, None)

    async def get_order_status(self, order_id: int) -> Dict[str, Any]:
        logger.info(f"Getting status for order {order_id}")
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}

        if order_id in self._order_status_cache:
            order_info = self._order_status_cache[order_id]
            logger.debug(f"Order {order_id} found in cache: {order_info.status.value}")
            return {
                'status': 'success',
                'order_id': order_id,
                'order_status': order_info.status.value,
                'filled': order_info.filled,
                'remaining': order_info.remaining,
                'avg_fill_price': order_info.avg_fill_price,
                'last_fill_price': order_info.last_fill_price,
                'why_held': order_info.why_held,
                'timestamp': order_info.timestamp.isoformat()
            }

        logger.info(f"Order {order_id} not in cache, requesting open orders to refresh.")
        
        # Create or get an event for this order_id
        if order_id not in self._order_events:
            self._order_events[order_id] = asyncio.Event()
        event = self._order_events[order_id]
        event.clear()
        
        # We need to request open orders to refresh our cache
        # This will trigger orderStatus callbacks which will update our cache
        # If the order exists, we should receive an update
        self.ib.reqAllOpenOrders()  # This will trigger openOrder and orderStatus events
        
        try:
            await asyncio.wait_for(event.wait(), timeout=5.0)  # Wait for *any* update related to this orderId
            if order_id in self._order_status_cache:
                order_info = self._order_status_cache[order_id]
                return {
                    'status': 'success',
                    'order_id': order_id,
                    'order_status': order_info.status.value,
                    'filled': order_info.filled,
                    'remaining': order_info.remaining,
                    'avg_fill_price': order_info.avg_fill_price,
                    'last_fill_price': order_info.last_fill_price,
                    'why_held': order_info.why_held,
                    'timestamp': order_info.timestamp.isoformat()
                }
            else:  # Event triggered but order not in cache (shouldn't happen if event is for this order_id)
                raise asyncio.TimeoutError("Order status update not received in cache.")
        except asyncio.TimeoutError:
            logger.warning(f"Timeout or order {order_id} not found after requesting open orders.")
            return {'status': 'error', 'order_id': order_id, 'message': 'Order not found or timeout getting status', 'timestamp': datetime.now().isoformat()}
        except Exception as e:
            logger.error(f"Error in get_order_status for {order_id}: {e}", exc_info=True)
            return {'status': 'error', 'order_id': order_id, 'message': f'Error getting order status: {str(e)}', 'timestamp': datetime.now().isoformat()}
        finally:
            # Only remove the event if we're completely done with it
            self._order_events.pop(order_id, None)

    async def get_active_orders(self) -> Dict[str, Any]:
        logger.info("Getting all active orders.")
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}

        self._active_orders_cache.clear() # Clear before refresh
        # _open_orders_complete_event is not reliably set if openOrderEndEvent is unavailable.
        # self._open_orders_complete_event.clear() 
        # Implementation based on ibpy_native (https://github.com/Devtography/ibpy_native)
        # Copyright (c) Devtography
        # Licensed under the Apache License, Version 2.0
        # See: https://www.apache.org/licenses/LICENSE-2.0
        try:
            logger.info("Calling self.ib.reqAllOpenOrdersAsync() to fetch active orders.")
            # reqAllOpenOrdersAsync() returns an awaitable List[Trade].
            # ib_async handles the underlying openOrderEnd from TWS.
            # During this call, openOrderEvent handlers populate _active_orders_cache.
            await self.ib.reqAllOpenOrdersAsync()
            logger.info("self.ib.reqAllOpenOrdersAsync() completed. Processing cached orders.")
            
            # At this point, _active_orders_cache should be populated by _on_open_order via handle_open_order.
        
            active_orders_list = []
            for order_id, order_info_obj in list(self._active_orders_cache.items()): # Use list() for safe iteration
                contract = order_info_obj.contract
                ib_order = order_info_obj.order # This is an ib_async.Order object
                
                active_orders_list.append({
                    'order_id': order_id,
                    'symbol': contract.symbol if contract else "N/A",
                    'exchange': contract.exchange if contract else "N/A",
                    'action': ib_order.action if ib_order else "N/A",
                    'quantity': float(ib_order.totalQuantity) if ib_order else 0.0,
                    'order_type': ib_order.orderType if ib_order else "N/A",
                    'limit_price': ib_order.lmtPrice if ib_order and ib_order.lmtPrice != float('inf') else None,
                    'stop_price': ib_order.auxPrice if ib_order and ib_order.auxPrice != float('inf') else None,
                    'status': order_info_obj.status.value,
                    'time_in_force': ib_order.tif if ib_order else "N/A",
                    'filled': order_info_obj.filled,
                    'remaining': order_info_obj.remaining,
                    'avg_fill_price': order_info_obj.avg_fill_price,
                    'timestamp': order_info_obj.timestamp.isoformat()
                })
            
            return {'status': 'success', 'orders': active_orders_list, 'count': len(active_orders_list), 'timestamp': datetime.now().isoformat()}
        except Exception as e:
            logger.error(f"Error getting active orders: {e}", exc_info=True)
            return {'status': 'error', 'message': f'Error getting active orders: {str(e)}', 'timestamp': datetime.now().isoformat()}
