import os
import time
import logging
import asyncio
import nest_asyncio
from datetime import datetime
from typing import List, Dict, Optional, Any, Union
from fastapi import HTTPException
from ib_async.contract import Contract, Stock, Option
from ib_async.order import Order, LimitOrder, MarketOrder, StopOrder
from ib_async.objects import TagValue


from decimal import Decimal
from dotenv import load_dotenv

# Import ib_async components needed for event handling
from ib_async import IB, Trade 
from ib_async.order import OrderState as IBOrderState # Corrected import for OrderState
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type


try:
    nest_asyncio.apply()
except ValueError as e:
    print(f"nest_asyncio.apply() skipped: {e}")

# Configure logging
logger = logging.getLogger('ibkr-service')

load_dotenv(override=True)

class IBKRConnectionError(Exception):
    """Exception raised for IBKR connection issues"""
    pass

class IBKRTimeoutError(Exception):
    """Exception raised for IBKR timeout issues"""
    pass

class IBKRDataError(Exception):
    """Exception raised for IBKR data issues"""
    pass

class IBKROrderError(Exception):
    """Exception raised for IBKR order issues"""
    pass

class IBKRService:
    def __init__(self):
        self.ib = IB()
        self.order_management_service_delegate: Optional['OrderManagementService'] = None # Delegate for order management events
        print(f"Created IB object: {type(self.ib)}")
        print(f"IB object has placeOrder: {hasattr(self.ib, 'placeOrder')}")
        print(f"IB object has placeOrderAsync: {hasattr(self.ib, 'placeOrderAsync')}")
        self.host = os.getenv("IBKR_HOST", "127.0.0.1")
        raw_port = os.getenv("IBKR_PORT", "7496").split()[0]
        self.port = int(raw_port)
        self.client_id = int(os.getenv("IBKR_CLIENT_ID", "0"))
        self.connected = False
        self.connection_time = None
        self.connection_attempts = 0
        self.max_connection_attempts = 3
        self.reconnect_delay = 5  # seconds
        self.last_error = None
        self.readonly = True # Default to readonly

        self._register_event_handlers()
        self._next_order_id = 1

    async def initialize(self):
        """Initialize the service without connecting to TWS"""
        logger.info("Initializing IBKR service")
        # Reset connection state
        self.connection_attempts = 0
        self.last_error = None
        return True

    def set_order_management_service(self, oms_delegate: 'OrderManagementService'):
        self.order_management_service_delegate = oms_delegate

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type(IBKRConnectionError)
    ) # The client_id_override parameter was removed as it's set on the instance by the tool
    async def connect(self, force: bool = False, readonly: Optional[bool] = None): # Added readonly parameter
        """
        Connect to IBKR TWS or Gateway with retry logic

        Args:
            force: If True, force reconnection even if already connected
        """
        # If already connected and not forcing reconnection, return
        if self.connected and not force:
            logger.debug("Already connected to IBKR")
            return True

        # If forcing reconnection, disconnect first
        if self.connected and force:
            await self.disconnect()

        # Update readonly setting if provided
        if readonly is not None:
            self.readonly = readonly

        self.connection_attempts += 1

        try:
            logger.info(f"Connecting to IBKR at {self.host}:{self.port} (attempt {self.connection_attempts})")
            await self.ib.connectAsync(
                host=self.host, # Use self.host
                port=self.port,
                clientId=self.client_id, # Uses the instance's client_id
                readonly=self.readonly,
                timeout=20
            )

            # Verify connection is working by making a simple request
            await self.ib.reqCurrentTimeAsync()

            self.connected = True
            self.connection_time = datetime.now()
            self.connection_attempts = 0  # Reset counter on success
            logger.info(f"Connected to IBKR at {self.host}:{self.port}")
            return True

        except asyncio.TimeoutError as e:
            self.connected = False
            self.last_error = str(e)
            logger.error(f"Timeout connecting to IBKR: {str(e)}")
            raise IBKRTimeoutError(f"Timeout connecting to IBKR: {str(e)}")

        except Exception as e:
            self.connected = False
            self.last_error = str(e)
            logger.error(f"Failed to connect to IBKR: {str(e)}")

            # Check if TWS is not running or not connected to IB servers
            if "Not connected" in str(e) or "Connection refused" in str(e):
                raise IBKRConnectionError(f"TWS/Gateway not running or not accessible: {str(e)}")
            else:
                raise IBKRConnectionError(f"Failed to connect to IBKR: {str(e)}")

    def _register_event_handlers(self):
        """Register handlers for ib_async events"""
        self.ib.orderStatusEvent += self._on_order_status
        self.ib.openOrderEvent += self._on_open_order # Correct: _on_open_order handles individual open orders
        # self.ib.openOrderEndEvent is not available in ib_async 1.0.3 as per docs and error.
        # The _on_open_order_end method and corresponding handle_open_order_end in OrderManagementService
        # will not be called by this event. reqAllOpenOrdersAsync() completion implies all orders are fetched.
        # Add other relevant handlers here, e.g., self.ib.execDetailsEvent += self._on_exec_details
        logger.debug("IBKRService event handlers registered")

    def _on_order_status(self, trade: Trade):
        """Handle order status updates from ib_async and delegate"""
        logger.debug(f"Received orderStatusEvent for OrderID {trade.order.orderId}, Status: {trade.orderStatus.status}")
        if self.order_management_service_delegate:
            self.order_management_service_delegate.handle_order_status_update(
                order_id=trade.order.orderId,
                status_str=trade.orderStatus.status,
                filled=float(trade.orderStatus.filled), # ib_async uses Decimal, convert to float
                remaining=float(trade.orderStatus.remaining), # ib_async uses Decimal, convert to float
                avg_fill_price=trade.orderStatus.avgFillPrice,
                perm_id=trade.order.permId,
                parent_id=trade.order.parentId,
                last_fill_price=trade.orderStatus.lastFillPrice,
                client_id=trade.order.clientId,
                why_held=trade.orderStatus.whyHeld,
                mkt_cap_price=getattr(trade.orderStatus, 'mktCapPrice', None) # mktCapPrice might not always be present
            )

    def _on_open_order(self, trade: Trade):
        """Handle open order information from ib_async and delegate"""
        logger.debug(f"Received openOrderEvent for OrderID {trade.order.orderId}, Symbol: {trade.contract.symbol}")
        if self.order_management_service_delegate:
            self.order_management_service_delegate.handle_open_order(trade.order.orderId, trade.contract, trade.order, trade.orderStatus)

    def _on_open_order_end(self):
        """Handle end of open order list from ib_async and delegate"""
        # This method is unlikely to be called if openOrderEndEvent is not available
        # in the ib_async version being used.
        logger.debug("Received _on_open_order_end call (potentially unused if no openOrderEndEvent)")
        if self.order_management_service_delegate and hasattr(self.order_management_service_delegate, 'handle_open_order_end'):
             self.order_management_service_delegate.handle_open_order_end()

    async def disconnect(self):
        """Disconnect from IBKR asynchronously"""
        if self.connected:
            logger.info("Disconnecting from IBKR")
            # Use asyncio.to_thread for potentially blocking operations
            await asyncio.to_thread(self.ib.disconnect)
            self.connected = False
            logger.info("Disconnected from IBKR")
            return True
        return False

    async def check_connection(self):
        """Check if connection is still valid and reconnect if needed"""
        if not self.connected:
            logger.info("Not connected to IBKR, attempting to connect")
            return await self.connect()

        try:
            # Try a simple request to verify connection
            logger.debug("Connection to IBKR is valid")
            return True
        except Exception as e:
            print(f"=== DEBUG: reqCurrentTimeAsync failed: {e} ===")
            logger.warning(f"Connection check failed: {str(e)}, reconnecting...")
            self.connected = False
            return await self.connect(force=True)

    async def get_portfolio(self) -> List[Dict]:
        """Get portfolio positions with connection management"""
        try:
            await self.check_connection()

            logger.info("Fetching portfolio positions")
            positions = await asyncio.to_thread(self.ib.positions)

            # Format portfolio data
            portfolio_data = []
            for pos in positions:
                position_data = {
                    'symbol': pos.contract.symbol,
                    'secType': pos.contract.secType,
                    'exchange': pos.contract.exchange,
                    'currency': pos.contract.currency,
                    'position': pos.position,
                    'avgCost': pos.avgCost,
                    'marketPrice': getattr(pos, 'marketPrice', 0),
                    'marketValue': getattr(pos, 'marketValue', 0),
                    'unrealizedPNL': getattr(pos, 'unrealizedPNL', 0),
                    'realizedPNL': getattr(pos, 'realizedPNL', 0)
                }
                portfolio_data.append(position_data)

            logger.info(f"Retrieved {len(portfolio_data)} portfolio positions")
            return portfolio_data

        except Exception as e:
            logger.error(f"Failed to get portfolio: {str(e)}")
            raise IBKRDataError(f"Failed to get portfolio: {str(e)}")

    async def get_account_summary(self, tags: List[str] = None) -> Dict:
        """Get account summary with connection management"""
        try:
            await self.check_connection()

            if tags is None:
                tags = ["NetLiquidation", "TotalCashValue", "SettledCash",
                        "AccruedCash", "BuyingPower", "EquityWithLoanValue",
                        "PreviousDayEquityWithLoanValue", "GrossPositionValue",
                        "RegTMargin", "InitMarginReq", "MaintMarginReq",
                        "AvailableFunds", "ExcessLiquidity", "Cushion",
                        "FullInitMarginReq", "FullMaintMarginReq", "FullAvailableFunds",
                        "FullExcessLiquidity", "LookAheadNextChange",
                        "LookAheadInitMarginReq", "LookAheadMaintMarginReq",
                        "LookAheadAvailableFunds", "LookAheadExcessLiquidity",
                        "HighestSeverity", "DayTradesRemaining", "Leverage"]

            logger.info("Fetching account summary")
            account_summary = await self.ib.accountSummaryAsync()

            # Convert to dictionary format
            summary_dict = {}
            for item in account_summary:
                summary_dict[item.tag] = {
                    'value': item.value,
                    'currency': item.currency
                }

            logger.info("Retrieved account summary")
            return summary_dict

        except Exception as e:
            logger.error(f"Failed to get account summary: {str(e)}")
            raise IBKRDataError(f"Failed to get account summary: {str(e)}")

    async def fetch_portfolio_details(self) -> Dict:
        """Fetch portfolio details from IBKR (legacy method)"""
        try:
            portfolio_data = await self.get_portfolio()
            account_summary = await self.get_account_summary()

            return {
                'positions': portfolio_data,
                'account_summary': account_summary
            }

        except Exception as e:
            logger.error(f"Failed to fetch portfolio details: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to fetch portfolio: {str(e)}"
            )

    async def create_contract(self, symbol: str, sec_type: str = "STK", 
                             exchange: str = "SMART", currency: str = "USD", **kwargs) -> Contract:
        """
        Create a contract object for trading
        
        Args:
            symbol (str): The ticker symbol
            sec_type (str): Security type (STK, OPT, FUT, CASH, etc.)
            exchange (str): Exchange (default: SMART)
            currency (str): Currency (default: USD)
            **kwargs: Additional contract parameters
                - For options: expiry, strike, right
                - For futures: expiry, multiplier
                
        Returns:
            Contract: IB contract object
        """
        try:
            contract = Contract()
            contract.symbol = symbol
            contract.secType = sec_type
            contract.exchange = exchange
            contract.currency = currency
            
            # Add security-specific parameters
            if sec_type == "OPT":
                if not all(k in kwargs for k in ['expiry', 'strike', 'right']):
                    raise ValueError("Options require expiry, strike, and right parameters")
                contract.lastTradeDateOrContractMonth = kwargs['expiry']
                contract.strike = kwargs['strike']
                contract.right = kwargs['right']
            
            elif sec_type == "FUT":
                if 'expiry' not in kwargs:
                    raise ValueError("Futures require expiry parameter")
                contract.lastTradeDateOrContractMonth = kwargs['expiry']
                if 'multiplier' in kwargs:
                    contract.multiplier = kwargs['multiplier']
            
            # Add any other parameters
            for key, value in kwargs.items():
                if key not in ['expiry', 'strike', 'right', 'multiplier'] and hasattr(contract, key):
                    setattr(contract, key, value)
            
            logger.debug(f"Created contract: {contract}")
            return contract
        
        except Exception as e:
            logger.error(f"Failed to create contract: {str(e)}")
            raise IBKROrderError(f"Failed to create contract: {str(e)}")

    async def create_order(self, order_type: str, symbol: str, action: str, 
                         quantity: Union[int, float, Decimal], account: str, # Added account parameter
                         price: Optional[float] = None, **kwargs) -> Dict:
        """
        Creates and places an order with Interactive Brokers TWS.
        
        Args:
            order_type (str): Type of order ("auction", "adaptive", "bracket")
            symbol (str): The ticker symbol
            action (str): "BUY" or "SELL"
            quantity: Number of shares/contracts
            account (str): The account ID to place the order in
            price: Primary price point for the order
            **kwargs: Additional parameters based on order type
                - For adaptive: priority ("Urgent", "Normal", "Patient")
                - For bracket: profit_price, stop_price
                - For all: exchange (defaults to "SMART")
                
        Returns:
            dict: Order status information including order ID
        
        Raises:
            ConnectionError: If not connected to TWS
            ValueError: If invalid parameters are provided
            IBKROrderError: For TWS API specific errors
        """
        try:
            # Check connection status
            await self.check_connection()
            if self.readonly:
                self.readonly = False
                # Reconnect with the new readonly setting
                await self.disconnect()
                await self.connect(force=True)
            print("=== DEBUG: Readonly check passed ===")
            
            # Create contract
            sec_type = kwargs.get("sec_type", "STK")
            exchange = kwargs.get("exchange", "SMART")
            currency = kwargs.get("currency", "USD")
            
            contract = await self.create_contract(
                symbol=symbol, 
                sec_type=sec_type,
                exchange=exchange, 
                currency=currency,
                **{k: v for k, v in kwargs.items() if k not in ['exchange', 'currency', 'sec_type']}
            )
                        
            # Convert quantity to Decimal if it's not already
            if not isinstance(quantity, Decimal):
                quantity = Decimal(str(quantity))
            
            # Handle different order types
            if order_type.lower() == "auction":
                order = Order()
                order.action = action
                order.orderType = "MTL"  # Market-to-limit order
                order.totalQuantity = quantity
                if price:
                    order.lmtPrice = float(price) # Ensure float type
                order.tif = "AUC"  # Auction time-in-force
                order.account = account # Set account
                
                # Place the order
                logger.debug(f"IB object type: {type(self.ib)}")
                logger.debug(f"Is IB connected: {self.ib.isConnected()}")

                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed auction order: {trade.order.orderId} for {symbol}")
                
                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Auction order placed for {symbol}"
                }
                
            elif order_type.lower() == "adaptive":
                order = Order()
                order.action = action
                order.orderType = "LMT"
                order.totalQuantity = quantity
                order.lmtPrice = float(price) # Ensure float type
                order.account = account # Set account

                # Set adaptive algorithm parameters
                # Note: Adaptive algo requires LMT or STP LMT order types
                order.algoStrategy = "Adaptive"
                order.algoParams = []
                priority = kwargs.get("priority", "Normal")
                order.algoParams.append(TagValue("adaptivePriority", priority))
                
                # Place the order
                logger.debug(f"IB object attributes before adaptive order: {dir(self.ib)}")
                logger.debug(f"Is IB connected: {self.ib.isConnected()}")

                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed adaptive order: {trade.order.orderId} for {symbol}")
                
                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Adaptive order placed for {symbol}"
                }
                
            elif order_type.lower() == "bracket":
                profit_price = kwargs.get("profit_price")
                stop_price = kwargs.get("stop_price")
                
                if profit_price is None or stop_price is None:
                    raise ValueError("Bracket orders require profit_price and stop_price parameters")
                
                parent_order_id = self.ib.client.getReqId()
                parent_order = Order()
                parent_order.orderId = parent_order_id
                parent_order.action = action
                parent_order.orderType = "LMT"
                parent_order.totalQuantity = quantity
                parent_order.lmtPrice = float(price) # Ensure float type
                parent_order.account = account # Set account)
                parent_order.transmit = False
                
                take_profit_id = parent_order_id + 1
                take_profit = Order() #Use base Order for bracket legs
                take_profit.orderId = take_profit_id
                take_profit.action = "SELL" if action == "BUY" else "BUY"
                take_profit.orderType = "LMT"
                take_profit.totalQuantity = quantity
                take_profit.lmtPrice = profit_price
                take_profit.account = account # Set account for take profit
                take_profit.parentId = parent_order_id
                take_profit.transmit = False
                
                stop_loss_id = parent_order_id + 2
                stop_loss = Order() #Use base Order for bracket legs
                stop_loss.orderId = stop_loss_id
                stop_loss.action = "SELL" if action == "BUY" else "BUY" #Action is opposite of parent
                stop_loss.orderType = "STP"
                stop_loss.totalQuantity = quantity
                stop_loss.auxPrice = stop_price
                stop_loss.account = account # Set account for stop loss
                stop_loss.parentId = parent_order_id
                stop_loss.transmit = True
                
                logger.debug(f"IB object attributes before bracket order: {dir(self.ib)}")
                logger.debug(f"Is IB connected: {self.ib.isConnected()}")

                parent_trade = self.ib.placeOrder(contract, parent_order)
                take_profit_trade = self.ib.placeOrder(contract, take_profit)
                stop_loss_trade = self.ib.placeOrder(contract, stop_loss)
                
                logger.info(f"Placed bracket order: {parent_order_id} for {symbol}")
                
                return {
                    "status": "success",
                    "orderId": parent_order_id,
                    "message": f"Bracket order placed for {symbol}",
                    "profit_orderId": take_profit_id,
                    "stop_orderId": stop_loss_id
                }
            
            else:
                raise ValueError(f"Unknown order type: {order_type}")
                
        except Exception as e:
            logger.error(f"Failed to create order: {str(e)}")
            raise IBKROrderError(f"Failed to create order: {str(e)}")

    async def place_auction_order(self, symbol: str, action: str, quantity: Union[int, float, Decimal], 
                               price: float, account: str, exchange: str = "SMART") -> Dict:
        """ # Added account parameter
        Place an Auction order
        
        An Auction order is entered during pre-market for execution at the 
        Calculated Opening Price (COP).
        
        Args:
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            price: Limit price
            account: The account ID to place the order in
            exchange: Exchange (default: SMART)
            
        Returns:
            dict: Order status information
        """
        logger.info(f"Placing auction order for {symbol}: {action} {quantity} @ {price}")
        return await self.create_order("auction", symbol, action, quantity, account, price, exchange=exchange) #Pass account

    async def place_adaptive_order(self, symbol: str, action: str, quantity: Union[int, float, Decimal], 
                                price: float, account: str, priority: str = "Normal", exchange: str = "SMART") -> Dict:
        """
        Place an Adaptive Algorithm order
        
        The Adaptive Algo combines IB's Smartrouting with user-defined priority settings
        for better execution prices on average than regular limit or market orders.
        
        Args:
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            price: Limit price
            account: The account ID to place the order in
            priority: Priority setting ("Urgent", "Normal", or "Patient")
            exchange: Exchange (default: SMART)
            
        Returns:
            dict: Order status information
        """
        try:
            logger.info(f"Placing adaptive order for {symbol}: {action} {quantity} @ {price} with {priority} priority")
            return await self.create_order("adaptive", symbol, action, quantity, account, price, priority=priority, exchange=exchange) #Pass account
        except Exception as e:
            # Capture the full stack trace
            import traceback
            print("=== FULL EXCEPTION TRACEBACK ===")
            print(f"Exception type: {type(e).__name__}")
            print(f"Exception message: {str(e)}")
            print("Full traceback:")
            traceback.print_exc()
            print("=== END EXCEPTION TRACEBACK ===")
            
            # Re-raise the exception so the MCP layer can handle it
            raise

    async def place_bracket_order(self, symbol: str, action: str, quantity: Union[int, float, Decimal], 
                               entry_price: float, profit_price: float, stop_price: float, # Added account parameter
                               account: str, exchange: str = "SMART") -> Dict:
        """
        Place a Bracket Order
        
        Bracket Orders help limit loss and lock in profit by "bracketing" an order with
        two opposite-side orders. A BUY order is bracketed by a high-side sell limit order
        and a low-side sell stop order.
        
        Args:
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            entry_price: Entry order price
            profit_price: Profit target price
            stop_price: Stop loss price
            account: The account ID to place the order in
            exchange: Exchange (default: SMART)
            
        Returns:
            dict: Order status information
        """
        logger.info(f"Placing bracket order for {symbol}: {action} {quantity} @ {entry_price} "
                   f"with profit at {profit_price} and stop at {stop_price}")
        return await self.create_order("bracket", symbol, action, quantity, entry_price,
                                    account, profit_price=profit_price, stop_price=stop_price, 
                                    exchange=exchange)

    async def get_market_data(self, symbol: str, exchange: str = "SMART") -> Dict:
        """
        Get market data for a symbol
        
        Args:
            symbol: The ticker symbol
            exchange: The exchange (default: SMART)
            
        Returns:
            dict: Market data
        """
        try:
            await self.check_connection()
            
            # Create contract
            contract = await self.create_contract(symbol, exchange=exchange)
            
            # Request market data
            logger.info(f"Requesting market data for {symbol}")
            tickers = await self.ib.reqTickersAsync(contract)
            
            if not tickers:
                logger.warning(f"No market data received for {symbol}")
                return {
                    "status": "error",
                    "message": f"No market data available for {symbol}"
                }
            
            ticker = tickers[0]
            
            # Format market data
            market_data = {
                "symbol": symbol,
                "exchange": exchange,
                "bid": ticker.bid,
                "ask": ticker.ask,
                "last": ticker.last,
                "close": ticker.close,
                "volume": ticker.volume,
                "high": ticker.high,
                "low": ticker.low,
                "open": ticker.open,
                "time": datetime.now().isoformat()
            }
            
            logger.info(f"Retrieved market data for {symbol}")
            return {
                "status": "success",
                "data": market_data
            }
            
        except Exception as e:
            logger.error(f"Failed to get market data: {str(e)}")
            raise IBKRDataError(f"Failed to get market data: {str(e)}")

    async def check_connection_status(self) -> Dict:
        """
        Check the current connection status to TWS/IB Gateway
        
        Returns:
            dict: Connection status information
        """
        try:
            is_connected = await self.check_connection()
            
            status = {
                "status": "success",
                "connected": is_connected,
                "connectionTime": self.connection_time.isoformat() if self.connection_time else None,
                "clientId": self.client_id,
                "host": self.host,
                "port": self.port
            }
            
            return status
        except Exception as e:
            logger.error(f"Failed to check connection status: {str(e)}")
            return {
                "status": "error",
                "connected": False,
                "message": str(e)
            }

    async def search_contracts(self, pattern: str) -> Dict:
        """
        Search for stock contracts matching a pattern
        
        Args:
            pattern: Search pattern (e.g., "AAPL" or "Apple")
            
        Returns:
            dict: List of matching contracts
        """
        try:
            await self.check_connection()
            
            # Create a contract with the pattern
            contract = Contract()
            contract.symbol = pattern
            contract.secType = "STK"
            contract.exchange = "SMART"
            contract.currency = "USD"
            
            # Request matching contracts
            logger.info(f"Searching for contracts matching '{pattern}'")
            contracts = await self.ib.reqContractDetailsAsync(contract)
            
            # Format results
            results = []
            for details in contracts:
                contract_info = {
                    "symbol": details.contract.symbol,
                    "name": details.longName,
                    "exchange": details.contract.exchange,
                    "currency": details.contract.currency,
                    "secType": details.contract.secType,
                    "conId": details.contract.conId
                }
                results.append(contract_info)
            
            logger.info(f"Found {len(results)} contracts matching '{pattern}'")
            return {
                "status": "success",
                "contracts": results
            }
            
        except Exception as e:
            logger.error(f"Failed to search contracts: {str(e)}")
            raise IBKRDataError(f"Failed to search contracts: {str(e)}")

    def _map_timeframe_to_duration(self, timeframe: str) -> tuple:
        """Maps user-friendly timeframe to TWS API duration and bar size"""
        mapping = {
            "1d": ("1 D", "5 mins"),
            "5d": ("5 D", "1 hour"),
            "1m": ("1 M", "1 day"),
            "3m": ("3 M", "1 day"),
            "6m": ("6 M", "1 day"),
            "1y": ("1 Y", "1 day"),
            "5y": ("5 Y", "1 week")
        }
        return mapping[timeframe]
        
    async def get_historical_data(self, symbol: str, timeframe: str) -> List[Dict]:
        """
        Get historical price data for a symbol
        
        Args:
            symbol: The ticker symbol
            timeframe: Timeframe (1d, 5d, 1m, 3m, 6m, 1y, 5y)
            
        Returns:
            List of historical data bars
        """
        try:
            await self.check_connection()
            
            # Map timeframe to TWS API duration and bar size
            duration, bar_size = self._map_timeframe_to_duration(timeframe)
            
            # Create contract
            contract = await self.create_contract(symbol)
            
            # Request historical data
            logger.info(f"Requesting historical data for {symbol} with timeframe {timeframe}")
            bars = await self.ib.reqHistoricalDataAsync(
                contract=contract,
                endDateTime='',  # Empty string means "now"
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow='TRADES',
                useRTH=True
            )
            
            # Format the data
            historical_data = []
            for bar in bars:
                historical_data.append({
                    'date': bar.date,
                    'open': bar.open,
                    'high': bar.high,
                    'low': bar.low,
                    'close': bar.close,
                    'volume': bar.volume
                })
            
            logger.info(f"Retrieved {len(historical_data)} historical data bars for {symbol}")
            return historical_data
            
        except Exception as e:
            logger.error(f"Failed to get historical data: {str(e)}")
            raise IBKRDataError(f"Failed to get historical data: {str(e)}")

# Create a singleton instance
ibkr_service = IBKRService()