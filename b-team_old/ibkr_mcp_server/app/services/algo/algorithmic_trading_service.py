"""
Algorithmic Trading Service

This service provides advanced algorithmic trading capabilities including
multiple trading strategies, risk management, and trade execution optimization.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union, Callable
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
import numpy as np
from dataclasses import dataclass, field

from ibkr_mcp_server.app.services.ibkr_service import IBKRService, IBKROrderError
from ibkr_mcp_server.app.services.order_management_service import OrderManagementService
from ib_async import Contract, Order, TagValue

logger = logging.getLogger('algo-trading-service')


class StrategyType(Enum):
    """Supported algorithmic trading strategies"""
    STATISTICAL_ARBITRAGE = "statistical_arbitrage"
    MARKET_MAKING = "market_making"
    TREND_FOLLOWING = "trend_following"
    MEAN_REVERSION = "mean_reversion"
    PAIRS_TRADING = "pairs_trading"
    MOMENTUM = "momentum"
    VWAP = "vwap"
    TWAP = "twap"
    SMART_ORDER_ROUTING = "smart_order_routing"
    ICEBERG = "iceberg"


@dataclass
class StrategyParameters:
    """Base parameters for all strategies"""
    strategy_type: StrategyType
    symbols: List[str]
    max_position_size: Decimal
    risk_limit: Decimal
    account: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    custom_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StrategyResult:
    """Result of strategy execution"""
    strategy_id: str
    status: str
    orders_placed: List[int]
    pnl: Optional[Decimal] = None
    metrics: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)


class AlgorithmicTradingService:
    """
    Main service for algorithmic trading strategies
    
    This service coordinates between the IBKR service, order management,
    and various trading strategies to provide sophisticated trading capabilities.
    """
    
    def __init__(self, ibkr_service: IBKRService, oms: OrderManagementService):
        self.ibkr_service = ibkr_service
        self.oms = oms
        self._active_strategies: Dict[str, asyncio.Task] = {}
        self._strategy_results: Dict[str, StrategyResult] = {}
        self._strategy_callbacks: Dict[str, Callable] = {}
        
        # Strategy implementations
        self._strategy_handlers = {
            StrategyType.STATISTICAL_ARBITRAGE: self._execute_stat_arb,
            StrategyType.MARKET_MAKING: self._execute_market_making,
            StrategyType.TREND_FOLLOWING: self._execute_trend_following,
            StrategyType.MEAN_REVERSION: self._execute_mean_reversion,
            StrategyType.PAIRS_TRADING: self._execute_pairs_trading,
            StrategyType.MOMENTUM: self._execute_momentum,
            StrategyType.VWAP: self._execute_vwap,
            StrategyType.TWAP: self._execute_twap,
            StrategyType.SMART_ORDER_ROUTING: self._execute_smart_routing,
            StrategyType.ICEBERG: self._execute_iceberg
        }
        
    async def execute_strategy(self, parameters: StrategyParameters) -> StrategyResult:
        """
        Execute a trading strategy with given parameters
        
        Args:
            parameters: Strategy configuration parameters
            
        Returns:
            StrategyResult with execution details
        """
        strategy_id = f"{parameters.strategy_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # Validate parameters
            await self._validate_strategy_parameters(parameters)
            
            # Get strategy handler
            handler = self._strategy_handlers.get(parameters.strategy_type)
            if not handler:
                raise ValueError(f"Unsupported strategy type: {parameters.strategy_type}")
            
            # Create and start strategy task
            task = asyncio.create_task(handler(strategy_id, parameters))
            self._active_strategies[strategy_id] = task
            
            # Wait for completion or timeout
            timeout = self._calculate_strategy_timeout(parameters)
            result = await asyncio.wait_for(task, timeout=timeout)
            
            self._strategy_results[strategy_id] = result
            return result
            
        except asyncio.TimeoutError:
            logger.error(f"Strategy {strategy_id} timed out")
            return StrategyResult(
                strategy_id=strategy_id,
                status="timeout",
                orders_placed=[],
                errors=[f"Strategy execution timed out after {timeout} seconds"]
            )
        except Exception as e:
            logger.error(f"Strategy execution error: {str(e)}")
            return StrategyResult(
                strategy_id=strategy_id,
                status="error",
                orders_placed=[],
                errors=[str(e)]
            )
        finally:
            # Clean up
            self._active_strategies.pop(strategy_id, None)
    
    async def stop_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """Stop a running strategy"""
        if strategy_id in self._active_strategies:
            task = self._active_strategies[strategy_id]
            task.cancel()
            
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"Strategy {strategy_id} cancelled successfully")
            
            return {
                "status": "success",
                "message": f"Strategy {strategy_id} stopped",
                "result": self._strategy_results.get(strategy_id)
            }
        else:
            return {
                "status": "error",
                "message": f"Strategy {strategy_id} not found or not active"
            }
    
    async def get_active_strategies(self) -> List[Dict[str, Any]]:
        """Get list of currently active strategies"""
        active = []
        for strategy_id, task in self._active_strategies.items():
            active.append({
                "strategy_id": strategy_id,
                "running": not task.done(),
                "result": self._strategy_results.get(strategy_id)
            })
        return active
    
    # Strategy Implementations
    
    async def _execute_stat_arb(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """
        Statistical Arbitrage Strategy
        
        Identifies and exploits pricing inefficiencies between related securities
        """
        logger.info(f"Executing statistical arbitrage strategy: {strategy_id}")
        orders_placed = []
        errors = []
        
        try:
            # Get market data for symbols
            market_data = {}
            for symbol in params.symbols:
                data = await self.ibkr_service.get_market_data(symbol)
                if data["status"] == "success":
                    market_data[symbol] = data["data"]
            
            # Calculate statistical relationships
            if len(market_data) >= 2:
                symbols = list(market_data.keys())
                
                # Simple z-score calculation for demonstration
                prices = [market_data[s].get("data", {}).get("last", 0) for s in symbols]
                if all(p > 0 for p in prices):
                    ratio = prices[0] / prices[1]
                    mean_ratio = params.custom_params.get("mean_ratio", ratio)
                    std_ratio = params.custom_params.get("std_ratio", 0.02)
                    
                    z_score = (ratio - mean_ratio) / std_ratio if std_ratio > 0 else 0
                    
                    # Trading logic based on z-score
                    threshold = params.custom_params.get("z_threshold", 2.0)
                    
                    if abs(z_score) > threshold:
                        # Place trades
                        quantity = min(
                            float(params.max_position_size),
                            params.custom_params.get("trade_size", 100)
                        )
                        
                        if z_score > threshold:
                            # Sell first symbol, buy second
                            order1 = await self.ibkr_service.place_adaptive_order(
                                symbol=symbols[0],
                                action="SELL",
                                quantity=quantity,
                                price=prices[0] * 0.999,  # Slightly below market
                                account=params.account
                            )
                            if order1["status"] == "success":
                                orders_placed.append(order1["orderId"])
                            
                            order2 = await self.ibkr_service.place_adaptive_order(
                                symbol=symbols[1],
                                action="BUY",
                                quantity=quantity,
                                price=prices[1] * 1.001,  # Slightly above market
                                account=params.account
                            )
                            if order2["status"] == "success":
                                orders_placed.append(order2["orderId"])
                        
                        elif z_score < -threshold:
                            # Buy first symbol, sell second
                            order1 = await self.ibkr_service.place_adaptive_order(
                                symbol=symbols[0],
                                action="BUY",
                                quantity=quantity,
                                price=prices[0] * 1.001,
                                account=params.account
                            )
                            if order1["status"] == "success":
                                orders_placed.append(order1["orderId"])
                            
                            order2 = await self.ibkr_service.place_adaptive_order(
                                symbol=symbols[1],
                                action="SELL",
                                quantity=quantity,
                                price=prices[1] * 0.999,
                                account=params.account
                            )
                            if order2["status"] == "success":
                                orders_placed.append(order2["orderId"])
            
            return StrategyResult(
                strategy_id=strategy_id,
                status="completed",
                orders_placed=orders_placed,
                metrics={"z_score": z_score if 'z_score' in locals() else None},
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"Statistical arbitrage error: {str(e)}")
            errors.append(str(e))
            return StrategyResult(
                strategy_id=strategy_id,
                status="error",
                orders_placed=orders_placed,
                errors=errors
            )
    
    async def _execute_market_making(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """
        Market Making Strategy
        
        Provides liquidity by placing both buy and sell orders around the current market price
        """
        logger.info(f"Executing market making strategy: {strategy_id}")
        orders_placed = []
        errors = []
        
        try:
            for symbol in params.symbols:
                # Get current market data
                market_data = await self.ibkr_service.get_market_data(symbol)
                if market_data["status"] != "success":
                    errors.append(f"Failed to get market data for {symbol}")
                    continue
                
                data = market_data["data"]["data"]
                mid_price = (data.get("bid", 0) + data.get("ask", 0)) / 2
                
                if mid_price <= 0:
                    errors.append(f"Invalid market price for {symbol}")
                    continue
                
                # Calculate spreads
                spread_bps = params.custom_params.get("spread_bps", 10)  # basis points
                spread = mid_price * spread_bps / 10000
                
                # Place buy and sell orders
                quantity = min(
                    float(params.max_position_size),
                    params.custom_params.get("order_size", 100)
                )
                
                # Buy order
                buy_price = mid_price - spread
                buy_order = await self.ibkr_service.place_adaptive_order(
                    symbol=symbol,
                    action="BUY",
                    quantity=quantity,
                    price=buy_price,
                    account=params.account,
                    priority="Patient"  # Market makers typically use patient priority
                )
                if buy_order["status"] == "success":
                    orders_placed.append(buy_order["orderId"])
                
                # Sell order
                sell_price = mid_price + spread
                sell_order = await self.ibkr_service.place_adaptive_order(
                    symbol=symbol,
                    action="SELL",
                    quantity=quantity,
                    price=sell_price,
                    account=params.account,
                    priority="Patient"
                )
                if sell_order["status"] == "success":
                    orders_placed.append(sell_order["orderId"])
            
            return StrategyResult(
                strategy_id=strategy_id,
                status="completed",
                orders_placed=orders_placed,
                metrics={
                    "spread_bps": spread_bps,
                    "symbols_traded": len([o for o in orders_placed if o])
                },
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"Market making error: {str(e)}")
            errors.append(str(e))
            return StrategyResult(
                strategy_id=strategy_id,
                status="error",
                orders_placed=orders_placed,
                errors=errors
            )
    
    async def _execute_vwap(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """
        Volume Weighted Average Price (VWAP) Strategy
        
        Executes orders to achieve a price close to the day's VWAP
        """
        logger.info(f"Executing VWAP strategy: {strategy_id}")
        orders_placed = []
        errors = []
        
        try:
            symbol = params.symbols[0]  # VWAP typically focuses on one symbol
            total_quantity = float(params.custom_params.get("total_quantity", 1000))
            
            # Get historical intraday data to calculate VWAP target
            historical_data = await self.ibkr_service.get_historical_data(symbol, "1d")
            
            if historical_data:
                # Calculate VWAP
                total_volume = sum(bar.get("volume", 0) for bar in historical_data)
                
                if total_volume > 0:
                    # Distribute orders throughout the day based on historical volume pattern
                    slices = min(params.custom_params.get("num_slices", 10), 20)
                    slice_quantity = total_quantity / slices
                    
                    # Place orders at intervals
                    for i in range(slices):
                        # Get current market price
                        market_data = await self.ibkr_service.get_market_data(symbol)
                        if market_data["status"] == "success":
                            current_price = market_data["data"]["data"].get("last", 0)
                            
                            if current_price > 0:
                                # Place adaptive order with VWAP algo
                                order = await self.ibkr_service.create_order(
                                    order_type="adaptive",
                                    symbol=symbol,
                                    action=params.custom_params.get("action", "BUY"),
                                    quantity=slice_quantity,
                                    account=params.account,
                                    price=current_price * 1.001,  # Slight premium for completion
                                    priority="Normal"
                                )
                                
                                if order["status"] == "success":
                                    orders_placed.append(order["orderId"])
                                
                                # Wait between slices
                                if i < slices - 1:
                                    await asyncio.sleep(params.custom_params.get("slice_interval", 60))
            
            return StrategyResult(
                strategy_id=strategy_id,
                status="completed",
                orders_placed=orders_placed,
                metrics={
                    "total_quantity": total_quantity,
                    "slices": slices if 'slices' in locals() else 0
                },
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"VWAP strategy error: {str(e)}")
            errors.append(str(e))
            return StrategyResult(
                strategy_id=strategy_id,
                status="error",
                orders_placed=orders_placed,
                errors=errors
            )
    
    async def _execute_trend_following(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """Trend Following Strategy - trades in direction of established trends"""
        logger.info(f"Executing trend following strategy: {strategy_id}")
        # Implementation would include moving average crossovers, momentum indicators, etc.
        return await self._execute_momentum(strategy_id, params)  # Similar implementation
    
    async def _execute_mean_reversion(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """Mean Reversion Strategy - trades expecting price to revert to mean"""
        logger.info(f"Executing mean reversion strategy: {strategy_id}")
        orders_placed = []
        errors = []
        
        try:
            for symbol in params.symbols:
                # Get historical data
                historical_data = await self.ibkr_service.get_historical_data(symbol, "1m")
                
                if len(historical_data) > 20:
                    # Calculate simple moving average
                    prices = [bar.get("close", 0) for bar in historical_data[-20:]]
                    sma = np.mean(prices)
                    std_dev = np.std(prices)
                    
                    current_price = prices[-1]
                    z_score = (current_price - sma) / std_dev if std_dev > 0 else 0
                    
                    # Trade if price deviates significantly from mean
                    if abs(z_score) > params.custom_params.get("z_threshold", 2.0):
                        action = "SELL" if z_score > 0 else "BUY"
                        quantity = min(
                            float(params.max_position_size),
                            params.custom_params.get("trade_size", 100)
                        )
                        
                        # Place bracket order for mean reversion
                        target_price = sma
                        stop_price = current_price * (1.02 if action == "SELL" else 0.98)
                        
                        order = await self.ibkr_service.place_bracket_order(
                            symbol=symbol,
                            action=action,
                            quantity=quantity,
                            entry_price=current_price,
                            profit_price=target_price,
                            stop_price=stop_price,
                            account=params.account
                        )
                        
                        if order["status"] == "success":
                            orders_placed.append(order["orderId"])
            
            return StrategyResult(
                strategy_id=strategy_id,
                status="completed",
                orders_placed=orders_placed,
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"Mean reversion error: {str(e)}")
            errors.append(str(e))
            return StrategyResult(
                strategy_id=strategy_id,
                status="error",
                orders_placed=orders_placed,
                errors=errors
            )
    
    async def _execute_pairs_trading(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """Pairs Trading Strategy - trades correlated pairs"""
        # Similar to statistical arbitrage but focuses on two specific correlated assets
        return await self._execute_stat_arb(strategy_id, params)
    
    async def _execute_momentum(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """Momentum Strategy - trades based on price momentum"""
        logger.info(f"Executing momentum strategy: {strategy_id}")
        orders_placed = []
        errors = []
        
        try:
            for symbol in params.symbols:
                # Get historical data
                historical_data = await self.ibkr_service.get_historical_data(symbol, "5d")
                
                if len(historical_data) >= 5:
                    # Calculate momentum (simple price change)
                    recent_prices = [bar.get("close", 0) for bar in historical_data[-5:]]
                    momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                    
                    # Trade if momentum exceeds threshold
                    threshold = params.custom_params.get("momentum_threshold", 0.03)
                    
                    if abs(momentum) > threshold:
                        action = "BUY" if momentum > 0 else "SELL"
                        quantity = min(
                            float(params.max_position_size),
                            params.custom_params.get("trade_size", 100)
                        )
                        
                        order = await self.ibkr_service.place_adaptive_order(
                            symbol=symbol,
                            action=action,
                            quantity=quantity,
                            price=recent_prices[-1] * (1.001 if action == "BUY" else 0.999),
                            account=params.account,
                            priority="Urgent" if abs(momentum) > threshold * 2 else "Normal"
                        )
                        
                        if order["status"] == "success":
                            orders_placed.append(order["orderId"])
            
            return StrategyResult(
                strategy_id=strategy_id,
                status="completed",
                orders_placed=orders_placed,
                metrics={"momentum": momentum if 'momentum' in locals() else None},
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"Momentum strategy error: {str(e)}")
            errors.append(str(e))
            return StrategyResult(
                strategy_id=strategy_id,
                status="error",
                orders_placed=orders_placed,
                errors=errors
            )
    
    async def _execute_twap(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """Time Weighted Average Price (TWAP) Strategy"""
        logger.info(f"Executing TWAP strategy: {strategy_id}")
        orders_placed = []
        errors = []
        
        try:
            symbol = params.symbols[0]
            total_quantity = float(params.custom_params.get("total_quantity", 1000))
            duration_minutes = params.custom_params.get("duration_minutes", 60)
            
            # Calculate time slices
            slices = min(params.custom_params.get("num_slices", duration_minutes // 5), 20)
            slice_quantity = total_quantity / slices
            interval_seconds = (duration_minutes * 60) / slices
            
            # Execute orders at regular intervals
            for i in range(slices):
                market_data = await self.ibkr_service.get_market_data(symbol)
                if market_data["status"] == "success":
                    current_price = market_data["data"]["data"].get("last", 0)
                    
                    if current_price > 0:
                        order = await self.ibkr_service.place_adaptive_order(
                            symbol=symbol,
                            action=params.custom_params.get("action", "BUY"),
                            quantity=slice_quantity,
                            price=current_price * 1.001,
                            account=params.account
                        )
                        
                        if order["status"] == "success":
                            orders_placed.append(order["orderId"])
                
                # Wait for next interval
                if i < slices - 1:
                    await asyncio.sleep(interval_seconds)
            
            return StrategyResult(
                strategy_id=strategy_id,
                status="completed",
                orders_placed=orders_placed,
                metrics={
                    "total_quantity": total_quantity,
                    "slices": slices,
                    "duration_minutes": duration_minutes
                },
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"TWAP strategy error: {str(e)}")
            errors.append(str(e))
            return StrategyResult(
                strategy_id=strategy_id,
                status="error",
                orders_placed=orders_placed,
                errors=errors
            )
    
    async def _execute_smart_routing(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """Smart Order Routing - intelligently routes orders across venues"""
        logger.info(f"Executing smart order routing: {strategy_id}")
        # This would integrate with multiple exchanges and dark pools
        # For now, use IB's SMART routing
        return await self._execute_adaptive_routing(strategy_id, params)
    
    async def _execute_iceberg(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """Iceberg Order - shows only a portion of the total order"""
        logger.info(f"Executing iceberg strategy: {strategy_id}")
        orders_placed = []
        errors = []
        
        try:
            symbol = params.symbols[0]
            total_quantity = float(params.custom_params.get("total_quantity", 1000))
            display_size = float(params.custom_params.get("display_size", 100))
            
            # Get current market price
            market_data = await self.ibkr_service.get_market_data(symbol)
            if market_data["status"] == "success":
                current_price = market_data["data"]["data"].get("last", 0)
                
                if current_price > 0:
                    # Create iceberg order (using IB's reserve order type)
                    contract = await self.ibkr_service.create_contract(symbol)
                    
                    order = Order()
                    order.action = params.custom_params.get("action", "BUY")
                    order.orderType = "LMT"
                    order.totalQuantity = Decimal(str(total_quantity))
                    order.lmtPrice = current_price * (1.001 if order.action == "BUY" else 0.999)
                    order.displaySize = int(display_size)  # This creates the iceberg effect
                    order.account = params.account
                    
                    # Place the order
                    trade = self.ibkr_service.ib.placeOrder(contract, order)
                    orders_placed.append(trade.order.orderId)
            
            return StrategyResult(
                strategy_id=strategy_id,
                status="completed",
                orders_placed=orders_placed,
                metrics={
                    "total_quantity": total_quantity,
                    "display_size": display_size
                },
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"Iceberg strategy error: {str(e)}")
            errors.append(str(e))
            return StrategyResult(
                strategy_id=strategy_id,
                status="error",
                orders_placed=orders_placed,
                errors=errors
            )
    
    async def _execute_adaptive_routing(self, strategy_id: str, params: StrategyParameters) -> StrategyResult:
        """Helper method for adaptive order routing"""
        orders_placed = []
        errors = []
        
        for symbol in params.symbols:
            try:
                quantity = min(
                    float(params.max_position_size),
                    params.custom_params.get("trade_size", 100)
                )
                
                market_data = await self.ibkr_service.get_market_data(symbol)
                if market_data["status"] == "success":
                    current_price = market_data["data"]["data"].get("last", 0)
                    
                    if current_price > 0:
                        order = await self.ibkr_service.place_adaptive_order(
                            symbol=symbol,
                            action=params.custom_params.get("action", "BUY"),
                            quantity=quantity,
                            price=current_price * 1.001,
                            account=params.account,
                            priority=params.custom_params.get("priority", "Normal")
                        )
                        
                        if order["status"] == "success":
                            orders_placed.append(order["orderId"])
            except Exception as e:
                errors.append(f"Error routing order for {symbol}: {str(e)}")
        
        return StrategyResult(
            strategy_id=strategy_id,
            status="completed",
            orders_placed=orders_placed,
            errors=errors
        )
    
    async def _validate_strategy_parameters(self, params: StrategyParameters):
        """Validate strategy parameters"""
        if not params.symbols:
            raise ValueError("At least one symbol is required")
        
        if params.max_position_size <= 0:
            raise ValueError("Max position size must be positive")
        
        if params.risk_limit <= 0:
            raise ValueError("Risk limit must be positive")
        
        if not params.account:
            raise ValueError("Account is required")
        
        # Strategy-specific validations
        if params.strategy_type in [StrategyType.STATISTICAL_ARBITRAGE, StrategyType.PAIRS_TRADING]:
            if len(params.symbols) < 2:
                raise ValueError(f"{params.strategy_type.value} requires at least 2 symbols")
    
    def _calculate_strategy_timeout(self, params: StrategyParameters) -> float:
        """Calculate appropriate timeout for strategy"""
        # Base timeout
        timeout = 300  # 5 minutes
        
        # Adjust based on strategy type
        if params.strategy_type in [StrategyType.VWAP, StrategyType.TWAP]:
            # Time-based strategies need longer timeout
            duration = params.custom_params.get("duration_minutes", 60)
            timeout = max(timeout, duration * 60 + 300)
        
        # Add buffer for end time
        if params.end_time:
            time_until_end = (params.end_time - datetime.now()).total_seconds()
            timeout = max(timeout, time_until_end + 300)
        
        return timeout
