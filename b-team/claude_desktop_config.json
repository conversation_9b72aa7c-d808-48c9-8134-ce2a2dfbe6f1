{"name": "IBKR Trading Assistant", "description": "Interactive Brokers trading assistant for <PERSON>", "version": "1.0.0", "servers": [{"ibkr-trading": {"command": "python3", "args": ["/Users/<USER>/projects/b-team/mcp_server_main.py", "--host", "127.0.0.1", "--port", "7496", "--client-id", "1"], "env": {"IBKR_AUTO_CONNECT": "false", "IBKR_DEFAULT_EXCHANGE": "SMART", "IBKR_DEFAULT_CURRENCY": "USD", "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"}}}]}