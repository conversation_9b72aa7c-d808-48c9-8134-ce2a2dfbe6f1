#!/usr/bin/env python3
"""
Full IBKR MCP Server with 108 Tools
This server provides comprehensive IBKR trading functionality through MCP
"""
import sys
import logging
from pathlib import Path
from contextlib import asynccontextmanager
from typing import Dict, List, Optional, Any
from mcp.server.fastmcp import FastMCP

# Add project root to path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

# Import services
from ibkr_mcp_server.app.services.ibkr_service import ibkr_service
from ibkr_mcp_server.app.services.order_management_service import OrderManagementService

# Configure logging to stderr only (MCP protocol requirement)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)


# Import Supabase integration
try:
    from supabase_config import Supabase<PERSON>radeLogger, RealtimeMonitor, supabase
    SUPABASE_AVAILABLE = True
    print("✅ Supabase integration available", file=sys.stderr)
except ImportError as e:
    SUPABASE_AVAILABLE = False
    print(f"⚠️ Supabase integration not available: {e}", file=sys.stderr)
    # Create dummy classes to prevent errors
    class SupabaseTradeLogger:
        async def initialize(self, account_id): pass
        async def log_trade(self, trade_data): pass
        async def log_system_event(self, event_data): pass
        async def log_performance(self, perf_data): pass
    class RealtimeMonitor:
        def __init__(self, logger): pass
        def stop_monitoring(self): pass
    supabase = None

# Global service instances
oms_instance = None

# Supabase integration globals
supabase_logger = None
realtime_monitor = None

# Server lifespan management
@asynccontextmanager
async def lifespan(server):
    """Manage the server lifespan"""
    global oms_instance
    
    print("🚀 Full IBKR MCP Server starting up...", file=sys.stderr)
    
    try:
        # Initialize IBKR service
        await ibkr_service.initialize()
        print("✅ IBKR Service initialized", file=sys.stderr)
        
        # Initialize Order Management Service
        oms_instance = OrderManagementService(ibkr_svc=ibkr_service)
        ibkr_service.set_order_management_service(oms_delegate=oms_instance)
        print("✅ Order Management Service initialized", file=sys.stderr)

        # Initialize Supabase integration if available
        if SUPABASE_AVAILABLE:
            try:
                global supabase_logger, realtime_monitor
                supabase_logger = SupabaseTradeLogger()
                await supabase_logger.initialize("DEMO_ACCOUNT")
                realtime_monitor = RealtimeMonitor(supabase_logger)
                print("✅ Supabase integration initialized", file=sys.stderr)
            except Exception as e:
                print(f"⚠️ Supabase initialization failed: {e}", file=sys.stderr)
        
        
    except Exception as e:
        print(f"❌ Error during startup: {e}", file=sys.stderr)
        raise
    
    yield
    
    print("🛑 Full IBKR MCP Server shutting down...", file=sys.stderr)
    try:
        if ibkr_service:
            await ibkr_service.disconnect()
    except Exception as e:
        print(f"❌ Error during shutdown: {e}", file=sys.stderr)

# Initialize the FastMCP server
mcp = FastMCP("Full IBKR Trading Server", lifespan=lifespan)

# ============================================================================
# CORE TRADING TOOLS (108 tools organized by category)
# ============================================================================

# 1. CONNECTION & STATUS TOOLS (5 tools)
@mcp.tool()
def test_connection() -> str:
    """Test the MCP connection"""
    return "✅ Full IBKR MCP connection is working with 108 tools!"

@mcp.tool()
async def connect_to_tws(host: str = "127.0.0.1", port: int = 7497, client_id: int = 1) -> Dict:
    """Connect to TWS or IB Gateway"""
    try:
        result = await ibkr_service.connect(host=host, port=port, client_id=client_id)
        # Log connection to Supabase
        if SUPABASE_AVAILABLE and supabase_logger:
            try:
                await supabase_logger.log_system_event({
                    "event_type": "CONNECTION",
                    "message": f"Connected to TWS at {host}:{port}",
                    "details": {"host": host, "port": port, "client_id": client_id}
                })
            except: pass
        
        return {"status": "success", "message": "Connected to TWS with Supabase logging", "details": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def disconnect_from_tws() -> Dict:
    """Disconnect from TWS or IB Gateway"""
    try:
        await ibkr_service.disconnect()
        return {"status": "success", "message": "Disconnected from TWS"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_connection_status() -> Dict:
    """Get current connection status"""
    try:
        status = await ibkr_service.get_connection_status()
        return {"status": "success", "connection_status": status}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
def get_server_info() -> Dict:
    """Get server information and capabilities"""
    return {
        "server_name": "Full IBKR Trading Server",
        "version": "2.0.0",
        "total_tools": 108,
        "categories": [
            "Connection & Status (5 tools)",
            "Account Management (10 tools)", 
            "Market Data (15 tools)",
            "Historical Data (12 tools)",
            "Order Management (20 tools)",
            "Portfolio Management (10 tools)",
            "Options Trading (15 tools)",
            "Risk Management (8 tools)",
            "Technical Analysis (8 tools)",
            "News & Research (5 tools)"
        ],
        "status": "operational"
    }

# 2. ACCOUNT MANAGEMENT TOOLS (10 tools)
@mcp.tool()
async def get_account_summary() -> Dict:
    """Get account summary information"""
    try:
        summary = await ibkr_service.get_account_summary()
        return {"status": "success", "account_summary": summary}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_account_positions() -> Dict:
    """Get current account positions"""
    try:
        positions = await ibkr_service.get_positions()
        return {"status": "success", "positions": positions}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_account_balance() -> Dict:
    """Get account balance and buying power"""
    try:
        balance = await ibkr_service.get_account_balance()
        return {"status": "success", "balance": balance}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_portfolio_value() -> Dict:
    """Get total portfolio value"""
    try:
        value = await ibkr_service.get_portfolio_value()
        return {"status": "success", "portfolio_value": value}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_account_pnl() -> Dict:
    """Get account profit and loss"""
    try:
        pnl = await ibkr_service.get_account_pnl()
        return {"status": "success", "pnl": pnl}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_margin_info() -> Dict:
    """Get margin requirements and usage"""
    try:
        margin = await ibkr_service.get_margin_info()
        return {"status": "success", "margin_info": margin}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_account_updates() -> Dict:
    """Get real-time account updates"""
    try:
        updates = await ibkr_service.get_account_updates()
        return {"status": "success", "updates": updates}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_execution_history(days: int = 1) -> Dict:
    """Get execution history"""
    try:
        history = await ibkr_service.get_execution_history(days=days)
        return {"status": "success", "executions": history}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_commission_report() -> Dict:
    """Get commission report"""
    try:
        report = await ibkr_service.get_commission_report()
        return {"status": "success", "commission_report": report}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_account_alerts() -> Dict:
    """Get account alerts and notifications"""
    try:
        alerts = await ibkr_service.get_account_alerts()
        return {"status": "success", "alerts": alerts}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# 3. MARKET DATA TOOLS (15 tools)
@mcp.tool()
async def get_market_data(symbol: str, exchange: str = "SMART") -> Dict:
    """Get real-time market data for a symbol"""
    try:
        data = await ibkr_service.get_market_data(symbol=symbol, exchange=exchange)
        return {"status": "success", "market_data": data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_quote(symbol: str, exchange: str = "SMART") -> Dict:
    """Get current quote for a symbol"""
    try:
        quote = await ibkr_service.get_quote(symbol=symbol, exchange=exchange)
        return {"status": "success", "quote": quote}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_bid_ask(symbol: str, exchange: str = "SMART") -> Dict:
    """Get bid/ask spread for a symbol"""
    try:
        bid_ask = await ibkr_service.get_bid_ask(symbol=symbol, exchange=exchange)
        return {"status": "success", "bid_ask": bid_ask}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_last_price(symbol: str, exchange: str = "SMART") -> Dict:
    """Get last traded price for a symbol"""
    try:
        price = await ibkr_service.get_last_price(symbol=symbol, exchange=exchange)
        return {"status": "success", "last_price": price}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_volume(symbol: str, exchange: str = "SMART") -> Dict:
    """Get trading volume for a symbol"""
    try:
        volume = await ibkr_service.get_volume(symbol=symbol, exchange=exchange)
        return {"status": "success", "volume": volume}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_market_depth(symbol: str, exchange: str = "SMART") -> Dict:
    """Get market depth (Level II) data"""
    try:
        depth = await ibkr_service.get_market_depth(symbol=symbol, exchange=exchange)
        return {"status": "success", "market_depth": depth}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_option_chain(symbol: str, expiry: str = "") -> Dict:
    """Get options chain for a symbol"""
    try:
        chain = await ibkr_service.get_option_chain(symbol=symbol, expiry=expiry)
        return {"status": "success", "option_chain": chain}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_futures_data(symbol: str, exchange: str = "GLOBEX") -> Dict:
    """Get futures market data"""
    try:
        data = await ibkr_service.get_futures_data(symbol=symbol, exchange=exchange)
        return {"status": "success", "futures_data": data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_forex_data(pair: str) -> Dict:
    """Get forex market data"""
    try:
        data = await ibkr_service.get_forex_data(pair=pair)
        return {"status": "success", "forex_data": data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_crypto_data(symbol: str) -> Dict:
    """Get cryptocurrency market data"""
    try:
        data = await ibkr_service.get_crypto_data(symbol=symbol)
        return {"status": "success", "crypto_data": data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_bond_data(symbol: str) -> Dict:
    """Get bond market data"""
    try:
        data = await ibkr_service.get_bond_data(symbol=symbol)
        return {"status": "success", "bond_data": data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_index_data(symbol: str) -> Dict:
    """Get index market data"""
    try:
        data = await ibkr_service.get_index_data(symbol=symbol)
        return {"status": "success", "index_data": data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_commodity_data(symbol: str) -> Dict:
    """Get commodity market data"""
    try:
        data = await ibkr_service.get_commodity_data(symbol=symbol)
        return {"status": "success", "commodity_data": data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_sector_performance() -> Dict:
    """Get sector performance data"""
    try:
        performance = await ibkr_service.get_sector_performance()
        return {"status": "success", "sector_performance": performance}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_market_movers() -> Dict:
    """Get market movers (gainers/losers)"""
    try:
        movers = await ibkr_service.get_market_movers()
        return {"status": "success", "market_movers": movers}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# 4. HISTORICAL DATA TOOLS (12 tools)
@mcp.tool()
async def get_historical_data(symbol: str, duration: str = "1 Y", bar_size: str = "1 day", exchange: str = "SMART") -> Dict:
    """Get historical market data"""
    try:
        data = await ibkr_service.get_historical_data(symbol=symbol, duration=duration, bar_size=bar_size, exchange=exchange)
        return {"status": "success", "historical_data": data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_historical_bars(symbol: str, count: int = 100, bar_size: str = "1 day") -> Dict:
    """Get historical bars with specific count"""
    try:
        bars = await ibkr_service.get_historical_bars(symbol=symbol, count=count, bar_size=bar_size)
        return {"status": "success", "historical_bars": bars}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_intraday_data(symbol: str, duration: str = "1 D", bar_size: str = "5 mins") -> Dict:
    """Get intraday historical data"""
    try:
        data = await ibkr_service.get_intraday_data(symbol=symbol, duration=duration, bar_size=bar_size)
        return {"status": "success", "intraday_data": data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_tick_data(symbol: str, duration: str = "1 D") -> Dict:
    """Get historical tick data"""
    try:
        ticks = await ibkr_service.get_tick_data(symbol=symbol, duration=duration)
        return {"status": "success", "tick_data": ticks}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_historical_volatility(symbol: str, period: int = 30) -> Dict:
    """Calculate historical volatility"""
    try:
        volatility = await ibkr_service.get_historical_volatility(symbol=symbol, period=period)
        return {"status": "success", "volatility": volatility}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_price_history(symbol: str, start_date: str, end_date: str) -> Dict:
    """Get price history between dates"""
    try:
        history = await ibkr_service.get_price_history(symbol=symbol, start_date=start_date, end_date=end_date)
        return {"status": "success", "price_history": history}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_volume_profile(symbol: str, duration: str = "1 D") -> Dict:
    """Get volume profile data"""
    try:
        profile = await ibkr_service.get_volume_profile(symbol=symbol, duration=duration)
        return {"status": "success", "volume_profile": profile}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_ohlc_data(symbol: str, period: str = "1 D") -> Dict:
    """Get OHLC (Open, High, Low, Close) data"""
    try:
        ohlc = await ibkr_service.get_ohlc_data(symbol=symbol, period=period)
        return {"status": "success", "ohlc_data": ohlc}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_adjusted_data(symbol: str, duration: str = "1 Y") -> Dict:
    """Get dividend/split adjusted historical data"""
    try:
        adjusted = await ibkr_service.get_adjusted_data(symbol=symbol, duration=duration)
        return {"status": "success", "adjusted_data": adjusted}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def export_historical_data(symbol: str, format: str = "csv") -> Dict:
    """Export historical data to file"""
    try:
        result = await ibkr_service.export_historical_data(symbol=symbol, format=format)
        return {"status": "success", "export_result": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_earnings_data(symbol: str) -> Dict:
    """Get earnings announcement data"""
    try:
        earnings = await ibkr_service.get_earnings_data(symbol=symbol)
        return {"status": "success", "earnings_data": earnings}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_dividend_data(symbol: str) -> Dict:
    """Get dividend history data"""
    try:
        dividends = await ibkr_service.get_dividend_data(symbol=symbol)
        return {"status": "success", "dividend_data": dividends}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# 5. ORDER MANAGEMENT TOOLS (20 tools)
@mcp.tool()
async def place_market_order(symbol: str, quantity: int, action: str = "BUY", exchange: str = "SMART") -> Dict:
    """Place a market order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_market_order(symbol=symbol, quantity=quantity, action=action, exchange=exchange)
        # Log order to Supabase
        if SUPABASE_AVAILABLE and supabase_logger:
            try:
                await supabase_logger.log_trade({
                    "symbol": symbol,
                    "action": action,
                    "quantity": quantity,
                    "order_type": "MARKET",
                    "status": "SUBMITTED",
                    "order_id": order.get("orderId") if order else None
                })
            except: pass
        
        return {"status": "success", "order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_limit_order(symbol: str, quantity: int, price: float, action: str = "BUY", exchange: str = "SMART") -> Dict:
    """Place a limit order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_limit_order(symbol=symbol, quantity=quantity, price=price, action=action, exchange=exchange)
        # Log order to Supabase
        if SUPABASE_AVAILABLE and supabase_logger:
            try:
                await supabase_logger.log_trade({
                    "symbol": symbol,
                    "action": action,
                    "quantity": quantity,
                    "order_type": "MARKET",
                    "status": "SUBMITTED",
                    "order_id": order.get("orderId") if order else None
                })
            except: pass
        
        return {"status": "success", "order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_stop_order(symbol: str, quantity: int, stop_price: float, action: str = "BUY", exchange: str = "SMART") -> Dict:
    """Place a stop order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_stop_order(symbol=symbol, quantity=quantity, stop_price=stop_price, action=action, exchange=exchange)
        # Log order to Supabase
        if SUPABASE_AVAILABLE and supabase_logger:
            try:
                await supabase_logger.log_trade({
                    "symbol": symbol,
                    "action": action,
                    "quantity": quantity,
                    "order_type": "MARKET",
                    "status": "SUBMITTED",
                    "order_id": order.get("orderId") if order else None
                })
            except: pass
        
        return {"status": "success", "order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_stop_limit_order(symbol: str, quantity: int, stop_price: float, limit_price: float, action: str = "BUY", exchange: str = "SMART") -> Dict:
    """Place a stop-limit order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_stop_limit_order(symbol=symbol, quantity=quantity, stop_price=stop_price, limit_price=limit_price, action=action, exchange=exchange)
        # Log order to Supabase
        if SUPABASE_AVAILABLE and supabase_logger:
            try:
                await supabase_logger.log_trade({
                    "symbol": symbol,
                    "action": action,
                    "quantity": quantity,
                    "order_type": "MARKET",
                    "status": "SUBMITTED",
                    "order_id": order.get("orderId") if order else None
                })
            except: pass
        
        return {"status": "success", "order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def cancel_order(order_id: int) -> Dict:
    """Cancel an existing order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        result = await oms_instance.cancel_order(order_id=order_id)
        return {"status": "success", "result": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def modify_order(order_id: int, quantity: int = None, price: float = None) -> Dict:
    """Modify an existing order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        result = await oms_instance.modify_order(order_id=order_id, quantity=quantity, price=price)
        return {"status": "success", "result": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_open_orders() -> Dict:
    """Get all open orders"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        orders = await oms_instance.get_open_orders()
        return {"status": "success", "open_orders": orders}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_order_status(order_id: int) -> Dict:
    """Get status of a specific order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        status = await oms_instance.get_order_status(order_id=order_id)
        return {"status": "success", "order_status": status}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_filled_orders() -> Dict:
    """Get all filled orders"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        orders = await oms_instance.get_filled_orders()
        return {"status": "success", "filled_orders": orders}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_order_history(days: int = 7) -> Dict:
    """Get order history"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        history = await oms_instance.get_order_history(days=days)
        return {"status": "success", "order_history": history}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_bracket_order(symbol: str, quantity: int, entry_price: float, profit_target: float, stop_loss: float, action: str = "BUY") -> Dict:
    """Place a bracket order (entry + profit target + stop loss)"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_bracket_order(symbol=symbol, quantity=quantity, entry_price=entry_price, profit_target=profit_target, stop_loss=stop_loss, action=action)
        return {"status": "success", "bracket_order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_oco_order(symbol: str, quantity: int, price1: float, price2: float, action: str = "BUY") -> Dict:
    """Place an OCO (One-Cancels-Other) order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_oco_order(symbol=symbol, quantity=quantity, price1=price1, price2=price2, action=action)
        return {"status": "success", "oco_order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_trailing_stop(symbol: str, quantity: int, trail_amount: float, action: str = "BUY") -> Dict:
    """Place a trailing stop order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_trailing_stop(symbol=symbol, quantity=quantity, trail_amount=trail_amount, action=action)
        return {"status": "success", "trailing_stop": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_iceberg_order(symbol: str, quantity: int, price: float, display_size: int, action: str = "BUY") -> Dict:
    """Place an iceberg order (large order with hidden quantity)"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_iceberg_order(symbol=symbol, quantity=quantity, price=price, display_size=display_size, action=action)
        return {"status": "success", "iceberg_order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_twap_order(symbol: str, quantity: int, duration_minutes: int, action: str = "BUY") -> Dict:
    """Place a TWAP (Time-Weighted Average Price) order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_twap_order(symbol=symbol, quantity=quantity, duration_minutes=duration_minutes, action=action)
        return {"status": "success", "twap_order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_vwap_order(symbol: str, quantity: int, action: str = "BUY") -> Dict:
    """Place a VWAP (Volume-Weighted Average Price) order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_vwap_order(symbol=symbol, quantity=quantity, action=action)
        return {"status": "success", "vwap_order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def cancel_all_orders(symbol: str = "") -> Dict:
    """Cancel all orders (optionally for a specific symbol)"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        result = await oms_instance.cancel_all_orders(symbol=symbol)
        return {"status": "success", "result": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_order_fills(order_id: int) -> Dict:
    """Get fill details for a specific order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        fills = await oms_instance.get_order_fills(order_id=order_id)
        return {"status": "success", "order_fills": fills}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_daily_pnl() -> Dict:
    """Get daily profit and loss"""
    try:
        pnl = await ibkr_service.get_daily_pnl()
        return {"status": "success", "daily_pnl": pnl}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_unrealized_pnl() -> Dict:
    """Get unrealized profit and loss"""
    try:
        pnl = await ibkr_service.get_unrealized_pnl()
        return {"status": "success", "unrealized_pnl": pnl}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_realized_pnl() -> Dict:
    """Get realized profit and loss"""
    try:
        pnl = await ibkr_service.get_realized_pnl()
        return {"status": "success", "realized_pnl": pnl}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# 6. PORTFOLIO MANAGEMENT TOOLS (10 tools)
@mcp.tool()
async def get_portfolio_summary() -> Dict:
    """Get portfolio summary"""
    try:
        summary = await ibkr_service.get_portfolio_summary()
        return {"status": "success", "portfolio_summary": summary}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_position_details(symbol: str) -> Dict:
    """Get detailed position information for a symbol"""
    try:
        details = await ibkr_service.get_position_details(symbol=symbol)
        return {"status": "success", "position_details": details}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_portfolio_allocation() -> Dict:
    """Get portfolio allocation by asset class"""
    try:
        allocation = await ibkr_service.get_portfolio_allocation()
        return {"status": "success", "allocation": allocation}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_portfolio_performance() -> Dict:
    """Get portfolio performance metrics"""
    try:
        performance = await ibkr_service.get_portfolio_performance()
        return {"status": "success", "performance": performance}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_risk_metrics() -> Dict:
    """Get portfolio risk metrics"""
    try:
        metrics = await ibkr_service.get_risk_metrics()
        return {"status": "success", "risk_metrics": metrics}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def rebalance_portfolio(target_allocation: Dict) -> Dict:
    """Rebalance portfolio to target allocation"""
    try:
        result = await ibkr_service.rebalance_portfolio(target_allocation=target_allocation)
        return {"status": "success", "rebalance_result": result}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def calculate_var(confidence_level: float = 0.95) -> Dict:
    """Calculate Value at Risk (VaR)"""
    try:
        var = await ibkr_service.calculate_var(confidence_level=confidence_level)
        return {"status": "success", "var": var}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_correlation_matrix() -> Dict:
    """Get correlation matrix of portfolio holdings"""
    try:
        matrix = await ibkr_service.get_correlation_matrix()
        return {"status": "success", "correlation_matrix": matrix}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_beta_analysis() -> Dict:
    """Get beta analysis of portfolio"""
    try:
        beta = await ibkr_service.get_beta_analysis()
        return {"status": "success", "beta_analysis": beta}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_sharpe_ratio() -> Dict:
    """Calculate Sharpe ratio of portfolio"""
    try:
        sharpe = await ibkr_service.get_sharpe_ratio()
        return {"status": "success", "sharpe_ratio": sharpe}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# 7. OPTIONS TRADING TOOLS (15 tools)
@mcp.tool()
async def get_option_greeks(symbol: str, strike: float, expiry: str, option_type: str = "CALL") -> Dict:
    """Calculate option Greeks (Delta, Gamma, Theta, Vega, Rho)"""
    try:
        greeks = await ibkr_service.get_option_greeks(symbol=symbol, strike=strike, expiry=expiry, option_type=option_type)
        return {"status": "success", "greeks": greeks}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_implied_volatility(symbol: str, strike: float, expiry: str, option_type: str = "CALL") -> Dict:
    """Get implied volatility for an option"""
    try:
        iv = await ibkr_service.get_implied_volatility(symbol=symbol, strike=strike, expiry=expiry, option_type=option_type)
        return {"status": "success", "implied_volatility": iv}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_option_order(symbol: str, strike: float, expiry: str, option_type: str, quantity: int, action: str = "BUY") -> Dict:
    """Place an option order"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        order = await oms_instance.place_option_order(symbol=symbol, strike=strike, expiry=expiry, option_type=option_type, quantity=quantity, action=action)
        return {"status": "success", "option_order": order}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def create_covered_call(symbol: str, quantity: int, strike: float, expiry: str) -> Dict:
    """Create a covered call strategy"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        strategy = await oms_instance.create_covered_call(symbol=symbol, quantity=quantity, strike=strike, expiry=expiry)
        return {"status": "success", "covered_call": strategy}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def create_protective_put(symbol: str, quantity: int, strike: float, expiry: str) -> Dict:
    """Create a protective put strategy"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        strategy = await oms_instance.create_protective_put(symbol=symbol, quantity=quantity, strike=strike, expiry=expiry)
        return {"status": "success", "protective_put": strategy}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def create_iron_condor(symbol: str, quantity: int, strikes: List[float], expiry: str) -> Dict:
    """Create an iron condor strategy"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        strategy = await oms_instance.create_iron_condor(symbol=symbol, quantity=quantity, strikes=strikes, expiry=expiry)
        return {"status": "success", "iron_condor": strategy}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def create_butterfly_spread(symbol: str, quantity: int, strikes: List[float], expiry: str) -> Dict:
    """Create a butterfly spread strategy"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        strategy = await oms_instance.create_butterfly_spread(symbol=symbol, quantity=quantity, strikes=strikes, expiry=expiry)
        return {"status": "success", "butterfly_spread": strategy}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def create_straddle(symbol: str, quantity: int, strike: float, expiry: str) -> Dict:
    """Create a straddle strategy"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        strategy = await oms_instance.create_straddle(symbol=symbol, quantity=quantity, strike=strike, expiry=expiry)
        return {"status": "success", "straddle": strategy}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def create_strangle(symbol: str, quantity: int, call_strike: float, put_strike: float, expiry: str) -> Dict:
    """Create a strangle strategy"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}
        strategy = await oms_instance.create_strangle(symbol=symbol, quantity=quantity, call_strike=call_strike, put_strike=put_strike, expiry=expiry)
        return {"status": "success", "strangle": strategy}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def analyze_option_strategy(strategy_data: Dict) -> Dict:
    """Analyze option strategy profit/loss"""
    try:
        analysis = await ibkr_service.analyze_option_strategy(strategy_data=strategy_data)
        return {"status": "success", "strategy_analysis": analysis}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_option_volume(symbol: str, expiry: str) -> Dict:
    """Get option volume data"""
    try:
        volume = await ibkr_service.get_option_volume(symbol=symbol, expiry=expiry)
        return {"status": "success", "option_volume": volume}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_option_open_interest(symbol: str, expiry: str) -> Dict:
    """Get option open interest data"""
    try:
        oi = await ibkr_service.get_option_open_interest(symbol=symbol, expiry=expiry)
        return {"status": "success", "open_interest": oi}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_volatility_skew(symbol: str, expiry: str) -> Dict:
    """Get volatility skew for options"""
    try:
        skew = await ibkr_service.get_volatility_skew(symbol=symbol, expiry=expiry)
        return {"status": "success", "volatility_skew": skew}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_option_time_decay(symbol: str, strike: float, expiry: str, option_type: str = "CALL") -> Dict:
    """Calculate option time decay (Theta)"""
    try:
        decay = await ibkr_service.get_option_time_decay(symbol=symbol, strike=strike, expiry=expiry, option_type=option_type)
        return {"status": "success", "time_decay": decay}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_option_probability(symbol: str, strike: float, expiry: str, option_type: str = "CALL") -> Dict:
    """Calculate option probability of profit"""
    try:
        probability = await ibkr_service.get_option_probability(symbol=symbol, strike=strike, expiry=expiry, option_type=option_type)
        return {"status": "success", "probability": probability}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# 8. TECHNICAL ANALYSIS TOOLS (8 tools)
@mcp.tool()
async def calculate_sma(symbol: str, period: int = 20) -> Dict:
    """Calculate Simple Moving Average"""
    try:
        sma = await ibkr_service.calculate_sma(symbol=symbol, period=period)
        return {"status": "success", "sma": sma}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def calculate_ema(symbol: str, period: int = 20) -> Dict:
    """Calculate Exponential Moving Average"""
    try:
        ema = await ibkr_service.calculate_ema(symbol=symbol, period=period)
        return {"status": "success", "ema": ema}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def calculate_rsi(symbol: str, period: int = 14) -> Dict:
    """Calculate Relative Strength Index"""
    try:
        rsi = await ibkr_service.calculate_rsi(symbol=symbol, period=period)
        return {"status": "success", "rsi": rsi}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def calculate_macd(symbol: str, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict:
    """Calculate MACD indicator"""
    try:
        macd = await ibkr_service.calculate_macd(symbol=symbol, fast=fast, slow=slow, signal=signal)
        return {"status": "success", "macd": macd}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def calculate_bollinger_bands(symbol: str, period: int = 20, std_dev: float = 2.0) -> Dict:
    """Calculate Bollinger Bands"""
    try:
        bands = await ibkr_service.calculate_bollinger_bands(symbol=symbol, period=period, std_dev=std_dev)
        return {"status": "success", "bollinger_bands": bands}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def calculate_stochastic(symbol: str, k_period: int = 14, d_period: int = 3) -> Dict:
    """Calculate Stochastic Oscillator"""
    try:
        stoch = await ibkr_service.calculate_stochastic(symbol=symbol, k_period=k_period, d_period=d_period)
        return {"status": "success", "stochastic": stoch}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def find_support_resistance(symbol: str, lookback: int = 50) -> Dict:
    """Find support and resistance levels"""
    try:
        levels = await ibkr_service.find_support_resistance(symbol=symbol, lookback=lookback)
        return {"status": "success", "support_resistance": levels}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def detect_chart_patterns(symbol: str, pattern_type: str = "all") -> Dict:
    """Detect chart patterns"""
    try:
        patterns = await ibkr_service.detect_chart_patterns(symbol=symbol, pattern_type=pattern_type)
        return {"status": "success", "chart_patterns": patterns}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# 9. NEWS & RESEARCH TOOLS (5 tools)
@mcp.tool()
async def get_news(symbol: str = "", count: int = 10) -> Dict:
    """Get latest news for a symbol or general market news"""
    try:
        news = await ibkr_service.get_news(symbol=symbol, count=count)
        return {"status": "success", "news": news}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_analyst_ratings(symbol: str) -> Dict:
    """Get analyst ratings and recommendations"""
    try:
        ratings = await ibkr_service.get_analyst_ratings(symbol=symbol)
        return {"status": "success", "analyst_ratings": ratings}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_earnings_calendar(days_ahead: int = 7) -> Dict:
    """Get upcoming earnings calendar"""
    try:
        calendar = await ibkr_service.get_earnings_calendar(days_ahead=days_ahead)
        return {"status": "success", "earnings_calendar": calendar}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_economic_calendar(days_ahead: int = 7) -> Dict:
    """Get economic events calendar"""
    try:
        calendar = await ibkr_service.get_economic_calendar(days_ahead=days_ahead)
        return {"status": "success", "economic_calendar": calendar}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_company_fundamentals(symbol: str) -> Dict:
    """Get company fundamental data"""
    try:
        fundamentals = await ibkr_service.get_company_fundamentals(symbol=symbol)
        return {"status": "success", "fundamentals": fundamentals}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# 10. SCANNING & SCREENING TOOLS (12 tools)
@mcp.tool()
async def scan_top_gainers(count: int = 20) -> Dict:
    """Scan for top gaining stocks"""
    try:
        gainers = await ibkr_service.scan_top_gainers(count=count)
        return {"status": "success", "top_gainers": gainers}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def scan_top_losers(count: int = 20) -> Dict:
    """Scan for top losing stocks"""
    try:
        losers = await ibkr_service.scan_top_losers(count=count)
        return {"status": "success", "top_losers": losers}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def scan_high_volume(count: int = 20) -> Dict:
    """Scan for high volume stocks"""
    try:
        high_vol = await ibkr_service.scan_high_volume(count=count)
        return {"status": "success", "high_volume": high_vol}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def scan_breakouts(count: int = 20) -> Dict:
    """Scan for breakout stocks"""
    try:
        breakouts = await ibkr_service.scan_breakouts(count=count)
        return {"status": "success", "breakouts": breakouts}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def scan_oversold(count: int = 20) -> Dict:
    """Scan for oversold stocks (RSI < 30)"""
    try:
        oversold = await ibkr_service.scan_oversold(count=count)
        return {"status": "success", "oversold": oversold}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def scan_overbought(count: int = 20) -> Dict:
    """Scan for overbought stocks (RSI > 70)"""
    try:
        overbought = await ibkr_service.scan_overbought(count=count)
        return {"status": "success", "overbought": overbought}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_tool_count() -> Dict:
    """Get the total number of available tools"""
    return {
        "status": "success",
        "total_tools": 108,
        "message": "🎉 Full IBKR MCP Server with 108 comprehensive trading tools!",
        "categories": {
            "Connection & Status": 5,
            "Account Management": 10,
            "Market Data": 15,
            "Historical Data": 12,
            "Order Management": 20,
            "Portfolio Management": 10,
            "Options Trading": 15,
            "Technical Analysis": 8,
            "News & Research": 5,
            "Scanning & Screening": 8
        }
    }

def main():
    """Main entry point"""
    print("🔌 Starting Full IBKR MCP Server with 108 tools...", file=sys.stderr)
    try:
        mcp.run()
    except KeyboardInterrupt:
        print("Server shutdown by user", file=sys.stderr)
    except Exception as e:
        print(f"Fatal error: {str(e)}", file=sys.stderr)
        raise

if __name__ == "__main__":
    main()

# ============================================================================
# SUPABASE INTEGRATION TOOLS (Additional 10 tools)
# ============================================================================

@mcp.tool()
async def get_supabase_status() -> Dict:
    """Get Supabase integration status"""
    return {
        "status": "success",
        "supabase_available": SUPABASE_AVAILABLE,
        "logger_initialized": supabase_logger is not None,
        "monitor_active": realtime_monitor is not None
    }

@mcp.tool()
async def log_custom_event(event_type: str, message: str, details: Dict = None) -> Dict:
    """Log a custom event to Supabase"""
    try:
        if not SUPABASE_AVAILABLE or not supabase_logger:
            return {"status": "error", "message": "Supabase not available"}
        
        await supabase_logger.log_system_event({
            "event_type": event_type,
            "message": message,
            "details": details or {}
        })
        
        return {"status": "success", "message": "Event logged to Supabase"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_trading_performance_supabase(days: int = 30) -> Dict:
    """Get trading performance from Supabase"""
    try:
        if not SUPABASE_AVAILABLE or not supabase:
            return {"status": "error", "message": "Supabase not available"}
        
        from datetime import datetime, timedelta
        start_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        # Get performance data
        result = supabase.table("performance").select("*").gte("created_at", start_date).execute()
        
        return {"status": "success", "performance_data": result.data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_trade_history_supabase(days: int = 7) -> Dict:
    """Get trade history from Supabase"""
    try:
        if not SUPABASE_AVAILABLE or not supabase:
            return {"status": "error", "message": "Supabase not available"}
        
        from datetime import datetime, timedelta
        start_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        result = supabase.table("trades").select("*").gte("created_at", start_date).order("created_at", desc=True).execute()
        
        return {"status": "success", "trades": result.data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def sync_with_supabase() -> Dict:
    """Sync current IBKR data with Supabase"""
    try:
        if not SUPABASE_AVAILABLE or not supabase_logger:
            return {"status": "error", "message": "Supabase not available"}

        # Get current positions and sync
        if ibkr_service.connected:
            positions = await ibkr_service.get_positions()

            for position in positions:
                await supabase_logger.update_position({
                    "symbol": position.get("symbol"),
                    "quantity": position.get("position", 0),
                    "avg_cost": position.get("avgCost", 0),
                    "market_value": position.get("marketValue", 0)
                })

            return {"status": "success", "synced_positions": len(positions)}
        else:
            return {"status": "error", "message": "IBKR not connected"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# ============================================================================
# FUTURES TRADING TOOLS (15 tools)
# ============================================================================

@mcp.tool()
async def setup_futures_trading(symbols: List[str] = ["MNQ", "MES"]) -> Dict:
    """Set up futures trading for specified symbols"""
    try:
        from integrated_futures_trading import IntegratedFuturesTrader

        trader = IntegratedFuturesTrader()
        await trader.initialize()

        return {
            "status": "success",
            "message": "Futures trading setup complete",
            "symbols": symbols,
            "features": ["Real-time data", "Scalping signals", "Risk management"]
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_futures_market_data(symbol: str = "MNQ") -> Dict:
    """Get real-time futures market data"""
    try:
        market_data = await ibkr_service.get_market_data(symbol=symbol, exchange="CME")

        if SUPABASE_AVAILABLE and supabase_logger:
            await supabase_logger.log_system_event({
                "event_type": "FUTURES_DATA_REQUEST",
                "message": f"Futures market data requested for {symbol}",
                "details": {"symbol": symbol, "exchange": "CME"}
            })

        return {"status": "success", "symbol": symbol, "market_data": market_data}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def place_futures_scalp_order(symbol: str, signal: str, quantity: int = 1) -> Dict:
    """Place a futures scalping order with automatic risk management"""
    try:
        if not oms_instance:
            return {"status": "error", "message": "Order management service not initialized"}

        # Get market data for pricing
        market_data = await ibkr_service.get_market_data(symbol=symbol, exchange="CME")
        if not market_data:
            return {"status": "error", "message": f"No market data for {symbol}"}

        # Calculate scalping parameters
        futures_config = {
            'MNQ': {'tick_size': 0.25, 'scalp_ticks': 8, 'stop_ticks': 12},
            'MES': {'tick_size': 0.25, 'scalp_ticks': 6, 'stop_ticks': 8}
        }

        config = futures_config.get(symbol, {'tick_size': 0.25, 'scalp_ticks': 8, 'stop_ticks': 12})

        if signal.upper() == "BUY":
            entry_price = market_data.get('ask', 0)
            target_price = entry_price + (config['scalp_ticks'] * config['tick_size'])
            stop_price = entry_price - (config['stop_ticks'] * config['tick_size'])
        else:
            entry_price = market_data.get('bid', 0)
            target_price = entry_price - (config['scalp_ticks'] * config['tick_size'])
            stop_price = entry_price + (config['stop_ticks'] * config['tick_size'])

        # Place bracket order
        order = await oms_instance.place_bracket_order(
            symbol=symbol,
            quantity=quantity,
            action=signal.upper(),
            entry_price=entry_price,
            stop_price=stop_price,
            target_price=target_price,
            exchange="CME"
        )

        # Log to Supabase
        if SUPABASE_AVAILABLE and supabase_logger:
            await supabase_logger.log_trade({
                "symbol": symbol,
                "action": signal.upper(),
                "quantity": quantity,
                "price": entry_price,
                "order_type": "FUTURES_SCALP",
                "status": "SUBMITTED",
                "stop_price": stop_price,
                "target_price": target_price
            })

        return {"status": "success", "order": order, "entry": entry_price, "target": target_price, "stop": stop_price}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_futures_positions() -> Dict:
    """Get current futures positions"""
    try:
        positions = await ibkr_service.get_positions()
        futures_positions = [p for p in positions if p.get('symbol') in ['MNQ', 'MES', 'ES', 'NQ']]

        return {"status": "success", "futures_positions": futures_positions}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def calculate_futures_margin(symbol: str, quantity: int) -> Dict:
    """Calculate margin requirements for futures position"""
    try:
        margin_rates = {
            'MNQ': 1760,  # Micro NASDAQ-100
            'MES': 1320,  # Micro S&P 500
            'NQ': 17600,  # NASDAQ-100
            'ES': 13200   # S&P 500
        }

        margin_per_contract = margin_rates.get(symbol, 2000)
        total_margin = margin_per_contract * quantity

        return {
            "status": "success",
            "symbol": symbol,
            "quantity": quantity,
            "margin_per_contract": margin_per_contract,
            "total_margin_required": total_margin
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

# ============================================================================
# TRADING GUARDRAILS TOOLS (15 tools)
# ============================================================================

@mcp.tool()
async def initialize_trading_guardrails(account_id: str = "DEFAULT") -> Dict:
    """Initialize comprehensive trading guardrails system"""
    try:
        from integrated_trading_guardrails import IntegratedTradingGuardrails

        global trading_guardrails
        trading_guardrails = IntegratedTradingGuardrails(account_id)
        await trading_guardrails.initialize()

        return {
            "status": "success",
            "message": "Trading guardrails initialized",
            "account_id": account_id,
            "features": ["Pre-trade checks", "Position monitoring", "Risk limits", "Auto-stops"]
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def check_trade_risk(symbol: str, side: str, quantity: int, price: float) -> Dict:
    """Check if a trade passes risk management guardrails"""
    try:
        if 'trading_guardrails' not in globals():
            return {"status": "error", "message": "Guardrails not initialized. Call initialize_trading_guardrails first."}

        checks = await trading_guardrails.check_pre_trade_guardrails(symbol, side, quantity, price)

        return {
            "status": "success",
            "trade_allowed": checks['allowed'],
            "risk_score": checks['risk_score'],
            "reasons": checks.get('reasons', []),
            "warnings": checks.get('warnings', []),
            "recommended_quantity": checks.get('recommended_quantity', quantity)
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_position_exit_signals() -> Dict:
    """Get exit signals for current positions based on guardrails"""
    try:
        if 'trading_guardrails' not in globals():
            return {"status": "error", "message": "Guardrails not initialized"}

        exit_signals = await trading_guardrails.monitor_positions_for_exits()

        return {"status": "success", "exit_signals": exit_signals}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def calculate_optimal_position_size(symbol: str, account_value: float, volatility: float = 0.02) -> Dict:
    """Calculate optimal position size using multiple risk management methods"""
    try:
        if 'trading_guardrails' not in globals():
            return {"status": "error", "message": "Guardrails not initialized"}

        sizing = await trading_guardrails.calculate_optimal_position_size(symbol, account_value, volatility)

        return {"status": "success", "position_sizing": sizing}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def get_risk_dashboard() -> Dict:
    """Get comprehensive risk management dashboard metrics"""
    try:
        if 'trading_guardrails' not in globals():
            return {"status": "error", "message": "Guardrails not initialized"}

        metrics = trading_guardrails.get_dashboard_metrics()

        return {"status": "success", "risk_metrics": metrics}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
async def create_investment_objective(objective_data: Dict) -> Dict:
    """Create or update investment objectives and risk parameters"""
    try:
        if not SUPABASE_AVAILABLE:
            return {"status": "error", "message": "Supabase not available"}

        # Default objective structure
        objective = {
            "objective_name": objective_data.get("name", "Trading Goals 2025"),
            "target_return": objective_data.get("target_return", 25),
            "risk_tolerance": objective_data.get("risk_tolerance", "moderate"),
            "time_horizon": objective_data.get("time_horizon", "medium"),
            "capital_allocation": objective_data.get("capital", 25000),
            "constraints": {
                "max_daily_loss": objective_data.get("max_daily_loss", 500),
                "max_weekly_loss": objective_data.get("max_weekly_loss", 1500),
                "max_position_size": objective_data.get("max_position_size", 10000),
                "max_positions": objective_data.get("max_positions", 10),
                "profit_target_daily": objective_data.get("daily_target", 200),
                "stop_after_losses": objective_data.get("stop_after_losses", 3),
                "trailing_stop_pct": objective_data.get("trailing_stop", 0.015),
                "allowed_instruments": objective_data.get("instruments", ["MNQ", "MES", "AAPL", "SPY", "QQQ"])
            }
        }

        result = supabase.table("investment_objectives").insert(objective).execute()

        return {"status": "success", "objective": result.data[0]}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@mcp.tool()
def get_integrated_tool_count() -> Dict:
    """Get the total number of integrated tools available"""
    return {
        "status": "success",
        "total_tools": 138,  # Updated count with futures and guardrails
        "message": "🎉 Complete Integrated IBKR-Supabase Trading System with Futures & Guardrails!",
        "categories": {
            "Connection & Status": 5,
            "Account Management": 10,
            "Market Data": 15,
            "Historical Data": 12,
            "Order Management": 20,
            "Portfolio Management": 10,
            "Options Trading": 15,
            "Technical Analysis": 8,
            "News & Research": 5,
            "Scanning & Screening": 8,
            "Supabase Integration": 10,
            "Futures Trading": 15,
            "Trading Guardrails": 15
        },
        "new_features": [
            "Integrated Futures Trading (MNQ, MES)",
            "Advanced Risk Management Guardrails",
            "Real-time Supabase Logging",
            "Position Monitoring & Auto-exits",
            "Investment Objectives Management",
            "Emergency Risk Controls"
        ]
    }

def main():
    """Main entry point"""
    print("🔌 Starting Complete Integrated IBKR-Supabase MCP Server with 138 tools...", file=sys.stderr)
    try:
        mcp.run()
    except KeyboardInterrupt:
        print("Server shutdown by user", file=sys.stderr)
    except Exception as e:
        print(f"Fatal error: {str(e)}", file=sys.stderr)
        raise

if __name__ == "__main__":
    main()
