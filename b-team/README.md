# IBKR MCP Server

Interactive Brokers Model Context Protocol (MCP) Server for seamless integration with <PERSON>.

## Overview

This project provides a bridge between <PERSON> and Interactive Brokers' Trader Workstation (TWS) API, enabling AI-assisted trading and portfolio management through natural language interactions.

## Current Status

✅ **PRODUCTION READY** - Forensic examination complete. All critical issues resolved.

### Recent Achievements
- ✅ **Massive cleanup completed**: 1,912 lines of duplicate code removed (62% size reduction)
- ✅ **Duplicate registration issue resolved**: No more server startup warnings
- ✅ **Account parameter implementation**: All order functions now support multi-account trading
- ✅ **Professional project organization**: Clean directory structure with comprehensive documentation
- ✅ **Production validation**: Server tested and verified ready for live trading

### Production Metrics
- **Server file**: 1,150 lines (reduced from 3,063)
- **MCP registrations**: 9 tools, 1 prompt (no duplicates)
- **Account support**: Full multi-account trading capability
- **Error rate**: Zero duplicate warnings or conflicts

## Quick Start

### Prerequisites
- Interactive Brokers account with TWS or IB Gateway installed
- Python 3.9+
- Claude Des<PERSON> application

### Installation

1. **Install dependencies**:
   ```bash
   cd ibkr_mcp_server
   pip install -r requirements.txt
   ```

2. **Configure Claude Desktop**:
   Update your Claude Desktop configuration with the MCP server settings from `claude_desktop_config.json`.

3. **Start the server**:
   ```bash
   python3 mcp_server_main.py --host 127.0.0.1 --port 7497
   ```

## Project Structure

```
├── mcp_server_main.py           # Main server entry point
├── claude_desktop_config.json   # Claude Desktop MCP configuration
├── ibkr_mcp_server/            # Core application package
│   ├── app/                    # Application modules
│   │   ├── mcp/               # MCP server implementation
│   │   ├── services/          # Business logic services
│   │   ├── models/            # Data models
│   │   └── core/              # Core configuration
│   └── source/                # Interactive Brokers Python API
├── docs/                      # API documentation and guides
├── reports/                   # Project reports and analysis
├── tools/                     # Utility scripts for maintenance
├── logs/                      # Application log files
├── archive/                   # Archived development files
└── transcript/                # Development conversations
```

## Key Features

### Trading Operations
- **Order Management**: Place, modify, and cancel orders
- **Advanced Order Types**: Bracket orders, adaptive orders, auction orders
- **Portfolio Monitoring**: Real-time portfolio positions and P&L
- **Account Information**: Account summaries and balances

### Market Data
- **Real-time Quotes**: Live market data for stocks, options, futures
- **Contract Search**: Find and analyze financial instruments
- **Market Depth**: Level II order book data

### Integration
- **MCP Protocol**: Native Claude Desktop integration
- **TWS API**: Direct connection to Interactive Brokers
- **Error Handling**: Comprehensive error management and logging

## Usage Examples

Once configured with Claude Desktop, you can interact naturally:

- *"Show me my current portfolio positions"*
- *"Place a bracket order for 100 shares of AAPL at $150 with a stop at $145 and target at $160"*
- *"What's the current market data for TSLA?"*
- *"Search for option contracts for SPY expiring next month"*

## Configuration

### TWS/IB Gateway Settings
- **Paper Trading**: Port 7497 (recommended for testing)
- **Live Trading**: Port 7496 (production use)
- **Client ID**: Unique identifier (default: 1)

### Server Options
```bash
python3 mcp_server_main.py --help
```

## Development

### Running Tests
```bash
cd tools
python3 test_fixes.py
```

### Checking for Duplicates
```bash
cd tools
python3 find_duplicate_tools.py
```

### Debugging
Enable debug logging for detailed troubleshooting:
```bash
python3 mcp_server_main.py --debug
```

## Security

- **Paper Trading**: Always test with paper trading first
- **API Permissions**: Configure TWS API permissions carefully  
- **Log Security**: Logs may contain sensitive trading information
- **Network Access**: Restrict network access to trusted clients only

## Support

- **Documentation**: See `docs/` directory for detailed API guides
- **Reports**: Check `reports/` for implementation status and analysis
- **Logs**: Review `logs/` for troubleshooting information

## License

This project integrates with Interactive Brokers' Python API. Please review IB's API licensing terms and conditions.

---

*For detailed implementation reports and technical analysis, see the `reports/` directory.*
