#!/usr/bin/env python3
"""
Interactive Brokers MCP Server - Main Entry Point

This script starts the MCP server that provides Claude Desktop with access
to Interactive Brokers' TWS API through the existing IBKRService.
"""
import asyncio
import logging
import os
import sys
import argparse
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

print("--- SYS.PATH ---")
for p in sys.path:
    print(p)
print("----------------")

# Import the MCP server
try:
    # Import directly from the app level
    from ibkr_mcp_server.app.ibkr_mcp_server import mcp
    print("✅ Successfully imported MCP server")
    
    # Check if lifespan functions exist
    from ibkr_mcp_server.app.ibkr_mcp_server import lifespan
    print("✅ Successfully imported lifespan function")
    
except ImportError as e:
    print(f"❌ Error importing MCP server: {e}")
    sys.exit(1)

# Ensure lifespan is properly set
if not hasattr(mcp, 'lifespan') or mcp.lifespan is None:
    mcp.lifespan = lifespan
    print("Lifespan function explicitly set on MCP server")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%dT%H:%M:%S.%03dZ',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(project_root, 'ibkr_mcp_server.log'))
    ]
)
logger = logging.getLogger('ibkr-mcp-main')

async def main():
    """Main entry point for the MCP server"""
    parser = argparse.ArgumentParser(description='Interactive Brokers MCP Server')
    parser.add_argument('--host', type=str, default='127.0.0.1', help='Host to bind to')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind to')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    args = parser.parse_args()

    # Set log level based on debug flag
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Debug mode enabled")

    try:
        # Start the MCP server
        logger.info(f"Starting MCP server")
        # Use the run method to start the server
        mcp.run()
    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error(f"Error starting server: {str(e)}")
        raise
    finally:
        logger.info("Server shutdown complete")

if __name__ == "__main__":
    try:
        # Start the MCP server
        logger.info(f"Starting MCP server")
        mcp.run()
    except KeyboardInterrupt:
        print("\nServer shutdown by user")
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)
    finally:
        logger.info("Server shutdown complete")
