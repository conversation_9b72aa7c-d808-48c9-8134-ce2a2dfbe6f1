"""
Supabase Configuration and Integration for Trading System
"""
import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import asyncio
import aiohttp
from supabase import create_client, Client
import pandas as pd

# Supabase Configuration
SUPABASE_URL = "https://qwpovikethadrfwlyrzh.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF3cG92aWtldGhhZHJmd2x5cnpoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2NDY0MTgsImV4cCI6MjA2NDIyMjQxOH0.JnDjxWbzkZNdUxTx5B1vWUgCFh-QKek3fXNWJ8PGr9c"

# Initialize Supabase client with error handling
try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    print("✅ Supabase client initialized successfully")
except Exception as e:
    print(f"❌ Supabase client initialization failed: {e}")
    # Create a dummy client to prevent import errors
    class DummyClient:
        def table(self, name): return self
        def select(self, *args): return self
        def insert(self, *args): return self
        def update(self, *args): return self
        def eq(self, *args): return self
        def execute(self): return type('obj', (object,), {'data': []})()
    supabase = DummyClient()

def test_supabase_connection():
    """Test Supabase connection and return status"""
    try:
        # Try a simple query that should work
        result = supabase.table("auth.users").select("*").limit(1).execute()
        return True, "Connection successful"
    except Exception as e:
        return False, f"Connection failed: {str(e)}"

def get_supabase_status():
    """Get detailed Supabase connection status"""
    is_connected, message = test_supabase_connection()
    return {
        "connected": is_connected,
        "message": message,
        "url": SUPABASE_URL,
        "client_type": type(supabase).__name__
    }

class SupabaseTradeLogger:
    """Handles all Supabase operations for the trading system"""
    
    def __init__(self):
        self.client = supabase
        self.account_id = None
        
    async def initialize(self, account_id: str):
        """Initialize the logger with account ID"""
        self.account_id = account_id
        print(f"✓ Supabase logger initialized for account: {account_id}")
        
    async def log_trade(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Log a trade to Supabase"""
        try:
            trade_record = {
                "account_id": self.account_id,
                "symbol": trade_data.get("symbol"),
                "side": trade_data.get("action"),
                "quantity": trade_data.get("quantity"),
                "price": trade_data.get("price"),
                "order_type": trade_data.get("order_type", "MARKET"),
                "status": trade_data.get("status", "FILLED"),
                "commission": trade_data.get("commission", 0),
                "ib_order_id": trade_data.get("order_id"),
                "strategy_id": trade_data.get("strategy_id"),
                "execution_time": datetime.utcnow().isoformat()
            }
            
            response = self.client.table("trades").insert(trade_record).execute()
            print(f"✓ Trade logged: {trade_data['symbol']} {trade_data['action']} {trade_data['quantity']}@{trade_data['price']}")
            return response.data[0] if response.data else {}
            
        except Exception as e:
            print(f"✗ Error logging trade: {e}")
            return {}
    
    async def update_position(self, position_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update or create a position in Supabase"""
        try:
            # Check if position exists
            existing = self.client.table("positions").select("*").eq(
                "account_id", self.account_id
            ).eq(
                "symbol", position_data["symbol"]
            ).eq(
                "status", "OPEN"
            ).execute()
            
            position_record = {
                "account_id": self.account_id,
                "symbol": position_data["symbol"],
                "side": position_data["side"],
                "quantity": position_data["quantity"],
                "entry_price": position_data["avg_cost"],
                "current_price": position_data.get("market_price", position_data["avg_cost"]),
                "pnl": position_data.get("unrealized_pnl", 0),
                "pnl_percentage": position_data.get("pnl_percentage", 0),
                "current_value": position_data.get("market_value", 0),
                "status": "OPEN",
                "strategy_id": position_data.get("strategy_id")
            }
            
            if existing.data:
                # Update existing position
                response = self.client.table("positions").update(position_record).eq(
                    "id", existing.data[0]["id"]
                ).execute()
            else:
                # Create new position
                response = self.client.table("positions").insert(position_record).execute()
            
            print(f"✓ Position updated: {position_data['symbol']} {position_data['quantity']} @ ${position_data.get('market_price', 0):.2f}")
            return response.data[0] if response.data else {}
            
        except Exception as e:
            print(f"✗ Error updating position: {e}")
            return {}
    
    async def close_position(self, symbol: str, exit_price: float, exit_time: datetime = None):
        """Close a position in Supabase"""
        try:
            # Find open position
            position = self.client.table("positions").select("*").eq(
                "account_id", self.account_id
            ).eq(
                "symbol", symbol
            ).eq(
                "status", "OPEN"
            ).execute()
            
            if position.data:
                pos = position.data[0]
                final_pnl = (exit_price - pos["entry_price"]) * pos["quantity"]
                if pos["side"] == "SHORT":
                    final_pnl = -final_pnl
                
                # Update position to closed
                response = self.client.table("positions").update({
                    "status": "CLOSED",
                    "exit_price": exit_price,
                    "exit_time": (exit_time or datetime.utcnow()).isoformat(),
                    "pnl": final_pnl,
                    "current_price": exit_price
                }).eq("id", pos["id"]).execute()
                
                print(f"✓ Position closed: {symbol} with P&L: ${final_pnl:.2f}")
                return response.data[0] if response.data else {}
            
        except Exception as e:
            print(f"✗ Error closing position: {e}")
            return {}
    
    async def log_performance(self, performance_data: Dict[str, Any]):
        """Log performance metrics to Supabase"""
        try:
            record = {
                "account_id": self.account_id,
                "total_value": performance_data.get("total_value", 0),
                "cash_balance": performance_data.get("cash_balance", 0),
                "positions_value": performance_data.get("positions_value", 0),
                "daily_pnl": performance_data.get("daily_pnl", 0),
                "total_pnl": performance_data.get("total_pnl", 0),
                "win_rate": performance_data.get("win_rate", 0),
                "sharpe_ratio": performance_data.get("sharpe_ratio", 0),
                "max_drawdown": performance_data.get("max_drawdown", 0),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            response = self.client.table("performance_logs").insert(record).execute()
            print(f"✓ Performance logged: Total Value: ${performance_data['total_value']:.2f}, Daily P&L: ${performance_data['daily_pnl']:.2f}")
            
        except Exception as e:
            print(f"✗ Error logging performance: {e}")
    
    async def check_alerts(self, current_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for alert conditions and trigger if necessary"""
        try:
            # Get active alert conditions
            alerts = self.client.table("alert_conditions").select("*").eq(
                "active", True
            ).execute()
            
            triggered_alerts = []
            
            for alert in alerts.data:
                triggered = False
                
                if alert["condition_type"] == "POSITION_SIZE":
                    # Check position size alerts
                    if current_data.get("position_value", 0) > alert["threshold_value"]:
                        triggered = True
                        
                elif alert["condition_type"] == "DAILY_LOSS":
                    # Check daily loss alerts
                    if current_data.get("daily_pnl", 0) < -alert["threshold_value"]:
                        triggered = True
                        
                elif alert["condition_type"] == "PRICE_CHANGE":
                    # Check price change alerts
                    if alert["symbol"] == current_data.get("symbol"):
                        price_change = abs(current_data.get("price_change_pct", 0))
                        if price_change > alert["threshold_value"]:
                            triggered = True
                
                if triggered:
                    # Log alert
                    alert_log = {
                        "alert_condition_id": alert["id"],
                        "account_id": self.account_id,
                        "triggered_value": current_data.get(alert["condition_type"].lower(), 0),
                        "message": f"{alert['name']} triggered: {alert['condition_type']} exceeded threshold",
                        "severity": alert["severity"]
                    }
                    
                    response = self.client.table("alert_logs").insert(alert_log).execute()
                    triggered_alerts.append(alert_log)
                    print(f"⚠️  Alert triggered: {alert['name']}")
            
            return triggered_alerts
            
        except Exception as e:
            print(f"✗ Error checking alerts: {e}")
            return []
    
    async def get_active_strategies(self) -> List[Dict[str, Any]]:
        """Get all active trading strategies"""
        try:
            response = self.client.table("strategies").select("*").eq(
                "active", True
            ).execute()
            return response.data
        except Exception as e:
            print(f"✗ Error fetching strategies: {e}")
            return []
    
    async def update_strategy_performance(self, strategy_id: int, metrics: Dict[str, Any]):
        """Update strategy performance metrics"""
        try:
            response = self.client.table("strategies").update({
                "total_pnl": metrics.get("total_pnl", 0),
                "win_rate": metrics.get("win_rate", 0),
                "sharpe_ratio": metrics.get("sharpe_ratio", 0),
                "last_updated": datetime.utcnow().isoformat()
            }).eq("id", strategy_id).execute()
            
            print(f"✓ Strategy performance updated: ID {strategy_id}")
            
        except Exception as e:
            print(f"✗ Error updating strategy performance: {e}")


class RealtimeMonitor:
    """Monitors Supabase data in real-time"""
    
    def __init__(self, logger: SupabaseTradeLogger):
        self.logger = logger
        self.running = False
        
    async def start_monitoring(self, callback_fn=None):
        """Start real-time monitoring of positions and performance"""
        self.running = True
        print("🔄 Starting real-time monitoring...")
        
        while self.running:
            try:
                # Fetch current positions
                positions = supabase.table("positions").select("*").eq(
                    "status", "OPEN"
                ).execute()
                
                # Calculate metrics
                total_value = sum(pos["current_value"] for pos in positions.data)
                total_pnl = sum(pos["pnl"] for pos in positions.data)
                
                # Fetch today's performance
                today = datetime.utcnow().date().isoformat()
                daily_perf = supabase.table("performance_logs").select("*").gte(
                    "timestamp", today
                ).order("timestamp", desc=False).limit(1).execute()
                
                daily_pnl = 0
                if daily_perf.data:
                    daily_pnl = total_pnl - daily_perf.data[0].get("total_pnl", 0)
                
                current_data = {
                    "positions": positions.data,
                    "total_value": total_value,
                    "total_pnl": total_pnl,
                    "daily_pnl": daily_pnl,
                    "position_count": len(positions.data)
                }
                
                # Check alerts
                await self.logger.check_alerts({
                    "position_value": total_value,
                    "daily_pnl": daily_pnl
                })
                
                # Call callback if provided
                if callback_fn:
                    await callback_fn(current_data)
                
                # Wait 5 seconds before next update
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"✗ Monitoring error: {e}")
                await asyncio.sleep(5)
    
    def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.running = False
        print("⏹️  Monitoring stopped")


# Example usage
async def main():
    """Example of how to use the Supabase integration"""
    
    # Initialize logger
    logger = SupabaseTradeLogger()
    await logger.initialize("YOUR_IB_ACCOUNT_ID")
    
    # Log a sample trade
    await logger.log_trade({
        "symbol": "AAPL",
        "action": "BUY",
        "quantity": 100,
        "price": 175.50,
        "order_type": "LIMIT",
        "status": "FILLED",
        "commission": 1.00,
        "order_id": "12345",
        "strategy_id": 1
    })
    
    # Update a position
    await logger.update_position({
        "symbol": "AAPL",
        "side": "LONG",
        "quantity": 100,
        "avg_cost": 175.50,
        "market_price": 176.00,
        "unrealized_pnl": 50.00,
        "pnl_percentage": 0.28,
        "market_value": 17600.00,
        "strategy_id": 1
    })
    
    # Log performance
    await logger.log_performance({
        "total_value": 150000,
        "cash_balance": 50000,
        "positions_value": 100000,
        "daily_pnl": 500,
        "total_pnl": 5000,
        "win_rate": 0.65,
        "sharpe_ratio": 1.5,
        "max_drawdown": 0.05
    })
    
    # Start monitoring
    monitor = RealtimeMonitor(logger)
    
    async def on_update(data):
        print(f"📊 Update: {data['position_count']} positions, Total P&L: ${data['total_pnl']:.2f}")
    
    # Run monitoring for 30 seconds as example
    monitoring_task = asyncio.create_task(monitor.start_monitoring(on_update))
    await asyncio.sleep(30)
    monitor.stop_monitoring()
    await monitoring_task


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())
