#!/usr/bin/env python3
"""
Quick Tool Count Check
Run this to quickly verify tool counts without starting the full server
"""

def check_tool_definitions():
    """Count tool definitions by parsing the file"""
    import re
    
    file_path = "/Users/<USER>/IBKR/b-team/integrated_mcp_server_133_tools.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find all @mcp.tool() decorators
    tool_pattern = r'@mcp\.tool\(\)'
    tools = re.findall(tool_pattern, content)
    
    print(f"🔧 Tool Definitions Found: {len(tools)}")
    
    # Find tool function names
    func_pattern = r'@mcp\.tool\(\)\s*(?:async\s+)?def\s+(\w+)'
    tool_functions = re.findall(func_pattern, content)
    
    print(f"📋 Tool Functions:")
    for i, func in enumerate(tool_functions, 1):
        print(f"   {i:3d}. {func}")
    
    return len(tools)

def check_get_tool_count_function():
    """Check what the get_tool_count function returns"""
    file_path = "/Users/<USER>/IBKR/b-team/integrated_mcp_server_133_tools.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the get_tool_count function
    import re
    pattern = r'async def get_tool_count.*?"total_tools":\s*(\d+)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        count = int(match.group(1))
        print(f"📊 get_tool_count() returns: {count}")
        return count
    else:
        print("❌ Could not find get_tool_count function")
        return None

if __name__ == "__main__":
    print("🔍 Quick Tool Count Verification")
    print("="*40)
    
    # Count actual tool definitions
    actual_tools = check_tool_definitions()
    
    # Check what get_tool_count returns
    reported_tools = check_get_tool_count_function()
    
    print(f"\n📊 SUMMARY:")
    print(f"   Actual tool definitions: {actual_tools}")
    print(f"   Reported by get_tool_count(): {reported_tools}")
    
    if actual_tools == 133 and reported_tools == 133:
        print(f"✅ SUCCESS: Both counts match 133!")
    elif actual_tools == reported_tools:
        print(f"⚠️  CONSISTENT: Counts match but not 133 ({actual_tools})")
    else:
        print(f"❌ MISMATCH: Definition count ({actual_tools}) != Reported count ({reported_tools})")
