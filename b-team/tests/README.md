# IBKR MCP Server Fix Testing Guide

This directory contains test scripts to validate the fixes applied to the IBKR MCP server.

## Prerequisites

1. **TWS or IB Gateway must be running**
   - Use TWS Paper Trading (port 7497) for testing
   - Enable API connections in TWS: File → Global Configuration → API → Settings
   - Check "Enable ActiveX and Socket Clients"
   - Check "Download open orders on connection"

2. **MCP Server must be running**
   ```bash
   cd /Users/<USER>/projects/b-team/ibkr_mcp_server
   python app/ibkr_mcp_server.py
   ```

## Test Scripts

### 1. `validate_fixes.py` - Validate Code Fixes
Run this first to ensure all fixes are properly applied to the codebase:

```bash
python validate_fixes.py
```

This checks that all code modifications from Phases 1-5 are present.

### 2. `test_mcp_basic.py` - Basic Connectivity Test
Tests basic MCP server functionality:

```bash
python test_mcp_basic.py
```

This tests:
- MCP server health
- TWS connection status
- Account summary retrieval
- Contract search

### 3. `test_ibkr_fixes.py` - Comprehensive Fix Validation
Full test suite for all fixed functionality:

```bash
python test_ibkr_fixes.py
```

This tests:
1. **Connection** - TWS connectivity
2. **Bracket Orders** - Critical fix for order placement
3. **Real-time Data** - 5-second bar subscriptions
4. **Scanner** - Market scanner functionality
5. **Risk Calculations** - VaR and risk metrics
6. **Contract Search** - Multi-asset search (futures, forex)

## Expected Results

### ✅ All Fixes Working Properly
- All validation checks pass
- Connection to TWS successful
- Bracket orders place without account conversion errors
- Real-time data streams properly
- Scanner returns market data
- Risk calculations complete
- Contract searches return results

### ⚠️ Common Issues

1. **Connection Failed**
   - Ensure TWS is running with API enabled
   - Check port configuration (7497 for paper, 7496 for live)
   - Verify firewall isn't blocking connections

2. **Account Field Error in Orders**
   - The fix implements a workaround by omitting the account field
   - You'll see: "WORKAROUND: Account field omitted due to ib_async conversion bug"
   - This is expected behavior until ib_async library is fixed

3. **No Real-time Data**
   - Ensure you have market data subscriptions
   - Check if markets are open
   - Verify symbol is valid

## Fix Summary

### Phase 1: Order Management ✅
- Fixed bracket order account parameter conversion error
- Implemented workaround for ib_async bug
- All order types now functional

### Phase 2: Contract Search ✅
- Fixed futures contract search
- Fixed forex pair search with IDEALPRO exchange
- Enhanced contract details

### Phase 3: Real-time Data ✅
- Fixed subscription methods using ib_async API
- Implemented proper event handlers
- Added data caching

### Phase 4: Scanner ✅
- Fixed scanner data requests
- Implemented subscription handlers
- Added result conversion

### Phase 5: Risk Management ✅
- Fixed portfolio position retrieval
- Corrected historical data methods
- Updated market data retrieval

## Next Steps

After validating the fixes:

1. **For Production Use**
   - Test with paper trading account first
   - Monitor for any ib_async library updates
   - Consider implementing retry logic for API calls

2. **Known Limitations**
   - Account field omitted in orders (workaround for ib_async bug)
   - News service requires additional configuration
   - Some advanced order types may need additional testing

3. **Monitoring**
   - Check MCP server logs for any errors
   - Monitor TWS API connection stability
   - Track order execution accuracy

## Troubleshooting

If tests fail:

1. Check MCP server logs:
   ```bash
   tail -f mcp-server-ibkr-trading.log
   ```

2. Verify TWS API settings
3. Ensure market data subscriptions are active
4. Check network connectivity

## Support

For issues with:
- TWS API: Check Interactive Brokers documentation
- ib_async library: See https://github.com/ib-api-reloaded/ib_async
- MCP Protocol: Refer to Anthropic's MCP documentation
