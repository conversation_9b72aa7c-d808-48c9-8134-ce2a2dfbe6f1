"""
Simple test to verify IBKR MCP server is running and basic functionality works.
Run this after starting the MCP server.
"""

import requests
import json
import time

# MCP server endpoint (adjust if running on different port)
MCP_SERVER_URL = "http://localhost:8000"

def test_server_health():
    """Test if MCP server is running"""
    print("Testing MCP server health...")
    try:
        # Try different possible endpoints
        endpoints = [
            f"{MCP_SERVER_URL}/health",
            f"{MCP_SERVER_URL}/api/health",
            f"{MCP_SERVER_URL}/",
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, timeout=5)
                if response.status_code == 200:
                    print(f"✅ Server is running at {endpoint}")
                    return True
            except:
                continue
        
        print("❌ Server health check failed - server may not be running")
        return False
    except Exception as e:
        print(f"❌ Error checking server health: {e}")
        return False

def test_connection_status():
    """Test connection status to TWS"""
    print("\nTesting TWS connection status...")
    
    # Try to call the connection status tool
    payload = {
        "jsonrpc": "2.0",
        "method": "call_tool",
        "params": {
            "tool_name": "get_connection_status",
            "arguments": {}
        },
        "id": 1
    }
    
    try:
        response = requests.post(
            f"{MCP_SERVER_URL}/rpc",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if "result" in result:
                status = result["result"]
                connected = status.get("connection_info", {}).get("connected", False)
                if connected:
                    print(f"✅ Connected to TWS")
                    print(f"   Host: {status['connection_info'].get('host')}")
                    print(f"   Port: {status['connection_info'].get('port')}")
                else:
                    print("⚠️  Not connected to TWS")
                    print("   Make sure TWS or IB Gateway is running")
                return connected
            else:
                print(f"❌ Invalid response: {result}")
        else:
            print(f"❌ HTTP error {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
    
    return False

def test_account_summary():
    """Test getting account summary"""
    print("\nTesting account summary...")
    
    payload = {
        "jsonrpc": "2.0",
        "method": "call_tool",
        "params": {
            "tool_name": "get_account_summary",
            "arguments": {}
        },
        "id": 2
    }
    
    try:
        response = requests.post(
            f"{MCP_SERVER_URL}/rpc",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if "result" in result and result["result"].get("status") == "success":
                print("✅ Account summary retrieved successfully")
                summary = result["result"].get("account_summary", {})
                if summary:
                    print(f"   Net Liquidation: {summary.get('NetLiquidation', {}).get('value', 'N/A')}")
                    print(f"   Total Cash: {summary.get('TotalCashValue', {}).get('value', 'N/A')}")
                return True
            else:
                print(f"❌ Failed to get account summary: {result}")
        else:
            print(f"❌ HTTP error {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Error getting account summary: {e}")
    
    return False

def test_contract_search():
    """Test contract search functionality"""
    print("\nTesting contract search...")
    
    payload = {
        "jsonrpc": "2.0",
        "method": "call_tool",
        "params": {
            "tool_name": "search_contracts",
            "arguments": {
                "pattern": "AAPL",
                "sec_type": "STK"
            }
        },
        "id": 3
    }
    
    try:
        response = requests.post(
            f"{MCP_SERVER_URL}/rpc",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if "result" in result and result["result"].get("status") == "success":
                contracts = result["result"].get("contracts", [])
                print(f"✅ Contract search successful - found {len(contracts)} contracts")
                if contracts:
                    print(f"   First contract: {contracts[0].get('symbol')} - {contracts[0].get('name', 'N/A')}")
                return True
            else:
                print(f"❌ Contract search failed: {result}")
        else:
            print(f"❌ HTTP error {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Error searching contracts: {e}")
    
    return False

def main():
    """Run all tests"""
    print("="*60)
    print("IBKR MCP Server Basic Functionality Test")
    print("="*60)
    print(f"Testing server at: {MCP_SERVER_URL}")
    print(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    results = {
        "Server Health": test_server_health(),
        "TWS Connection": test_connection_status(),
        "Account Summary": False,
        "Contract Search": False
    }
    
    # Only test account and contracts if connected
    if results["TWS Connection"]:
        results["Account Summary"] = test_account_summary()
        results["Contract Search"] = test_contract_search()
    else:
        print("\n⚠️  Skipping account and contract tests - not connected to TWS")
    
    # Summary
    print("\n" + "="*60)
    print("Test Summary")
    print("="*60)
    
    passed = sum(1 for v in results.values() if v)
    total = len(results)
    
    for test, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test}: {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed!")
    elif passed > 0:
        print("\n⚠️  Some tests failed. Check the output above.")
    else:
        print("\n❌ All tests failed. Make sure:")
        print("   1. The MCP server is running")
        print("   2. TWS or IB Gateway is running")
        print("   3. API connections are enabled in TWS")

if __name__ == "__main__":
    main()
