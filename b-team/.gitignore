# Python virtual environment
.venv_ibkr/
.venv/
venv/
ENV/
env/
ibkr-mcp-server/venv/
ibkr-mcp-server/.venv_ibkr/

# Python bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Jupyter Notebook
.ipynb_checkpoints

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~
.spyderproject
.spyproject
.ropeproject

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs and databases
*.log
*.sql
*.sqlite
*.db

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Project specific - Interactive Brokers
tws.xml
*.jar
.jts/

# Temporary files
*.bak
*.tmp
.pytest_cache/

# Project specific directories
reports/
tests/
