**Stock Contract Search**



Starting in API **v973.02** and TWS **v964**, a function [**IBApi::EClient::reqMatchingSymbols**](https://interactivebrokers.github.io/tws-api/classIBApi_1_1EClient.html#aa0bff193c5cbaa73e89dccdd0f027195) is available to search for stock contracts. The input can be either the first few letters of the ticker symbol or a character sequence matching a word in the security name for longer strings. For instance, to search for the stock symbol 'IBKR', the input 'I' or 'IB' and the word 'Interactive' can be used. Up to 16 matching results are returned.

- There must be at least 1 second between successive calls to reqMatchingSymbols

```
     self.reqMatchingSymbols(218, "IBM")
```

Matching stock contracts are returned to [**IBApi::EWrapper::symbolSamples**](https://interactivebrokers.github.io/tws-api/interfaceIBApi_1_1EWrapper.html#a82cfd0c663944c5e6d4fee8ebd482ecf) with information about types of derivative contracts which exist (warrants, options, dutch warrants, futures).

```
def symbolSamples(self, reqId: int,
            contractDescriptions: ListOfContractDescription):
     super().symbolSamples(reqId, contractDescriptions)
     print("Symbol Samples. Request Id: ", reqId)

     for contractDescription in contractDescriptions:
       derivSecTypes = ""
       for derivSecType in contractDescription.derivativeSecTypes:
         derivSecTypes += " "
         derivSecTypes += derivSecType
       print("Contract: conId:%s, symbol:%s, secType:%s primExchange:%s, "
          "currency:%s, derivativeSecTypes:%s, description:%s, issuerId:%s" % (
         contractDescription.contract.conId,
         contractDescription.contract.symbol,
         contractDescription.contract.secType,
         contractDescription.contract.primaryExchange,
         contractDescription.contract.currency, derivSecTypes,
         contractDescription.contract.description,
         contractDescription.contract.issuerId))
```

