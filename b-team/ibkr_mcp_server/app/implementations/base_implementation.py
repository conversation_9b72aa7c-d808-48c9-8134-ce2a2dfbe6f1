"""
CRITICAL FIX: Enhanced Base Implementation with Emergency Service Injection
This fixes the service injection problem at the implementation level.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)

class BaseImplementation(ABC):
    """Enhanced base class for all implementations with emergency service injection"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        
        # Store other services if provided
        for key, value in kwargs.items():
            setattr(self, key, value)
        
        # 🔧 CRITICAL FIX: Emergency service injection if service not provided
        if not self.ibkr_service:
            self._emergency_service_injection()
    
    def _emergency_service_injection(self):
        """
        🔧 EMERGENCY FIX: Inject global IBKR service if not provided during initialization
        This is the critical fix for the service injection problem
        """
        try:
            from services.ibkr_service import ibkr_service as global_ibkr_service
            if global_ibkr_service:
                self.ibkr_service = global_ibkr_service
                logger.info(f"🔧 Emergency injection: {self.__class__.__name__} using global IBKR service")
            else:
                logger.warning(f"⚠️  {self.__class__.__name__}: Global IBKR service not available")
        except ImportError:
            logger.error(f"❌ {self.__class__.__name__}: Cannot import global IBKR service")
    
    def ensure_service_available(self) -> bool:
        """
        🛡️  Ensure IBKR service is available before operations
        Returns True if service is available and connected
        """
        if not self.ibkr_service:
            # Try emergency injection one more time
            self._emergency_service_injection()
        
        if not self.ibkr_service:
            logger.error(f"❌ {self.__class__.__name__}: IBKR service not available")
            return False
        
        if not self.ibkr_service.connected:
            logger.warning(f"⚠️  {self.__class__.__name__}: IBKR service not connected")
            return False
        
        return True
    
    def create_error_response(self, message: str, context: str = "") -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            "status": "error",
            "message": message,
            "implementation": self.__class__.__name__,
            "context": context,
            "service_available": bool(self.ibkr_service),
            "service_connected": getattr(self.ibkr_service, 'connected', False) if self.ibkr_service else False
        }
    
    def create_success_response(self, data: Any, message: str = "Operation successful") -> Dict[str, Any]:
        """Create standardized success response"""
        return {
            "status": "success",
            "message": message,
            "data": data,
            "implementation": self.__class__.__name__
        }
