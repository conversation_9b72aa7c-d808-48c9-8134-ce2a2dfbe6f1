"""
MarketDepth Implementation - Layer 3: Business Logic
Core business logic for MarketDepth operations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, List, Callable
import logging
import asyncio
from datetime import datetime
from decimal import Decimal

logger = logging.getLogger(__name__)

class MarketDepthImplementation:
    """Core implementation for MarketDepth operations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        self.market_data_service = kwargs.get('market_data_streaming_service')
        self._active_subscriptions = {}
        
        if not self.ibkr_service:
            logger.warning(f"MarketDepthImplementation initialized without IBKR service")
    
    async def get_market_depth(
        self,
        symbol: str,
        num_rows: int = 5,
        is_smart_depth: bool = False
    ) -> Dict[str, Any]:
        """
        Get Level II market depth data (order book)
        
        Args:
            symbol: Symbol to get depth for
            num_rows: Number of depth levels on each side (max 5)
            is_smart_depth: Whether to consolidate across exchanges
            
        Returns:
            Market depth data with bids and asks
        """
        try:
            if not self.ibkr_service:
                return {
                    "status": "error",
                    "message": "IBKR service not available"
                }
            
            # Use market data service if available, fallback to direct IBKR
            if self.market_data_service:
                result = await self.market_data_service.get_market_depth(symbol, num_rows)
                return result
            
            # Direct IBKR implementation
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Request market depth
            depth_ticker = await self.ibkr_service.ib.reqMktDepthAsync(
                contract=contract,
                numRows=min(num_rows, 5),  # Max 5 rows
                isSmartDepth=is_smart_depth
            )
            
            # Format the response
            bids = []
            asks = []
            
            # Extract bid data
            if hasattr(depth_ticker, 'domBids'):
                for bid in depth_ticker.domBids[:num_rows]:
                    bids.append({
                        "price": float(bid.price) if bid.price else None,
                        "size": float(bid.size) if bid.size else None,
                        "marketMaker": bid.marketMaker if hasattr(bid, 'marketMaker') else None,
                        "exchange": getattr(bid, 'exchange', None)
                    })
            
            # Extract ask data
            if hasattr(depth_ticker, 'domAsks'):
                for ask in depth_ticker.domAsks[:num_rows]:
                    asks.append({
                        "price": float(ask.price) if ask.price else None,
                        "size": float(ask.size) if ask.size else None,
                        "marketMaker": ask.marketMaker if hasattr(ask, 'marketMaker') else None,
                        "exchange": getattr(ask, 'exchange', None)
                    })
            
            # Calculate spread and metrics
            best_bid = bids[0]["price"] if bids and bids[0]["price"] else None
            best_ask = asks[0]["price"] if asks and asks[0]["price"] else None
            spread = (best_ask - best_bid) if best_bid and best_ask else None
            
            total_bid_size = sum(bid["size"] for bid in bids if bid["size"])
            total_ask_size = sum(ask["size"] for ask in asks if ask["size"])
            
            return {
                "status": "success",
                "symbol": symbol,
                "bids": bids,
                "asks": asks,
                "best_bid": best_bid,
                "best_ask": best_ask,
                "spread": spread,
                "total_bid_size": total_bid_size,
                "total_ask_size": total_ask_size,
                "num_rows": len(bids) if len(bids) > len(asks) else len(asks),
                "is_smart_depth": is_smart_depth,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get market depth for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to get market depth: {str(e)}",
                "symbol": symbol
            }
    
    async def stream_market_depth(
        self,
        symbol: str,
        num_rows: int = 5,
        is_smart_depth: bool = False,
        callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Stream real-time market depth updates
        
        Args:
            symbol: Symbol to stream depth for
            num_rows: Number of depth levels
            is_smart_depth: Whether to consolidate across exchanges
            callback: Optional callback for depth updates
            
        Returns:
            Streaming subscription details
        """
        try:
            if not self.ibkr_service:
                return {
                    "status": "error",
                    "message": "IBKR service not available"
                }
            
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Generate unique request ID
            req_id = len(self._active_subscriptions) + 1000
            
            # Set up depth update handlers
            def on_depth_update(ticker, operation, side, price, size):
                depth_data = {
                    "symbol": symbol,
                    "operation": operation,  # 0=insert, 1=update, 2=delete
                    "side": side,  # 0=ask, 1=bid
                    "price": float(price) if price else None,
                    "size": float(size) if size else None,
                    "timestamp": datetime.now().isoformat()
                }
                
                if callback:
                    asyncio.create_task(callback(depth_data))
            
            # Subscribe to market depth
            depth_ticker = await self.ibkr_service.ib.reqMktDepthAsync(
                contract=contract,
                numRows=min(num_rows, 5),
                isSmartDepth=is_smart_depth
            )
            
            # Store subscription
            subscription_id = f"depth_{symbol}_{req_id}"
            self._active_subscriptions[subscription_id] = {
                "symbol": symbol,
                "req_id": req_id,
                "ticker": depth_ticker,
                "contract": contract,
                "active": True,
                "start_time": datetime.now().isoformat()
            }
            
            return {
                "status": "success",
                "subscription_id": subscription_id,
                "symbol": symbol,
                "num_rows": num_rows,
                "is_smart_depth": is_smart_depth,
                "message": "Market depth streaming started"
            }
            
        except Exception as e:
            logger.error(f"Failed to start market depth streaming for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to start streaming: {str(e)}",
                "symbol": symbol
            }
    
    async def cancel_market_depth(self, subscription_id: str) -> Dict[str, Any]:
        """
        Cancel market depth streaming subscription
        
        Args:
            subscription_id: The subscription ID to cancel
            
        Returns:
            Cancellation status
        """
        try:
            if subscription_id not in self._active_subscriptions:
                return {
                    "status": "error",
                    "message": f"Subscription {subscription_id} not found"
                }
            
            subscription = self._active_subscriptions[subscription_id]
            
            if not self.ibkr_service:
                return {
                    "status": "error",
                    "message": "IBKR service not available"
                }
            
            # Cancel the market depth subscription
            self.ibkr_service.ib.cancelMktDepth(
                reqId=subscription["req_id"],
                isSmartDepth=subscription.get("is_smart_depth", False)
            )
            
            # Mark as inactive and remove
            subscription["active"] = False
            subscription["end_time"] = datetime.now().isoformat()
            del self._active_subscriptions[subscription_id]
            
            return {
                "status": "success",
                "subscription_id": subscription_id,
                "symbol": subscription["symbol"],
                "message": "Market depth streaming cancelled"
            }
            
        except Exception as e:
            logger.error(f"Failed to cancel market depth subscription {subscription_id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to cancel subscription: {str(e)}",
                "subscription_id": subscription_id
            }
    
    async def get_order_book_analysis(
        self,
        symbol: str,
        num_rows: int = 5
    ) -> Dict[str, Any]:
        """
        Get detailed order book analysis and metrics
        
        Args:
            symbol: Symbol to analyze
            num_rows: Number of depth levels to analyze
            
        Returns:
            Order book analysis with metrics
        """
        try:
            # Get current market depth
            depth_data = await self.get_market_depth(symbol, num_rows)
            
            if depth_data["status"] != "success":
                return depth_data
            
            bids = depth_data["bids"]
            asks = depth_data["asks"]
            
            # Calculate advanced metrics
            analysis = {
                "symbol": symbol,
                "basic_metrics": {
                    "best_bid": depth_data["best_bid"],
                    "best_ask": depth_data["best_ask"],
                    "spread": depth_data["spread"],
                    "spread_pct": (depth_data["spread"] / depth_data["best_bid"] * 100) if depth_data["best_bid"] and depth_data["spread"] else None
                },
                "depth_metrics": {},
                "liquidity_metrics": {},
                "market_quality": {},
                "timestamp": datetime.now().isoformat()
            }
            
            # Depth metrics
            if bids and asks:
                bid_prices = [b["price"] for b in bids if b["price"]]
                ask_prices = [a["price"] for a in asks if a["price"]]
                bid_sizes = [b["size"] for b in bids if b["size"]]
                ask_sizes = [a["size"] for a in asks if a["size"]]
                
                analysis["depth_metrics"] = {
                    "total_bid_volume": sum(bid_sizes),
                    "total_ask_volume": sum(ask_sizes),
                    "avg_bid_size": sum(bid_sizes) / len(bid_sizes) if bid_sizes else 0,
                    "avg_ask_size": sum(ask_sizes) / len(ask_sizes) if ask_sizes else 0,
                    "bid_levels": len(bids),
                    "ask_levels": len(asks),
                    "price_depth_bid": max(bid_prices) - min(bid_prices) if bid_prices else 0,
                    "price_depth_ask": max(ask_prices) - min(ask_prices) if ask_prices else 0
                }
                
                # Liquidity metrics
                total_volume = sum(bid_sizes) + sum(ask_sizes)
                analysis["liquidity_metrics"] = {
                    "total_volume": total_volume,
                    "bid_ask_ratio": sum(bid_sizes) / sum(ask_sizes) if sum(ask_sizes) > 0 else None,
                    "volume_imbalance": (sum(bid_sizes) - sum(ask_sizes)) / total_volume if total_volume > 0 else 0,
                    "weighted_mid": self._calculate_weighted_mid(bids, asks),
                    "effective_spread": self._calculate_effective_spread(bids, asks)
                }
                
                # Market quality metrics
                analysis["market_quality"] = {
                    "market_impact_buy": self._calculate_market_impact(asks, "buy"),
                    "market_impact_sell": self._calculate_market_impact(bids, "sell"),
                    "resilience_score": self._calculate_resilience_score(bids, asks),
                    "depth_score": self._calculate_depth_score(bids, asks)
                }
            
            return {
                "status": "success",
                "analysis": analysis
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze order book for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to analyze order book: {str(e)}",
                "symbol": symbol
            }
    
    def _calculate_weighted_mid(self, bids: List[Dict], asks: List[Dict]) -> Optional[float]:
        """Calculate volume-weighted midpoint"""
        if not bids or not asks:
            return None
        
        best_bid = bids[0]["price"]
        best_ask = asks[0]["price"]
        bid_size = bids[0]["size"]
        ask_size = asks[0]["size"]
        
        if not all([best_bid, best_ask, bid_size, ask_size]):
            return None
        
        total_size = bid_size + ask_size
        weighted_mid = (best_bid * ask_size + best_ask * bid_size) / total_size
        return weighted_mid
    
    def _calculate_effective_spread(self, bids: List[Dict], asks: List[Dict]) -> Optional[float]:
        """Calculate effective spread considering volume"""
        if not bids or not asks or len(bids) < 2 or len(asks) < 2:
            return None
        
        # Use top 2 levels for effective spread
        bid_avg = (bids[0]["price"] + bids[1]["price"]) / 2 if bids[0]["price"] and bids[1]["price"] else None
        ask_avg = (asks[0]["price"] + asks[1]["price"]) / 2 if asks[0]["price"] and asks[1]["price"] else None
        
        if bid_avg and ask_avg:
            return ask_avg - bid_avg
        return None
    
    def _calculate_market_impact(self, levels: List[Dict], side: str) -> float:
        """Calculate market impact for given size"""
        if not levels:
            return 0.0
        
        # Calculate impact for a theoretical 1000 share order
        target_size = 1000
        cumulative_size = 0
        weighted_price = 0
        total_cost = 0
        
        for level in levels:
            if not level["price"] or not level["size"]:
                continue
                
            level_size = min(level["size"], target_size - cumulative_size)
            level_cost = level_size * level["price"]
            total_cost += level_cost
            cumulative_size += level_size
            
            if cumulative_size >= target_size:
                break
        
        if cumulative_size > 0:
            avg_price = total_cost / cumulative_size
            best_price = levels[0]["price"] if levels[0]["price"] else 0
            impact = abs(avg_price - best_price) / best_price if best_price > 0 else 0
            return impact
        
        return 0.0
    
    def _calculate_resilience_score(self, bids: List[Dict], asks: List[Dict]) -> float:
        """Calculate market resilience score (0-1)"""
        if not bids or not asks:
            return 0.0
        
        # Simple resilience based on depth and tightness
        total_levels = len(bids) + len(asks)
        max_levels = 10  # 5 on each side
        depth_score = min(total_levels / max_levels, 1.0)
        
        # Tightness score based on spread
        best_bid = bids[0]["price"] if bids[0]["price"] else 0
        best_ask = asks[0]["price"] if asks[0]["price"] else 0
        
        if best_bid > 0 and best_ask > 0:
            spread_pct = (best_ask - best_bid) / best_bid
            tightness_score = max(0, 1 - spread_pct * 100)  # Lower spread = higher score
        else:
            tightness_score = 0
        
        # Combined resilience score
        resilience = (depth_score * 0.6 + tightness_score * 0.4)
        return min(resilience, 1.0)
    
    def _calculate_depth_score(self, bids: List[Dict], asks: List[Dict]) -> float:
        """Calculate depth score based on volume distribution"""
        if not bids or not asks:
            return 0.0
        
        bid_sizes = [b["size"] for b in bids if b["size"]]
        ask_sizes = [a["size"] for a in asks if a["size"]]
        
        if not bid_sizes or not ask_sizes:
            return 0.0
        
        # Score based on volume at multiple levels
        total_bid_volume = sum(bid_sizes)
        total_ask_volume = sum(ask_sizes)
        total_volume = total_bid_volume + total_ask_volume
        
        if total_volume == 0:
            return 0.0
        
        # Higher score for more evenly distributed volume
        top_level_volume = (bid_sizes[0] if bid_sizes else 0) + (ask_sizes[0] if ask_sizes else 0)
        concentration = top_level_volume / total_volume if total_volume > 0 else 1
        
        # Lower concentration = higher depth score
        depth_score = max(0, 1 - concentration)
        return depth_score
    
    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """Get all active market depth subscriptions"""
        return {
            "status": "success",
            "active_subscriptions": list(self._active_subscriptions.keys()),
            "subscription_details": {
                sub_id: {
                    "symbol": sub["symbol"],
                    "active": sub["active"],
                    "start_time": sub["start_time"]
                }
                for sub_id, sub in self._active_subscriptions.items()
            },
            "total_count": len(self._active_subscriptions)
        }

