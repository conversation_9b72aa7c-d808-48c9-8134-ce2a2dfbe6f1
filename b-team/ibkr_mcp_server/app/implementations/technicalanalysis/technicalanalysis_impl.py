"""
TechnicalAnalysis Implementation - Layer 3: Business Logic
Core business logic for TechnicalAnalysis operations.
Provides comprehensive technical analysis including indicators, pattern recognition, and trend analysis.
"""

from typing import Dict, Any, Optional, List, Union, Tuple
import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
import math
from statistics import mean, stdev

logger = logging.getLogger(__name__)

class TechnicalAnalysisImplementation:
    """Core implementation for TechnicalAnalysis operations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        
        # Technical analysis parameters
        self.default_period = 20
        self.default_lookback_days = 252
        self.signal_threshold = 0.02  # 2% threshold for signals
        
        if not self.ibkr_service:
            logger.warning(f"TechnicalAnalysisImplementation initialized without IBKR service")
    
    async def calculate_technical_indicators(self,
                                           symbol: str,
                                           indicators: List[str],
                                           period: int = 20,
                                           lookback_days: int = 252) -> Dict[str, Any]:
        """
        Calculate technical indicators for a symbol
        
        Args:
            symbol: Symbol to analyze
            indicators: List of indicators to calculate
            period: Period for indicator calculations
            lookback_days: Number of days of historical data
            
        Returns:
            Dict containing calculated indicators
        """
        try:
            logger.info(f"Calculating technical indicators for {symbol}")
            
            # Get historical price data
            price_data = await self._get_historical_price_data(symbol, lookback_days)
            if not price_data:
                return {
                    "status": "error",
                    "message": f"No price data available for {symbol}"
                }
            
            # Calculate requested indicators
            calculated_indicators = {}
            
            for indicator in indicators:
                if indicator.upper() == "SMA":
                    calculated_indicators["sma"] = self._calculate_sma(price_data, period)
                elif indicator.upper() == "EMA":
                    calculated_indicators["ema"] = self._calculate_ema(price_data, period)
                elif indicator.upper() == "RSI":
                    calculated_indicators["rsi"] = self._calculate_rsi(price_data, period)
                elif indicator.upper() == "MACD":
                    calculated_indicators["macd"] = self._calculate_macd(price_data)
                elif indicator.upper() == "BOLLINGER":
                    calculated_indicators["bollinger_bands"] = self._calculate_bollinger_bands(price_data, period)
                elif indicator.upper() == "STOCHASTIC":
                    calculated_indicators["stochastic"] = self._calculate_stochastic(price_data, period)
                elif indicator.upper() == "ATR":
                    calculated_indicators["atr"] = self._calculate_atr(price_data, period)
                elif indicator.upper() == "ADX":
                    calculated_indicators["adx"] = self._calculate_adx(price_data, period)
                elif indicator.upper() == "CCI":
                    calculated_indicators["cci"] = self._calculate_cci(price_data, period)
                elif indicator.upper() == "WILLIAMS_R":
                    calculated_indicators["williams_r"] = self._calculate_williams_r(price_data, period)
                else:
                    logger.warning(f"Unknown indicator: {indicator}")
            
            # Generate trading signals
            signals = self._generate_trading_signals(calculated_indicators, price_data)
            
            return {
                "status": "success",
                "symbol": symbol,
                "indicators": calculated_indicators,
                "signals": signals,
                "current_price": price_data[-1]["close"] if price_data else None,
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to calculate indicators: {str(e)}"
            }
    
    async def identify_chart_patterns(self,
                                    symbol: str,
                                    pattern_types: Optional[List[str]] = None,
                                    lookback_days: int = 100) -> Dict[str, Any]:
        """
        Identify chart patterns in price data
        
        Args:
            symbol: Symbol to analyze
            pattern_types: List of pattern types to search for
            lookback_days: Number of days to analyze
            
        Returns:
            Dict containing identified patterns
        """
        try:
            logger.info(f"Identifying chart patterns for {symbol}")
            
            if pattern_types is None:
                pattern_types = ["head_and_shoulders", "triangle", "flag", "support_resistance", "double_top", "double_bottom"]
            
            # Get historical price data
            price_data = await self._get_historical_price_data(symbol, lookback_days)
            if not price_data:
                return {
                    "status": "error",
                    "message": f"No price data available for {symbol}"
                }
            
            identified_patterns = {}
            
            for pattern_type in pattern_types:
                if pattern_type == "head_and_shoulders":
                    identified_patterns["head_and_shoulders"] = self._identify_head_and_shoulders(price_data)
                elif pattern_type == "triangle":
                    identified_patterns["triangle"] = self._identify_triangle_patterns(price_data)
                elif pattern_type == "flag":
                    identified_patterns["flag"] = self._identify_flag_patterns(price_data)
                elif pattern_type == "support_resistance":
                    identified_patterns["support_resistance"] = self._identify_support_resistance(price_data)
                elif pattern_type == "double_top":
                    identified_patterns["double_top"] = self._identify_double_top(price_data)
                elif pattern_type == "double_bottom":
                    identified_patterns["double_bottom"] = self._identify_double_bottom(price_data)
            
            # Calculate pattern strength and reliability
            pattern_analysis = self._analyze_pattern_strength(identified_patterns, price_data)
            
            return {
                "status": "success",
                "symbol": symbol,
                "patterns": identified_patterns,
                "pattern_analysis": pattern_analysis,
                "analysis_period": f"{lookback_days} days",
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error identifying patterns for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to identify patterns: {str(e)}"
            }
    
    async def analyze_trend_strength(self,
                                   symbol: str,
                                   timeframe: str = "daily",
                                   lookback_days: int = 50) -> Dict[str, Any]:
        """
        Analyze trend strength and direction
        
        Args:
            symbol: Symbol to analyze
            timeframe: Timeframe for analysis
            lookback_days: Number of days to analyze
            
        Returns:
            Dict containing trend analysis
        """
        try:
            logger.info(f"Analyzing trend strength for {symbol}")
            
            # Get historical price data
            price_data = await self._get_historical_price_data(symbol, lookback_days)
            if not price_data:
                return {
                    "status": "error",
                    "message": f"No price data available for {symbol}"
                }
            
            # Calculate trend metrics
            trend_direction = self._calculate_trend_direction(price_data)
            trend_strength = self._calculate_trend_strength(price_data)
            trend_momentum = self._calculate_trend_momentum(price_data)
            
            # Calculate moving averages for trend confirmation
            sma_20 = self._calculate_sma(price_data, 20)
            sma_50 = self._calculate_sma(price_data, 50)
            ema_12 = self._calculate_ema(price_data, 12)
            ema_26 = self._calculate_ema(price_data, 26)
            
            # Determine trend status
            trend_status = self._determine_trend_status(price_data, sma_20, sma_50)
            
            # Calculate price velocity and acceleration
            velocity = self._calculate_price_velocity(price_data)
            acceleration = self._calculate_price_acceleration(price_data)
            
            return {
                "status": "success",
                "symbol": symbol,
                "trend_analysis": {
                    "direction": trend_direction,
                    "strength": trend_strength,
                    "momentum": trend_momentum,
                    "status": trend_status,
                    "velocity": velocity,
                    "acceleration": acceleration
                },
                "moving_averages": {
                    "sma_20": sma_20[-1] if sma_20 else None,
                    "sma_50": sma_50[-1] if sma_50 else None,
                    "ema_12": ema_12[-1] if ema_12 else None,
                    "ema_26": ema_26[-1] if ema_26 else None
                },
                "current_price": price_data[-1]["close"] if price_data else None,
                "analysis_period": f"{lookback_days} days",
                "calculation_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing trend for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to analyze trend: {str(e)}"
            }
    
    async def generate_trading_signals(self,
                                     symbol: str,
                                     signal_types: Optional[List[str]] = None,
                                     sensitivity: str = "medium") -> Dict[str, Any]:
        """
        Generate trading signals based on technical analysis
        
        Args:
            symbol: Symbol to analyze
            signal_types: Types of signals to generate
            sensitivity: Signal sensitivity level
            
        Returns:
            Dict containing trading signals
        """
        try:
            logger.info(f"Generating trading signals for {symbol}")
            
            if signal_types is None:
                signal_types = ["momentum", "trend", "mean_reversion", "breakout"]
            
            # Get historical price data
            price_data = await self._get_historical_price_data(symbol, 100)
            if not price_data:
                return {
                    "status": "error",
                    "message": f"No price data available for {symbol}"
                }
            
            # Calculate indicators needed for signals
            rsi = self._calculate_rsi(price_data, 14)
            macd = self._calculate_macd(price_data)
            bollinger = self._calculate_bollinger_bands(price_data, 20)
            sma_20 = self._calculate_sma(price_data, 20)
            sma_50 = self._calculate_sma(price_data, 50)
            
            generated_signals = {}
            
            # Generate different types of signals
            if "momentum" in signal_types:
                generated_signals["momentum"] = self._generate_momentum_signals(price_data, rsi, macd)
            
            if "trend" in signal_types:
                generated_signals["trend"] = self._generate_trend_signals(price_data, sma_20, sma_50)
            
            if "mean_reversion" in signal_types:
                generated_signals["mean_reversion"] = self._generate_mean_reversion_signals(price_data, bollinger, rsi)
            
            if "breakout" in signal_types:
                generated_signals["breakout"] = self._generate_breakout_signals(price_data)
            
            # Combine and weight signals
            combined_signal = self._combine_signals(generated_signals, sensitivity)
            
            return {
                "status": "success",
                "symbol": symbol,
                "signals": generated_signals,
                "combined_signal": combined_signal,
                "current_price": price_data[-1]["close"] if price_data else None,
                "signal_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to generate signals: {str(e)}"
            }
    
    async def perform_market_scan(self,
                                symbols: List[str],
                                scan_criteria: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform technical analysis scan across multiple symbols
        
        Args:
            symbols: List of symbols to scan
            scan_criteria: Criteria for scanning
            
        Returns:
            Dict containing scan results
        """
        try:
            logger.info(f"Performing market scan on {len(symbols)} symbols")
            
            scan_results = []
            
            for symbol in symbols:
                try:
                    # Get basic technical data
                    price_data = await self._get_historical_price_data(symbol, 50)
                    if not price_data:
                        continue
                    
                    # Calculate key indicators
                    rsi = self._calculate_rsi(price_data, 14)
                    sma_20 = self._calculate_sma(price_data, 20)
                    volume_sma = self._calculate_volume_sma(price_data, 20)
                    
                    current_price = price_data[-1]["close"]
                    current_volume = price_data[-1]["volume"]
                    
                    # Check scan criteria
                    # Apply defaults of 0 or 0.0 for None values to match required parameter types
                    matches_criteria = self._check_scan_criteria(
                        symbol, current_price, rsi[-1] if rsi else 0.0,
                        sma_20[-1] if sma_20 else 0.0, current_volume,
                        volume_sma[-1] if volume_sma else 0.0, scan_criteria
                    )
                    
                    if matches_criteria:
                        scan_results.append({
                            "symbol": symbol,
                            "current_price": current_price,
                            "rsi": rsi[-1] if rsi else None,
                            "sma_20": sma_20[-1] if sma_20 else None,
                            "volume": current_volume,
                            "volume_avg": volume_sma[-1] if volume_sma else None,
                            "score": matches_criteria["score"]
                        })
                        
                except Exception as e:
                    logger.warning(f"Error scanning {symbol}: {str(e)}")
                    continue
            
            # Sort results by score
            scan_results.sort(key=lambda x: x["score"], reverse=True)
            
            return {
                "status": "success",
                "scan_results": scan_results,
                "total_scanned": len(symbols),
                "matches_found": len(scan_results),
                "scan_criteria": scan_criteria,
                "scan_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error performing market scan: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to perform scan: {str(e)}"
            }
    
    # ============================================================================
    # HELPER METHODS - Technical Indicators
    # ============================================================================
    
    async def _get_historical_price_data(self, symbol: str, days: int) -> List[Dict]:
        """Get historical price data for analysis"""
        try:
            if self.ibkr_service and hasattr(self.ibkr_service, 'get_historical_data'):
                # Use IBKR service if available
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)
                
                result = await self.ibkr_service.get_historical_data(
                    symbol=symbol,
                    start_date=start_date.strftime('%Y%m%d'),
                    end_date=end_date.strftime('%Y%m%d'),
                    bar_size='1 day'
                )
                
                if result.get('status') == 'success':
                    return result.get('data', [])
            
            # Fallback to mock data for testing
            return self._generate_mock_price_data(symbol, days)
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {str(e)}")
            return self._generate_mock_price_data(symbol, days)
    
    def _generate_mock_price_data(self, symbol: str, days: int) -> List[Dict]:
        """Generate mock price data for testing"""
        base_price = 100.0
        data = []
        
        for i in range(days):
            # Simple random walk
            change = np.random.normal(0, 0.02)  # 2% daily volatility
            if i == 0:
                price = base_price
            else:
                price = data[i-1]["close"] * (1 + change)
            
            high = price * (1 + abs(np.random.normal(0, 0.01)))
            low = price * (1 - abs(np.random.normal(0, 0.01)))
            volume = int(np.random.normal(1000000, 200000))
            
            data.append({
                "date": (datetime.now() - timedelta(days=days-i)).strftime('%Y-%m-%d'),
                "open": price,
                "high": max(price, high),
                "low": min(price, low),
                "close": price,
                "volume": max(volume, 100000)
            })
        
        return data
    
    def _calculate_sma(self, price_data: List[Dict], period: int) -> List[float]:
        """Calculate Simple Moving Average"""
        if len(price_data) < period:
            return []
        
        sma_values = []
        for i in range(period - 1, len(price_data)):
            sum_prices = sum(price_data[j]["close"] for j in range(i - period + 1, i + 1))
            sma_values.append(sum_prices / period)
        
        return sma_values
    
    def _calculate_ema(self, price_data: List[Dict], period: int) -> List[float]:
        """Calculate Exponential Moving Average"""
        if len(price_data) < period:
            return []
        
        multiplier = 2 / (period + 1)
        ema_values = []
        
        # Start with SMA for the first value
        sma = sum(price_data[i]["close"] for i in range(period)) / period
        ema_values.append(sma)
        
        # Calculate EMA for remaining values
        for i in range(period, len(price_data)):
            ema = (price_data[i]["close"] * multiplier) + (ema_values[-1] * (1 - multiplier))
            ema_values.append(ema)
        
        return ema_values
    
    def _calculate_rsi(self, price_data: List[Dict], period: int = 14) -> List[float]:
        """Calculate Relative Strength Index"""
        if len(price_data) < period + 1:
            return []
        
        gains = []
        losses = []
        
        # Calculate price changes
        for i in range(1, len(price_data)):
            change = price_data[i]["close"] - price_data[i-1]["close"]
            gains.append(max(change, 0))
            losses.append(max(-change, 0))
        
        rsi_values = []
        
        # Calculate initial average gain and loss
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        for i in range(period, len(gains)):
            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            
            rsi_values.append(rsi)
            
            # Update averages
            avg_gain = ((avg_gain * (period - 1)) + gains[i]) / period
            avg_loss = ((avg_loss * (period - 1)) + losses[i]) / period
        
        return rsi_values
    
    def _calculate_macd(self, price_data: List[Dict], fast: int = 12, slow: int = 26, signal: int = 9) -> Dict:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        if len(price_data) < slow:
            return {"macd": [], "signal": [], "histogram": []}
        
        ema_fast = self._calculate_ema(price_data, fast)
        ema_slow = self._calculate_ema(price_data, slow)
        
        # Align the EMAs (slow EMA will be shorter)
        start_idx = len(ema_fast) - len(ema_slow)
        ema_fast_aligned = ema_fast[start_idx:]
        
        # Calculate MACD line
        macd_line = [fast_val - slow_val for fast_val, slow_val in zip(ema_fast_aligned, ema_slow)]
        
        # Calculate signal line (EMA of MACD)
        if len(macd_line) >= signal:
            # Create mock data for signal calculation
            macd_data = [{"close": val} for val in macd_line]
            signal_line = self._calculate_ema(macd_data, signal)
            
            # Calculate histogram
            start_signal = len(macd_line) - len(signal_line)
            histogram = [macd_line[start_signal + i] - signal_line[i] for i in range(len(signal_line))]
            
            return {
                "macd": macd_line,
                "signal": signal_line,
                "histogram": histogram
            }
        
        return {"macd": macd_line, "signal": [], "histogram": []}
    
    def _calculate_bollinger_bands(self, price_data: List[Dict], period: int = 20, std_dev: float = 2) -> Dict:
        """Calculate Bollinger Bands"""
        if len(price_data) < period:
            return {"upper": [], "middle": [], "lower": []}
        
        sma = self._calculate_sma(price_data, period)
        
        upper_band = []
        lower_band = []
        
        for i in range(period - 1, len(price_data)):
            # Calculate standard deviation for the period
            prices = [price_data[j]["close"] for j in range(i - period + 1, i + 1)]
            std = np.std(prices)
            
            sma_idx = i - period + 1
            if sma_idx < len(sma):
                middle = sma[sma_idx]
                upper_band.append(middle + (std_dev * std))
                lower_band.append(middle - (std_dev * std))
        
        return {
            "upper": upper_band,
            "middle": sma,
            "lower": lower_band
        }
    
    def _calculate_stochastic(self, price_data: List[Dict], period: int = 14) -> Dict:
        """Calculate Stochastic Oscillator"""
        if len(price_data) < period:
            return {"k": [], "d": []}
        
        k_values = []
        
        for i in range(period - 1, len(price_data)):
            # Get high and low for the period
            period_highs = [price_data[j]["high"] for j in range(i - period + 1, i + 1)]
            period_lows = [price_data[j]["low"] for j in range(i - period + 1, i + 1)]
            
            highest_high = max(period_highs)
            lowest_low = min(period_lows)
            current_close = price_data[i]["close"]
            
            if highest_high == lowest_low:
                k_value = 50  # Avoid division by zero
            else:
                k_value = ((current_close - lowest_low) / (highest_high - lowest_low)) * 100
            
            k_values.append(k_value)
        
        # Calculate %D (SMA of %K)
        d_period = 3
        d_values = []
        if len(k_values) >= d_period:
            for i in range(d_period - 1, len(k_values)):
                d_value = sum(k_values[j] for j in range(i - d_period + 1, i + 1)) / d_period
                d_values.append(d_value)
        
        return {"k": k_values, "d": d_values}
    
    def _calculate_atr(self, price_data: List[Dict], period: int = 14) -> List[float]:
        """Calculate Average True Range"""
        if len(price_data) < period + 1:
            return []
        
        true_ranges = []
        
        for i in range(1, len(price_data)):
            high = price_data[i]["high"]
            low = price_data[i]["low"]
            prev_close = price_data[i-1]["close"]
            
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        # Calculate ATR as SMA of True Range
        atr_values = []
        for i in range(period - 1, len(true_ranges)):
            atr = sum(true_ranges[j] for j in range(i - period + 1, i + 1)) / period
            atr_values.append(atr)
        
        return atr_values
    
    def _calculate_adx(self, price_data: List[Dict], period: int = 14) -> List[float]:
        """Calculate Average Directional Index (simplified)"""
        if len(price_data) < period + 1:
            return []
        
        # Simplified ADX calculation
        directional_movements = []
        
        for i in range(1, len(price_data)):
            high_diff = price_data[i]["high"] - price_data[i-1]["high"]
            low_diff = price_data[i-1]["low"] - price_data[i]["low"]
            
            dm_plus = high_diff if high_diff > low_diff and high_diff > 0 else 0
            dm_minus = low_diff if low_diff > high_diff and low_diff > 0 else 0
            
            directional_movements.append(abs(dm_plus - dm_minus))
        
        # Calculate ADX as smoothed average
        adx_values = []
        for i in range(period - 1, len(directional_movements)):
            adx = sum(directional_movements[j] for j in range(i - period + 1, i + 1)) / period
            adx_values.append(min(adx * 100, 100))  # Normalize to 0-100
        
        return adx_values
    
    def _calculate_cci(self, price_data: List[Dict], period: int = 20) -> List[float]:
        """Calculate Commodity Channel Index"""
        if len(price_data) < period:
            return []
        
        cci_values = []
        
        for i in range(period - 1, len(price_data)):
            # Calculate typical prices for the period
            typical_prices = []
            for j in range(i - period + 1, i + 1):
                typical_price = (price_data[j]["high"] + price_data[j]["low"] + price_data[j]["close"]) / 3
                typical_prices.append(typical_price)
            
            # Calculate SMA of typical prices
            sma_tp = sum(typical_prices) / period
            
            # Calculate mean deviation
            mean_deviation = sum(abs(tp - sma_tp) for tp in typical_prices) / period
            
            # Calculate CCI
            current_tp = typical_prices[-1]
            if mean_deviation == 0:
                cci = 0
            else:
                cci = (current_tp - sma_tp) / (0.015 * mean_deviation)
            
            cci_values.append(cci)
        
        return cci_values
    
    def _calculate_williams_r(self, price_data: List[Dict], period: int = 14) -> List[float]:
        """Calculate Williams %R"""
        if len(price_data) < period:
            return []
        
        williams_r_values = []
        
        for i in range(period - 1, len(price_data)):
            # Get highest high and lowest low for the period
            period_highs = [price_data[j]["high"] for j in range(i - period + 1, i + 1)]
            period_lows = [price_data[j]["low"] for j in range(i - period + 1, i + 1)]
            
            highest_high = max(period_highs)
            lowest_low = min(period_lows)
            current_close = price_data[i]["close"]
            
            if highest_high == lowest_low:
                williams_r = -50  # Neutral value
            else:
                williams_r = ((highest_high - current_close) / (highest_high - lowest_low)) * -100
            
            williams_r_values.append(williams_r)
        
        return williams_r_values
    
    def _calculate_volume_sma(self, price_data: List[Dict], period: int) -> List[float]:
        """Calculate Simple Moving Average of volume"""
        if len(price_data) < period:
            return []
        
        volume_sma = []
        for i in range(period - 1, len(price_data)):
            avg_volume = sum(price_data[j]["volume"] for j in range(i - period + 1, i + 1)) / period
            volume_sma.append(avg_volume)
        
        return volume_sma
    
    # ============================================================================
    # HELPER METHODS - Pattern Recognition
    # ============================================================================
    
    def _identify_head_and_shoulders(self, price_data: List[Dict]) -> Dict:
        """Identify head and shoulders pattern (simplified)"""
        if len(price_data) < 20:
            return {"found": False, "confidence": 0}
        
        # Look for three peaks pattern
        highs = [data["high"] for data in price_data[-20:]]
        
        # Find local maxima
        peaks = []
        for i in range(1, len(highs) - 1):
            if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
                peaks.append((i, highs[i]))
        
        if len(peaks) >= 3:
            # Check if middle peak is highest (head)
            peaks.sort(key=lambda x: x[1], reverse=True)
            head = peaks[0]
            
            # Check for shoulders
            left_shoulder = None
            right_shoulder = None
            
            for peak in peaks[1:]:
                if peak[0] < head[0]:
                    left_shoulder = peak
                elif peak[0] > head[0]:
                    right_shoulder = peak
                    break
            
            if left_shoulder and right_shoulder:
                # Calculate confidence based on symmetry and height
                height_ratio = min(left_shoulder[1], right_shoulder[1]) / head[1]
                confidence = min(height_ratio * 100, 85)  # Max 85% confidence
                
                return {
                    "found": True,
                    "confidence": confidence,
                    "pattern_type": "head_and_shoulders",
                    "description": "Bearish reversal pattern identified"
                }
        
        return {"found": False, "confidence": 0}
    
    def _identify_triangle_patterns(self, price_data: List[Dict]) -> Dict:
        """Identify triangle patterns (simplified)"""
        if len(price_data) < 15:
            return {"found": False, "confidence": 0}
        
        recent_data = price_data[-15:]
        highs = [data["high"] for data in recent_data]
        lows = [data["low"] for data in recent_data]
        
        # Check for converging trend lines
        high_slope = self._calculate_trend_slope(highs)
        low_slope = self._calculate_trend_slope(lows)
        
        # Ascending triangle: horizontal resistance, rising support
        if abs(high_slope) < 0.001 and low_slope > 0.001:
            return {
                "found": True,
                "confidence": 70,
                "pattern_type": "ascending_triangle",
                "description": "Bullish continuation pattern"
            }
        
        # Descending triangle: falling resistance, horizontal support
        if high_slope < -0.001 and abs(low_slope) < 0.001:
            return {
                "found": True,
                "confidence": 70,
                "pattern_type": "descending_triangle",
                "description": "Bearish continuation pattern"
            }
        
        # Symmetrical triangle: converging trend lines
        if high_slope < -0.001 and low_slope > 0.001:
            return {
                "found": True,
                "confidence": 65,
                "pattern_type": "symmetrical_triangle",
                "description": "Neutral continuation pattern"
            }
        
        return {"found": False, "confidence": 0}
    
    def _identify_flag_patterns(self, price_data: List[Dict]) -> Dict:
        """Identify flag patterns (simplified)"""
        if len(price_data) < 10:
            return {"found": False, "confidence": 0}
        
        # Look for sharp move followed by consolidation
        recent_closes = [data["close"] for data in price_data[-10:]]
        
        # Check for strong initial move
        initial_move = (recent_closes[3] - recent_closes[0]) / recent_closes[0]
        
        if abs(initial_move) > 0.05:  # 5% move
            # Check for consolidation after the move
            consolidation_range = max(recent_closes[4:]) - min(recent_closes[4:])
            consolidation_pct = consolidation_range / recent_closes[0]
            
            if consolidation_pct < 0.03:  # Tight consolidation
                pattern_type = "bull_flag" if initial_move > 0 else "bear_flag"
                return {
                    "found": True,
                    "confidence": 75,
                    "pattern_type": pattern_type,
                    "description": f"{'Bullish' if initial_move > 0 else 'Bearish'} continuation pattern"
                }
        
        return {"found": False, "confidence": 0}
    
    def _identify_support_resistance(self, price_data: List[Dict]) -> Dict:
        """Identify support and resistance levels"""
        if len(price_data) < 20:
            return {"support_levels": [], "resistance_levels": []}
        
        closes = [data["close"] for data in price_data]
        highs = [data["high"] for data in price_data]
        lows = [data["low"] for data in price_data]
        
        # Find potential support levels (areas where price bounced)
        support_levels = []
        resistance_levels = []
        
        for i in range(2, len(lows) - 2):
            # Support: local minimum
            if lows[i] <= lows[i-1] and lows[i] <= lows[i+1] and lows[i] <= lows[i-2] and lows[i] <= lows[i+2]:
                support_levels.append(lows[i])
            
            # Resistance: local maximum
            if highs[i] >= highs[i-1] and highs[i] >= highs[i+1] and highs[i] >= highs[i-2] and highs[i] >= highs[i+2]:
                resistance_levels.append(highs[i])
        
        # Remove duplicates and sort
        support_levels = sorted(list(set([round(level, 2) for level in support_levels])))
        resistance_levels = sorted(list(set([round(level, 2) for level in resistance_levels])), reverse=True)
        
        return {
            "support_levels": support_levels[-5:],  # Top 5 support levels
            "resistance_levels": resistance_levels[:5],  # Top 5 resistance levels
            "current_price": closes[-1]
        }
    
    def _identify_double_top(self, price_data: List[Dict]) -> Dict:
        """Identify double top pattern"""
        if len(price_data) < 15:
            return {"found": False, "confidence": 0}
        
        highs = [data["high"] for data in price_data[-15:]]
        
        # Find the two highest points
        peaks = []
        for i in range(1, len(highs) - 1):
            if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
                peaks.append((i, highs[i]))
        
        if len(peaks) >= 2:
            # Sort by height and take top 2
            peaks.sort(key=lambda x: x[1], reverse=True)
            peak1, peak2 = peaks[0], peaks[1]
            
            # Check if peaks are similar in height and separated
            height_diff = abs(peak1[1] - peak2[1]) / max(peak1[1], peak2[1])
            time_separation = abs(peak1[0] - peak2[0])
            
            if height_diff < 0.02 and time_separation > 3:  # 2% height tolerance, minimum separation
                return {
                    "found": True,
                    "confidence": 80,
                    "pattern_type": "double_top",
                    "description": "Bearish reversal pattern"
                }
        
        return {"found": False, "confidence": 0}
    
    def _identify_double_bottom(self, price_data: List[Dict]) -> Dict:
        """Identify double bottom pattern"""
        if len(price_data) < 15:
            return {"found": False, "confidence": 0}
        
        lows = [data["low"] for data in price_data[-15:]]
        
        # Find the two lowest points
        troughs = []
        for i in range(1, len(lows) - 1):
            if lows[i] < lows[i-1] and lows[i] < lows[i+1]:
                troughs.append((i, lows[i]))
        
        if len(troughs) >= 2:
            # Sort by depth and take bottom 2
            troughs.sort(key=lambda x: x[1])
            trough1, trough2 = troughs[0], troughs[1]
            
            # Check if troughs are similar in depth and separated
            depth_diff = abs(trough1[1] - trough2[1]) / min(trough1[1], trough2[1])
            time_separation = abs(trough1[0] - trough2[0])
            
            if depth_diff < 0.02 and time_separation > 3:  # 2% depth tolerance, minimum separation
                return {
                    "found": True,
                    "confidence": 80,
                    "pattern_type": "double_bottom",
                    "description": "Bullish reversal pattern"
                }
        
        return {"found": False, "confidence": 0}
    
    def _analyze_pattern_strength(self, patterns: Dict, price_data: List[Dict]) -> Dict:
        """Analyze overall pattern strength"""
        total_patterns = sum(1 for pattern in patterns.values() if pattern.get("found", False))
        
        if total_patterns == 0:
            return {
                "overall_strength": "weak",
                "confidence": 0,
                "recommendation": "No clear patterns identified"
            }
        
        avg_confidence = sum(pattern.get("confidence", 0) for pattern in patterns.values() if pattern.get("found", False)) / total_patterns
        
        if avg_confidence > 75:
            strength = "strong"
        elif avg_confidence > 50:
            strength = "moderate"
        else:
            strength = "weak"
        
        return {
            "overall_strength": strength,
            "confidence": round(avg_confidence, 1),
            "patterns_found": total_patterns,
            "recommendation": f"{strength.capitalize()} pattern signals detected"
        }
    
    # ============================================================================
    # HELPER METHODS - Trend Analysis
    # ============================================================================
    
    def _calculate_trend_direction(self, price_data: List[Dict]) -> str:
        """Calculate overall trend direction"""
        if len(price_data) < 10:
            return "neutral"
        
        closes = [data["close"] for data in price_data[-10:]]
        
        # Calculate linear regression slope
        x = list(range(len(closes)))
        n = len(closes)
        
        sum_x = sum(x)
        sum_y = sum(closes)
        sum_xy = sum(x[i] * closes[i] for i in range(n))
        sum_x2 = sum(xi * xi for xi in x)
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        
        if slope > 0.1:
            return "uptrend"
        elif slope < -0.1:
            return "downtrend"
        else:
            return "sideways"
    
    def _calculate_trend_strength(self, price_data: List[Dict]) -> float:
        """Calculate trend strength (0-100)"""
        if len(price_data) < 10:
            return 0
        
        closes = [data["close"] for data in price_data[-10:]]
        
        # Calculate R-squared for trend line fit
        x = list(range(len(closes)))
        n = len(closes)
        
        # Calculate slope and intercept
        sum_x = sum(x)
        sum_y = sum(closes)
        sum_xy = sum(x[i] * closes[i] for i in range(n))
        sum_x2 = sum(xi * xi for xi in x)
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n
        
        # Calculate R-squared
        y_mean = sum_y / n
        ss_tot = sum((yi - y_mean) ** 2 for yi in closes)
        ss_res = sum((closes[i] - (slope * x[i] + intercept)) ** 2 for i in range(n))
        
        if ss_tot == 0:
            r_squared = 0
        else:
            r_squared = 1 - (ss_res / ss_tot)
        
        return max(0, min(100, r_squared * 100))
    
    def _calculate_trend_momentum(self, price_data: List[Dict]) -> float:
        """Calculate trend momentum"""
        if len(price_data) < 5:
            return 0
        
        recent_closes = [data["close"] for data in price_data[-5:]]
        
        # Calculate rate of change
        roc = (recent_closes[-1] - recent_closes[0]) / recent_closes[0] * 100
        
        return roc
    
    def _determine_trend_status(self, price_data: List[Dict], sma_20: List[float], sma_50: List[float]) -> str:
        """Determine overall trend status"""
        if not price_data or not sma_20 or not sma_50:
            return "unclear"
        
        current_price = price_data[-1]["close"]
        current_sma_20 = sma_20[-1]
        current_sma_50 = sma_50[-1] if len(sma_50) > 0 else current_sma_20
        
        if current_price > current_sma_20 > current_sma_50:
            return "strong_uptrend"
        elif current_price > current_sma_20:
            return "uptrend"
        elif current_price < current_sma_20 < current_sma_50:
            return "strong_downtrend"
        elif current_price < current_sma_20:
            return "downtrend"
        else:
            return "consolidation"
    
    def _calculate_price_velocity(self, price_data: List[Dict]) -> float:
        """Calculate price velocity (rate of price change)"""
        if len(price_data) < 3:
            return 0
        
        closes = [data["close"] for data in price_data[-3:]]
        velocity = (closes[-1] - closes[0]) / 2  # Average change per period
        
        return velocity
    
    def _calculate_price_acceleration(self, price_data: List[Dict]) -> float:
        """Calculate price acceleration (rate of velocity change)"""
        if len(price_data) < 4:
            return 0
        
        closes = [data["close"] for data in price_data[-4:]]
        
        # Calculate velocities
        velocity1 = closes[1] - closes[0]
        velocity2 = closes[2] - closes[1]
        velocity3 = closes[3] - closes[2]
        
        # Calculate acceleration
        acceleration = (velocity3 - velocity1) / 2
        
        return acceleration
    
    def _calculate_trend_slope(self, values: List[float]) -> float:
        """Calculate slope of trend line"""
        if len(values) < 2:
            return 0
        
        x = list(range(len(values)))
        n = len(values)
        
        sum_x = sum(x)
        sum_y = sum(values)
        sum_xy = sum(x[i] * values[i] for i in range(n))
        sum_x2 = sum(xi * xi for xi in x)
        
        if n * sum_x2 - sum_x * sum_x == 0:
            return 0
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        
        return slope
    
    # ============================================================================
    # HELPER METHODS - Signal Generation
    # ============================================================================
    
    def _generate_trading_signals(self, indicators: Dict, price_data: List[Dict]) -> Dict:
        """Generate trading signals from indicators"""
        signals = {
            "buy_signals": [],
            "sell_signals": [],
            "neutral_signals": []
        }
        
        current_price = price_data[-1]["close"] if price_data else 0
        
        # RSI signals
        if "rsi" in indicators and indicators["rsi"]:
            rsi_value = indicators["rsi"][-1]
            if rsi_value < 30:
                signals["buy_signals"].append("RSI oversold")
            elif rsi_value > 70:
                signals["sell_signals"].append("RSI overbought")
        
        # MACD signals
        if "macd" in indicators and indicators["macd"].get("histogram"):
            histogram = indicators["macd"]["histogram"]
            if len(histogram) >= 2:
                if histogram[-1] > 0 and histogram[-2] <= 0:
                    signals["buy_signals"].append("MACD bullish crossover")
                elif histogram[-1] < 0 and histogram[-2] >= 0:
                    signals["sell_signals"].append("MACD bearish crossover")
        
        # Bollinger Bands signals
        if "bollinger_bands" in indicators:
            bb = indicators["bollinger_bands"]
            if bb["upper"] and bb["lower"]:
                upper = bb["upper"][-1]
                lower = bb["lower"][-1]
                
                if current_price <= lower:
                    signals["buy_signals"].append("Price at lower Bollinger Band")
                elif current_price >= upper:
                    signals["sell_signals"].append("Price at upper Bollinger Band")
        
        return signals
    
    def _generate_momentum_signals(self, price_data: List[Dict], rsi: List[float], macd: Dict) -> Dict:
        """Generate momentum-based signals"""
        signals = {"signal": "neutral", "strength": 0, "reasons": []}
        
        buy_signals = 0
        sell_signals = 0
        
        # RSI momentum
        if rsi:
            rsi_value = rsi[-1]
            if rsi_value < 30:
                buy_signals += 1
                signals["reasons"].append("RSI oversold")
            elif rsi_value > 70:
                sell_signals += 1
                signals["reasons"].append("RSI overbought")
        
        # MACD momentum
        if macd.get("histogram") and len(macd["histogram"]) >= 2:
            if macd["histogram"][-1] > macd["histogram"][-2]:
                buy_signals += 1
                signals["reasons"].append("MACD histogram increasing")
            else:
                sell_signals += 1
                signals["reasons"].append("MACD histogram decreasing")
        
        # Determine overall signal
        if buy_signals > sell_signals:
            signals["signal"] = "buy"
            signals["strength"] = min(buy_signals * 25, 100)
        elif sell_signals > buy_signals:
            signals["signal"] = "sell"
            signals["strength"] = min(sell_signals * 25, 100)
        
        return signals
    
    def _generate_trend_signals(self, price_data: List[Dict], sma_20: List[float], sma_50: List[float]) -> Dict:
        """Generate trend-based signals"""
        signals = {"signal": "neutral", "strength": 0, "reasons": []}
        
        if not price_data or not sma_20:
            return signals
        
        current_price = price_data[-1]["close"]
        current_sma_20 = sma_20[-1]
        
        buy_signals = 0
        sell_signals = 0
        
        # Price vs SMA signals
        if current_price > current_sma_20:
            buy_signals += 1
            signals["reasons"].append("Price above SMA-20")
        else:
            sell_signals += 1
            signals["reasons"].append("Price below SMA-20")
        
        # SMA crossover signals
        if sma_50 and len(sma_20) >= 2 and len(sma_50) >= 2:
            if sma_20[-1] > sma_50[-1] and sma_20[-2] <= sma_50[-2]:
                buy_signals += 1
                signals["reasons"].append("Golden cross (SMA-20 above SMA-50)")
            elif sma_20[-1] < sma_50[-1] and sma_20[-2] >= sma_50[-2]:
                sell_signals += 1
                signals["reasons"].append("Death cross (SMA-20 below SMA-50)")
        
        # Determine overall signal
        if buy_signals > sell_signals:
            signals["signal"] = "buy"
            signals["strength"] = min(buy_signals * 35, 100)
        elif sell_signals > buy_signals:
            signals["signal"] = "sell"
            signals["strength"] = min(sell_signals * 35, 100)
        
        return signals
    
    def _generate_mean_reversion_signals(self, price_data: List[Dict], bollinger: Dict, rsi: List[float]) -> Dict:
        """Generate mean reversion signals"""
        signals = {"signal": "neutral", "strength": 0, "reasons": []}
        
        if not price_data:
            return signals
        
        current_price = price_data[-1]["close"]
        buy_signals = 0
        sell_signals = 0
        
        # Bollinger Bands mean reversion
        if bollinger.get("upper") and bollinger.get("lower"):
            upper = bollinger["upper"][-1]
            lower = bollinger["lower"][-1]
            middle = bollinger["middle"][-1]
            
            if current_price <= lower:
                buy_signals += 1
                signals["reasons"].append("Price at lower Bollinger Band")
            elif current_price >= upper:
                sell_signals += 1
                signals["reasons"].append("Price at upper Bollinger Band")
            elif abs(current_price - middle) / middle < 0.01:  # Near middle
                signals["reasons"].append("Price near Bollinger Band middle")
        
        # RSI mean reversion
        if rsi:
            rsi_value = rsi[-1]
            if rsi_value < 25:  # Extreme oversold
                buy_signals += 2
                signals["reasons"].append("RSI extremely oversold")
            elif rsi_value > 75:  # Extreme overbought
                sell_signals += 2
                signals["reasons"].append("RSI extremely overbought")
        
        # Determine overall signal
        if buy_signals > sell_signals:
            signals["signal"] = "buy"
            signals["strength"] = min(buy_signals * 30, 100)
        elif sell_signals > buy_signals:
            signals["signal"] = "sell"
            signals["strength"] = min(sell_signals * 30, 100)
        
        return signals
    
    def _generate_breakout_signals(self, price_data: List[Dict]) -> Dict:
        """Generate breakout signals"""
        signals = {"signal": "neutral", "strength": 0, "reasons": []}
        
        if len(price_data) < 20:
            return signals
        
        current_price = price_data[-1]["close"]
        current_volume = price_data[-1]["volume"]
        
        # Calculate recent high/low and average volume
        recent_data = price_data[-20:]
        recent_high = max(data["high"] for data in recent_data[:-1])  # Exclude current bar
        recent_low = min(data["low"] for data in recent_data[:-1])
        avg_volume = sum(data["volume"] for data in recent_data[:-5]) / 15  # Exclude recent 5 bars
        
        buy_signals = 0
        sell_signals = 0
        
        # Price breakout
        if current_price > recent_high:
            buy_signals += 1
            signals["reasons"].append("Price breakout above recent high")
        elif current_price < recent_low:
            sell_signals += 1
            signals["reasons"].append("Price breakdown below recent low")
        
        # Volume confirmation
        if current_volume > avg_volume * 1.5:  # 50% above average
            if buy_signals > 0:
                buy_signals += 1
                signals["reasons"].append("High volume confirms breakout")
            elif sell_signals > 0:
                sell_signals += 1
                signals["reasons"].append("High volume confirms breakdown")
        
        # Determine overall signal
        if buy_signals > sell_signals:
            signals["signal"] = "buy"
            signals["strength"] = min(buy_signals * 40, 100)
        elif sell_signals > buy_signals:
            signals["signal"] = "sell"
            signals["strength"] = min(sell_signals * 40, 100)
        
        return signals
    
    def _combine_signals(self, signals: Dict, sensitivity: str) -> Dict:
        """Combine multiple signals into overall recommendation"""
        signal_weights = {
            "momentum": 0.3,
            "trend": 0.3,
            "mean_reversion": 0.2,
            "breakout": 0.2
        }
        
        # Adjust weights based on sensitivity
        if sensitivity == "high":
            signal_weights["momentum"] = 0.4
            signal_weights["breakout"] = 0.3
        elif sensitivity == "low":
            signal_weights["trend"] = 0.5
            signal_weights["momentum"] = 0.2
        
        total_buy_score = 0
        total_sell_score = 0
        
        for signal_type, signal_data in signals.items():
            weight = signal_weights.get(signal_type, 0)
            
            if signal_data.get("signal") == "buy":
                total_buy_score += signal_data.get("strength", 0) * weight
            elif signal_data.get("signal") == "sell":
                total_sell_score += signal_data.get("strength", 0) * weight
        
        # Determine combined signal
        if total_buy_score > total_sell_score and total_buy_score > 30:
            combined_signal = "buy"
            strength = total_buy_score
        elif total_sell_score > total_buy_score and total_sell_score > 30:
            combined_signal = "sell"
            strength = total_sell_score
        else:
            combined_signal = "neutral"
            strength = 0
        
        return {
            "signal": combined_signal,
            "strength": round(strength, 1),
            "buy_score": round(total_buy_score, 1),
            "sell_score": round(total_sell_score, 1),
            "sensitivity": sensitivity
        }
    
    def _check_scan_criteria(self, symbol: str, price: float, rsi: float, sma_20: float, 
                           volume: int, volume_avg: float, criteria: Dict) -> Dict:
        """Check if symbol matches scan criteria"""
        score = 0
        matches = []
        
        # Price criteria
        if "min_price" in criteria and price >= criteria["min_price"]:
            score += 10
            matches.append("min_price")
        
        if "max_price" in criteria and price <= criteria["max_price"]:
            score += 10
            matches.append("max_price")
        
        # RSI criteria
        if rsi and "rsi_oversold" in criteria and rsi <= criteria["rsi_oversold"]:
            score += 20
            matches.append("rsi_oversold")
        
        if rsi and "rsi_overbought" in criteria and rsi >= criteria["rsi_overbought"]:
            score += 20
            matches.append("rsi_overbought")
        
        # Moving average criteria
        if sma_20 and "above_sma" in criteria and price > sma_20:
            score += 15
            matches.append("above_sma")
        
        if sma_20 and "below_sma" in criteria and price < sma_20:
            score += 15
            matches.append("below_sma")
        
        # Volume criteria
        if volume_avg and "high_volume" in criteria and volume > volume_avg * 1.5:
            score += 15
            matches.append("high_volume")
        
        # Return match result if score is sufficient
        min_score = criteria.get("min_score", 30)
        if score >= min_score:
            return {"score": score, "matches": matches}
        
        # Return empty dictionary instead of None to match the return type
        return {"score": 0, "matches": []}

