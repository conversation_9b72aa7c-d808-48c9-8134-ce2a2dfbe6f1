"""
Surface Calculation Implementation - Layer 3: Business Logic
Core volatility surface calculation and interpolation logic.
"""

from typing import Dict, Any, List, Optional, Tuple
import logging
import numpy as np
from datetime import datetime, timedelta
import asyncio

logger = logging.getLogger(__name__)

class SurfaceCalculationImpl:
    """Core implementation for volatility surface calculations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        
        if not self.ibkr_service:
            logger.warning("SurfaceCalculationImpl initialized without IBKR service")

    async def build_volatility_surface(
        self, 
        underlying_symbol: str,
        strikes: Optional[List[float]] = None,
        expiries: Optional[List[str]] = None,
        surface_type: str = "implied_volatility"
    ) -> Dict[str, Any]:
        """Build comprehensive volatility surface for analysis"""
        try:
            if not self.ibkr_service:
                return {
                    "success": False,
                    "error": "IBKR service not available",
                    "operation": "build_volatility_surface"
                }

            # Get underlying price first
            underlying_price = await self._get_underlying_price(underlying_symbol)
            if not underlying_price:
                return {
                    "success": False,
                    "error": f"Could not get price for {underlying_symbol}",
                    "operation": "build_volatility_surface"
                }

            # Get option chain if strikes/expiries not provided
            if not strikes or not expiries:
                option_chain = await self._get_option_chain(underlying_symbol)
                if not option_chain:
                    return {
                        "success": False,
                        "error": "Could not retrieve option chain",
                        "operation": "build_volatility_surface"
                    }
                
                if not strikes:
                    strikes = self._generate_strike_range(underlying_price, option_chain)
                if not expiries:
                    expiries = self._get_available_expiries(option_chain)

            # Build surface data
            surface_data = await self._calculate_surface_points(
                underlying_symbol, underlying_price, strikes, expiries
            )
            
            # Perform interpolation
            interpolated_surface = self._interpolate_surface(surface_data)
            
            # Calculate metrics
            surface_metrics = self._calculate_surface_metrics(surface_data, underlying_price)
            
            return {
                "success": True,
                "operation": "build_volatility_surface",
                "data": {
                    "underlying_symbol": underlying_symbol,
                    "underlying_price": underlying_price,
                    "surface_type": surface_type,
                    "raw_data": surface_data,
                    "interpolated_surface": interpolated_surface,
                    "metrics": surface_metrics,
                    "strikes": strikes,
                    "expiries": expiries
                },
                "implementation": "SurfaceCalculationImpl"
            }
            
        except Exception as e:
            logger.error(f"Error in build_volatility_surface: {e}")
            return {
                "success": False,
                "error": str(e),
                "operation": "build_volatility_surface"
            }

    async def _get_underlying_price(self, symbol: str) -> Optional[float]:
        """Get current underlying price"""
        try:
            # Use IBKR service to get market data
            if self.ibkr_service and hasattr(self.ibkr_service, 'get_market_data'):
                ticker = await self.ibkr_service.get_market_data(symbol)
                if ticker and hasattr(ticker, 'last') and ticker.last:
                    return float(ticker.last)
                elif ticker and hasattr(ticker, 'close') and ticker.close:
                    return float(ticker.close)
            return None
        except Exception as e:
            logger.error(f"Error getting underlying price: {e}")
            return None

    async def _get_option_chain(self, symbol: str) -> Optional[Dict]:
        """Get option chain for symbol"""
        try:
            # Implementation would use IBKR service to get option chains
            # For now, return mock data structure
            return {
                "symbol": symbol,
                "chains": [],
                "expiries": [],
                "strikes": []
            }
        except Exception as e:
            logger.error(f"Error getting option chain: {e}")
            return None

    def _generate_strike_range(self, underlying_price: float, option_chain: Dict) -> List[float]:
        """Generate appropriate strike range around current price"""
        # Generate strikes from 70% to 130% of underlying price
        lower_bound = underlying_price * 0.7
        upper_bound = underlying_price * 1.3
        
        # Generate strikes every 5% or $5, whichever is smaller
        strike_interval = min(underlying_price * 0.05, 5.0)
        
        strikes = []
        strike = lower_bound
        while strike <= upper_bound:
            strikes.append(round(strike, 2))
            strike += strike_interval
        
        return strikes

    def _get_available_expiries(self, option_chain: Dict) -> List[str]:
        """Get available expiry dates"""
        # Generate next 6 monthly expiries
        expiries = []
        current_date = datetime.now()
        
        for i in range(6):
            # Third Friday of each month
            month_start = current_date.replace(day=1)
            if i > 0:
                if month_start.month == 12:
                    month_start = month_start.replace(year=month_start.year + 1, month=1)
                else:
                    month_start = month_start.replace(month=month_start.month + 1)
            
            # Find third Friday
            third_friday = self._find_third_friday(month_start)
            expiries.append(third_friday.strftime("%Y%m%d"))
        
        return expiries

    def _find_third_friday(self, month_start: datetime) -> datetime:
        """Find third Friday of the month"""
        # Find first Friday
        day = 1
        while month_start.replace(day=day).weekday() != 4:  # Friday is 4
            day += 1
        
        # Third Friday is first Friday + 14 days
        third_friday = month_start.replace(day=day + 14)
        return third_friday

    async def _calculate_surface_points(
        self, 
        symbol: str, 
        underlying_price: float, 
        strikes: List[float], 
        expiries: List[str]
    ) -> Dict:
        """Calculate volatility at each surface point"""
        surface_points = {}
        
        for expiry in expiries:
            surface_points[expiry] = {}
            for strike in strikes:
                # Calculate implied volatility for this strike/expiry
                iv_data = await self._get_implied_volatility(symbol, strike, expiry, underlying_price)
                surface_points[expiry][strike] = iv_data
        
        return surface_points

    async def _get_implied_volatility(
        self, 
        symbol: str, 
        strike: float, 
        expiry: str, 
        underlying_price: float
    ) -> Dict:
        """Get implied volatility for specific option"""
        try:
            # This would use IBKR's calculateImpliedVolatility
            # For now, return mock calculation
            moneyness = strike / underlying_price
            time_to_expiry = self._calculate_time_to_expiry(expiry)
            
            # Simple mock volatility surface
            base_vol = 0.25
            moneyness_adjustment = abs(moneyness - 1.0) * 0.1
            time_adjustment = max(0, (0.25 - time_to_expiry) * 0.05)
            
            implied_vol = base_vol + moneyness_adjustment + time_adjustment
            
            return {
                "strike": strike,
                "expiry": expiry,
                "implied_volatility": implied_vol,
                "moneyness": moneyness,
                "time_to_expiry": time_to_expiry,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error calculating implied volatility: {e}")
            return {
                "strike": strike,
                "expiry": expiry,
                "implied_volatility": None,
                "error": str(e),
                "success": False
            }

    def _calculate_time_to_expiry(self, expiry_str: str) -> float:
        """Calculate time to expiry in years"""
        try:
            expiry_date = datetime.strptime(expiry_str, "%Y%m%d")
            current_date = datetime.now()
            time_diff = expiry_date - current_date
            return time_diff.days / 365.25
        except Exception as e:
            logger.error(f"Error calculating time to expiry: {e}")
            return 0.0

    def _interpolate_surface(self, surface_data: Dict) -> Dict:
        """Interpolate volatility surface for smooth visualization"""
        try:
            # This would use scipy for interpolation
            # For now, return the raw data
            return {
                "interpolated": True,
                "method": "cubic_spline",
                "surface_data": surface_data,
                "note": "Full interpolation requires scipy"
            }
        except Exception as e:
            logger.error(f"Error interpolating surface: {e}")
            return {"error": str(e)}

    def _calculate_surface_metrics(self, surface_data: Dict, underlying_price: float) -> Dict:
        """Calculate key surface metrics"""
        try:
            metrics = {
                "atm_volatility": self._get_atm_volatility(surface_data, underlying_price),
                "volatility_smile_skew": self._calculate_skew(surface_data),
                "term_structure_slope": self._calculate_term_slope(surface_data),
                "surface_convexity": self._calculate_convexity(surface_data)
            }
            return metrics
        except Exception as e:
            logger.error(f"Error calculating surface metrics: {e}")
            return {"error": str(e)}

    def _get_atm_volatility(self, surface_data: Dict, underlying_price: float) -> Dict:
        """Get at-the-money volatility for each expiry"""
        atm_vols = {}
        
        for expiry, strikes_data in surface_data.items():
            closest_strike = min(strikes_data.keys(), key=lambda x: abs(x - underlying_price))
            strike_data = strikes_data[closest_strike]
            
            if strike_data.get("success") and strike_data.get("implied_volatility"):
                atm_vols[expiry] = {
                    "strike": closest_strike,
                    "volatility": strike_data["implied_volatility"],
                    "moneyness": closest_strike / underlying_price
                }
        
        return atm_vols

    def _calculate_skew(self, surface_data: Dict) -> Dict:
        """Calculate volatility skew for each expiry"""
        # Implementation for volatility skew calculation
        return {"skew_calculated": True, "method": "risk_reversal"}

    def _calculate_term_slope(self, surface_data: Dict) -> Dict:
        """Calculate term structure slope"""
        # Implementation for term structure analysis
        return {"term_slope_calculated": True, "method": "linear_regression"}

    def _calculate_convexity(self, surface_data: Dict) -> Dict:
        """Calculate surface convexity"""
        # Implementation for surface convexity
        return {"convexity_calculated": True, "method": "second_derivative"}
