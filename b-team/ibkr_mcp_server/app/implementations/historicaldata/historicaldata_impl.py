"""
HistoricalData Implementation - Layer 3: Business Logic
Core business logic for HistoricalData operations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class HistoricalDataImplementation:
    """Core implementation for HistoricalData operations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        
        if not self.ibkr_service:
            logger.warning(f"HistoricalDataImplementation initialized without IBKR service")
    
    def _map_timeframe_to_duration(self, timeframe: str) -> tuple:
        """Maps user-friendly timeframe to TWS API duration and bar size"""
        timeframe_mapping = {
            "1d": ("1 D", "1 min"),
            "5d": ("5 D", "5 mins"),
            "1m": ("1 M", "1 hour"),
            "3m": ("3 M", "1 hour"),
            "6m": ("6 M", "1 day"),
            "1y": ("1 Y", "1 day"),
            "5y": ("5 Y", "1 day")
        }
        
        return timeframe_mapping.get(timeframe, ("1 D", "1 min"))
    
    async def get_historical_data(self, symbol: str, timeframe: str = "1d",
                                 end_date: Optional[str] = None,
                                 what_to_show: str = "TRADES",
                                 use_rth: bool = True,
                                 duration: Optional[str] = None) -> Dict[str, Any]:
        """
        Get historical price data for a symbol
        
        Args:
            symbol: The ticker symbol
            timeframe: Timeframe (1d, 5d, 1m, 3m, 6m, 1y, 5y)
            end_date: End date in 'YYYYMMDD HH:MM:SS' format (optional, defaults to now)
            what_to_show: Data type ('TRADES', 'MIDPOINT', 'BID', 'ASK', etc.)
            use_rth: Use regular trading hours only
            
        Returns:
            Dict containing historical data bars and metadata
        """
        try:
            if not self.ibkr_service or not self.ibkr_service.connected:
                return {"status": "error", "message": "Not connected to TWS"}
            
            # Map timeframe to TWS API duration and bar size
            if duration is None:
                duration_str, bar_size = self._map_timeframe_to_duration(timeframe)
            else:
                # Use provided duration and map timeframe to bar size only
                duration_str = duration
                _, bar_size = self._map_timeframe_to_duration(timeframe)
            
            # Create contract
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Set end date time (empty string means "now")
            end_date_time = end_date if end_date else ''
            
            logger.info(f"Requesting historical data for {symbol} with timeframe {timeframe}")
            
            # Request historical data using ib_async
            bars = await self.ibkr_service.ib.reqHistoricalDataAsync(
                contract=contract,
                endDateTime=end_date_time,
                durationStr=duration_str,
                barSizeSetting=bar_size,
                whatToShow=what_to_show,
                useRTH=use_rth
            )
            
            # Format the data
            historical_data = []
            for bar in bars:
                historical_data.append({
                    'date': bar.date,
                    'open': float(bar.open),
                    'high': float(bar.high),
                    'low': float(bar.low),
                    'close': float(bar.close),
                    'volume': int(bar.volume) if bar.volume else 0,
                    'average': float(bar.average) if hasattr(bar, 'average') and bar.average else None,
                    'barCount': int(bar.barCount) if hasattr(bar, 'barCount') and bar.barCount else None
                })
            
            logger.info(f"Retrieved {len(historical_data)} historical data bars for {symbol}")
            
            return {
                "status": "success",
                "symbol": symbol,
                "timeframe": timeframe,
                "duration": duration_str,
                "bar_size": bar_size,
                "what_to_show": what_to_show,
                "use_rth": use_rth,
                "data": historical_data,
                "count": len(historical_data),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get historical data for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to get historical data: {str(e)}",
                "symbol": symbol,
                "timeframe": timeframe,
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_historical_ticks(self, symbol: str, start_date: str, end_date: str,
                                  number_of_ticks: int = 1000, what_to_show: str = "TRADES",
                                  use_rth: bool = True, ignore_size: bool = False) -> Dict[str, Any]:
        """
        Get historical tick data for a symbol
        
        Args:
            symbol: The ticker symbol
            start_date: Start date in 'YYYYMMDD-HH:MM:SS' format
            end_date: End date in 'YYYYMMDD-HH:MM:SS' format  
            number_of_ticks: Maximum number of ticks to retrieve
            what_to_show: Data type ('TRADES', 'MIDPOINT', 'BID_ASK')
            use_rth: Use regular trading hours only
            ignore_size: Whether to ignore size
            
        Returns:
            Dict containing tick data and metadata
        """
        try:
            if not self.ibkr_service or not self.ibkr_service.connected:
                return {"status": "error", "message": "Not connected to TWS"}
            
            # Create contract
            contract = await self.ibkr_service.create_contract(symbol)
            
            logger.info(f"Requesting historical ticks for {symbol} from {start_date} to {end_date}")
            
            # Request historical tick data
            ticks = await self.ibkr_service.ib.reqHistoricalTicksAsync(
                contract=contract,
                startDateTime=start_date,
                endDateTime=end_date,
                numberOfTicks=number_of_ticks,
                whatToShow=what_to_show,
                useRth=use_rth,
                ignoreSize=ignore_size
            )
            
            # Format tick data based on type
            formatted_ticks = []
            for tick in ticks:
                if what_to_show == "BID_ASK":
                    formatted_ticks.append({
                        'time': tick.time,
                        'bid_price': float(tick.priceBid),
                        'ask_price': float(tick.priceAsk),
                        'bid_size': int(tick.sizeBid) if tick.sizeBid else 0,
                        'ask_size': int(tick.sizeAsk) if tick.sizeAsk else 0
                    })
                elif what_to_show == "TRADES":
                    formatted_ticks.append({
                        'time': tick.time,
                        'price': float(tick.price),
                        'size': int(tick.size) if tick.size else 0,
                        'exchange': getattr(tick, 'exchange', ''),
                        'special_conditions': getattr(tick, 'specialConditions', '')
                    })
                else:  # MIDPOINT
                    formatted_ticks.append({
                        'time': tick.time,
                        'price': float(tick.price),
                        'size': int(tick.size) if tick.size else 0
                    })
            
            logger.info(f"Retrieved {len(formatted_ticks)} historical ticks for {symbol}")
            
            return {
                "status": "success",
                "symbol": symbol,
                "start_date": start_date,
                "end_date": end_date,
                "what_to_show": what_to_show,
                "use_rth": use_rth,
                "ticks": formatted_ticks,
                "count": len(formatted_ticks),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get historical ticks for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to get historical ticks: {str(e)}",
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_head_timestamp(self, symbol: str, what_to_show: str = "TRADES",
                               use_rth: bool = True) -> Dict[str, Any]:
        """
        Get the earliest available data timestamp for a contract
        
        Args:
            symbol: The ticker symbol
            what_to_show: Data type ('TRADES', 'MIDPOINT', 'BID', 'ASK', etc.)
            use_rth: Use regular trading hours only
            
        Returns:
            Dict containing head timestamp and metadata
        """
        try:
            if not self.ibkr_service or not self.ibkr_service.connected:
                return {"status": "error", "message": "Not connected to TWS"}
            
            # Create contract
            contract = await self.ibkr_service.create_contract(symbol)
            
            logger.info(f"Requesting head timestamp for {symbol}")
            
            # Request head timestamp
            head_timestamp = await self.ibkr_service.ib.reqHeadTimeStampAsync(
                contract=contract,
                whatToShow=what_to_show,
                useRTH=use_rth,
                formatDate=1  # Return as formatted date string
            )
            
            logger.info(f"Retrieved head timestamp for {symbol}: {head_timestamp}")
            
            return {
                "status": "success",
                "symbol": symbol,
                "what_to_show": what_to_show,
                "use_rth": use_rth,
                "head_timestamp": head_timestamp,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get head timestamp for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to get head timestamp: {str(e)}",
                "symbol": symbol,
                "timestamp": datetime.now().isoformat()
            }

