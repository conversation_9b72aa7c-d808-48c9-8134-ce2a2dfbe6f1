"""
Scanner Implementation - Layer 3: Business Logic
Core business logic for Scanner operations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, List, Callable
import logging
import asyncio
import time
from datetime import datetime, timezone, timedelta
from ibapi.contract import Contract
from ibapi.scanner import ScannerSubscription

logger = logging.getLogger(__name__)

class ScannerImplementation:
    """Core implementation for Scanner operations"""
    
    def __init__(self, ibkr_service=None, **kwargs):
        self.ibkr_service = ibkr_service
        self.services = kwargs
        self.active_scanners = {}  # Track active scanner subscriptions
        self.scanner_results_cache = {}  # Cache recent scanner results
        
        # Predefined scanner parameters for common scans
        self.predefined_scans = {
            "top_gainers": {
                "scan_code": "TOP_PERC_GAIN",
                "instrument": "STK",
                "location_code": "STK.US.MAJOR",
                "description": "Top percentage gainers in US major exchanges"
            },
            "top_losers": {
                "scan_code": "TOP_PERC_LOSE", 
                "instrument": "STK",
                "location_code": "STK.US.MAJOR",
                "description": "Top percentage losers in US major exchanges"
            },
            "most_active": {
                "scan_code": "MOST_ACTIVE",
                "instrument": "STK", 
                "location_code": "STK.US.MAJOR",
                "description": "Most active stocks by volume"
            },
            "hot_by_volume": {
                "scan_code": "HOT_BY_VOLUME",
                "instrument": "STK",
                "location_code": "STK.US.MAJOR", 
                "description": "Hot stocks by volume"
            },
            "gap_up": {
                "scan_code": "TOP_OPEN_PERC_GAIN",
                "instrument": "STK",
                "location_code": "STK.US.MAJOR",
                "description": "Stocks with largest opening gaps up"
            },
            "gap_down": {
                "scan_code": "TOP_OPEN_PERC_LOSE",
                "instrument": "STK",
                "location_code": "STK.US.MAJOR", 
                "description": "Stocks with largest opening gaps down"
            },
            "high_volume": {
                "scan_code": "HIGH_VOLUME_RATE",
                "instrument": "STK",
                "location_code": "STK.US.MAJOR",
                "description": "Stocks with high volume relative to average"
            },
            "new_highs": {
                "scan_code": "HIGH_VS_13W_HL",
                "instrument": "STK", 
                "location_code": "STK.US.MAJOR",
                "description": "Stocks at new 13-week highs"
            },
            "new_lows": {
                "scan_code": "LOW_VS_13W_HL", 
                "instrument": "STK",
                "location_code": "STK.US.MAJOR",
                "description": "Stocks at new 13-week lows"
            },
            "options_most_active": {
                "scan_code": "MOST_ACTIVE",
                "instrument": "OPT",
                "location_code": "OPT.US",
                "description": "Most active options"
            }
        }
        
        if not self.ibkr_service:
            logger.warning("ScannerImplementation initialized without IBKR service")
    
    async def run_market_scanner(
        self,
        scan_code: str,
        instrument: str = "STK", 
        location_code: str = "STK.US.MAJOR",
        number_of_rows: int = 20,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Run a market scanner with specified parameters.
        
        Args:
            scan_code: Scanner code (e.g., "TOP_PERC_GAIN", "MOST_ACTIVE")
            instrument: Instrument type ("STK", "OPT", "FUT", etc.)
            location_code: Location code (e.g., "STK.US.MAJOR", "STK.US.NASDAQ")
            number_of_rows: Number of results to return (max 50)
            filters: Optional filters like market cap, price range, etc.
            
        Returns:
            Dict containing scanner results and metadata
        """
        try:
            if not self.ibkr_service:
                return {
                    "error": "IBKR service not available",
                    "success": False
                }
            
            # Generate unique request ID
            req_id = int(time.time() * 1000) % 100000
            
            # Create scanner subscription
            scan_sub = ScannerSubscription()
            scan_sub.scanCode = scan_code
            scan_sub.instrument = instrument
            scan_sub.locationCode = location_code
            scan_sub.numberOfRows = min(number_of_rows, 50)  # IBKR limit
            
            # Apply filters if provided
            if filters:
                if "market_cap_above" in filters:
                    scan_sub.marketCapAbove = filters["market_cap_above"]
                if "market_cap_below" in filters:
                    scan_sub.marketCapBelow = filters["market_cap_below"] 
                if "price_above" in filters:
                    scan_sub.abovePrice = filters["price_above"]
                if "price_below" in filters:
                    scan_sub.belowPrice = filters["price_below"]
                if "volume_above" in filters:
                    scan_sub.aboveVolume = filters["volume_above"]
                if "average_option_volume_above" in filters:
                    scan_sub.averageOptionVolumeAbove = filters["average_option_volume_above"]
                if "stock_type_filter" in filters:
                    scan_sub.stockTypeFilter = filters["stock_type_filter"]
            
            if hasattr(self.ibkr_service, 'ib') and hasattr(self.ibkr_service.ib, 'reqScannerDataAsync'):
                try:
                    # Use ib_async's scanner method directly
                    scan_data_list = await self.ibkr_service.ib.reqScannerDataAsync(
                        subscription=scan_sub,
                        scannerSubscriptionOptions=[],
                        scannerSubscriptionFilterOptions=[]
                    )
                    
                    # Convert ScanData objects to dict format
                    scan_results = []
                    for scan_data in scan_data_list:
                        result_dict = {
                            "rank": scan_data.rank,
                            "contract": {
                                "symbol": scan_data.contractDetails.contract.symbol,
                                "secType": scan_data.contractDetails.contract.secType,
                                "exchange": scan_data.contractDetails.contract.exchange,
                                "currency": scan_data.contractDetails.contract.currency,
                                "conId": scan_data.contractDetails.contract.conId
                            },
                            "distance": scan_data.distance,
                            "benchmark": scan_data.benchmark,
                            "projection": scan_data.projection,
                            "legsStr": scan_data.legsStr
                        }
                        
                        # Add market data if available
                        if hasattr(scan_data.contractDetails, 'marketName'):
                            result_dict["market_name"] = scan_data.contractDetails.marketName
                        if hasattr(scan_data.contractDetails, 'longName'):
                            result_dict["long_name"] = scan_data.contractDetails.longName
                        
                        scan_results.append(result_dict)
                    
                    result = {"scan_results": scan_results}
                    
                    if result and "scan_results" in result:
                        scan_results = result["scan_results"]
                        
                        # Enhance results with analysis
                        enhanced_results = self._enhance_scanner_results(scan_results, scan_code)
                        
                        # Cache results
                        cache_key = f"{scan_code}_{instrument}_{location_code}"
                        self.scanner_results_cache[cache_key] = {
                            "results": enhanced_results,
                            "timestamp": datetime.now(timezone.utc),
                            "scan_parameters": {
                                "scan_code": scan_code,
                                "instrument": instrument,
                                "location_code": location_code,
                                "number_of_rows": number_of_rows,
                                "filters": filters
                            }
                        }
                        
                        return {
                            "success": True,
                            "scan_code": scan_code,
                            "instrument": instrument,
                            "location_code": location_code,
                            "number_of_rows": number_of_rows,
                            "filters": filters,
                            "result_count": len(enhanced_results),
                            "results": enhanced_results,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                    else:
                        return {
                            "error": "No scanner results received",
                            "success": False
                        }
                        
                except Exception as e:
                    logger.error(f"Failed to run scanner {scan_code}: {e}")
                    return {
                        "error": f"Failed to run scanner: {str(e)}",
                        "success": False
                    }
            else:
                return {
                    "error": "Scanner method not available in IBKR service",
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"Error running scanner {scan_code}: {e}")
            return {
                "error": f"Failed to run scanner: {str(e)}",
                "success": False
            }
    
    async def run_predefined_scan(
        self,
        scan_name: str,
        number_of_rows: int = 20,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Run a predefined scanner by name.
        
        Args:
            scan_name: Name of predefined scan (e.g., "top_gainers", "most_active")
            number_of_rows: Number of results to return
            filters: Optional filters
            
        Returns:
            Dict containing scanner results
        """
        try:
            if scan_name not in self.predefined_scans:
                available_scans = list(self.predefined_scans.keys())
                return {
                    "error": f"Unknown scan '{scan_name}'. Available scans: {available_scans}",
                    "success": False,
                    "available_scans": available_scans
                }
            
            scan_config = self.predefined_scans[scan_name]
            
            result = await self.run_market_scanner(
                scan_code=scan_config["scan_code"],
                instrument=scan_config["instrument"],
                location_code=scan_config["location_code"],
                number_of_rows=number_of_rows,
                filters=filters
            )
            
            if result.get("success"):
                result["scan_name"] = scan_name
                result["description"] = scan_config["description"]
            
            return result
            
        except Exception as e:
            logger.error(f"Error running predefined scan {scan_name}: {e}")
            return {
                "error": f"Failed to run predefined scan: {str(e)}",
                "success": False
            }
    
    async def subscribe_scanner(
        self,
        scan_code: str,
        instrument: str = "STK",
        location_code: str = "STK.US.MAJOR", 
        number_of_rows: int = 20,
        filters: Optional[Dict[str, Any]] = None,
        callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Subscribe to real-time scanner updates.
        
        Args:
            scan_code: Scanner code
            instrument: Instrument type
            location_code: Location code
            number_of_rows: Number of results
            filters: Optional filters
            callback: Callback function for updates
            
        Returns:
            Dict containing subscription details
        """
        try:
            if not self.ibkr_service:
                return {
                    "error": "IBKR service not available",
                    "success": False
                }
            
            # Generate unique subscription ID and request ID
            subscription_id = f"scanner_{scan_code}_{int(time.time() * 1000) % 100000}"
            req_id = int(time.time() * 1000) % 100000
            
            # Create scanner subscription
            scan_sub = ScannerSubscription()
            scan_sub.scanCode = scan_code
            scan_sub.instrument = instrument
            scan_sub.locationCode = location_code
            scan_sub.numberOfRows = min(number_of_rows, 50)
            
            # Apply filters
            if filters:
                if "market_cap_above" in filters:
                    scan_sub.marketCapAbove = filters["market_cap_above"]
                if "market_cap_below" in filters:
                    scan_sub.marketCapBelow = filters["market_cap_below"]
                if "price_above" in filters:
                    scan_sub.abovePrice = filters["price_above"]
                if "price_below" in filters:
                    scan_sub.belowPrice = filters["price_below"]
            
            # Store subscription info
            subscription_info = {
                "subscription_id": subscription_id,
                "req_id": req_id,
                "scan_code": scan_code,
                "instrument": instrument,
                "location_code": location_code,
                "number_of_rows": number_of_rows,
                "filters": filters,
                "callback": callback,
                "start_time": datetime.now(timezone.utc),
                "update_count": 0,
                "status": "active"
            }
            
            self.active_scanners[subscription_id] = subscription_info
            
            # Start scanner subscription
            if hasattr(self.ibkr_service, 'ib') and hasattr(self.ibkr_service.ib, 'reqScannerSubscription'):
                try:
                    # Use ib_async's scanner subscription method
                    # Note: This returns a subscription object that will emit updates
                    scanner_subscription = self.ibkr_service.ib.reqScannerSubscription(
                        subscription=scan_sub,
                        scannerSubscriptionOptions=[],
                        scannerSubscriptionFilterOptions=[]
                    )
                    
                    # Store the subscription object
                    subscription_info["scanner_subscription"] = scanner_subscription
                    
                    # Set up callback handler for updates
                    def on_scanner_update(scan_data_list):
                        # Update count
                        subscription_info["update_count"] += 1
                        
                        # Convert and cache results
                        results = []
                        for scan_data in scan_data_list:
                            result_dict = {
                                "rank": scan_data.rank,
                                "symbol": scan_data.contractDetails.contract.symbol,
                                "contract_id": scan_data.contractDetails.contract.conId,
                                "distance": scan_data.distance
                            }
                            results.append(result_dict)
                        
                        # Cache results
                        cache_key = f"{scan_code}_{instrument}_{location_code}"
                        self.scanner_results_cache[cache_key] = {
                            "results": results,
                            "timestamp": datetime.now(timezone.utc)
                        }
                        
                        # Call user callback if provided
                        if callback:
                            try:
                                callback(results)
                            except Exception as cb_error:
                                logger.error(f"Scanner callback error: {cb_error}")
                    
                    # Attach the callback
                    scanner_subscription.updateEvent += on_scanner_update
                    
                except Exception as e:
                    logger.error(f"Failed to start scanner subscription {scan_code}: {e}")
                    del self.active_scanners[subscription_id]
                    return {
                        "error": f"Failed to start scanner subscription: {str(e)}",
                        "success": False
                    }
            else:
                return {
                    "error": "Scanner subscription method not available in IBKR service",
                    "success": False
                }
            
            return {
                "success": True,
                "subscription_id": subscription_id,
                "scan_code": scan_code,
                "instrument": instrument,
                "location_code": location_code,
                "number_of_rows": number_of_rows,
                "filters": filters,
                "message": f"Scanner subscription started for {scan_code}"
            }
            
        except Exception as e:
            logger.error(f"Error subscribing to scanner {scan_code}: {e}")
            return {
                "error": f"Failed to subscribe to scanner: {str(e)}",
                "success": False
            }
    
    async def cancel_scanner_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """
        Cancel a scanner subscription.
        
        Args:
            subscription_id: ID of subscription to cancel
            
        Returns:
            Dict containing cancellation status
        """
        try:
            if subscription_id not in self.active_scanners:
                return {
                    "error": f"Scanner subscription {subscription_id} not found",
                    "success": False
                }
            
            subscription = self.active_scanners[subscription_id]
            req_id = subscription["req_id"]
            
            # Cancel with IBKR
            if self.ibkr_service and hasattr(self.ibkr_service.ib, 'cancelScannerSubscription'):
                try:
                    # Get the scanner subscription object
                    scanner_subscription = subscription.get("scanner_subscription")
                    if scanner_subscription:
                        # Cancel the scanner subscription
                        self.ibkr_service.ib.cancelScannerSubscription(scanner_subscription)
                except Exception as e:
                    logger.error(f"Failed to cancel scanner subscription {req_id}: {e}")
            
            # Update status
            subscription["status"] = "cancelled"
            subscription["end_time"] = datetime.now(timezone.utc)
            
            # Calculate stats
            duration = (subscription["end_time"] - subscription["start_time"]).total_seconds()
            update_count = subscription.get("update_count", 0)
            
            # Remove from active
            cancelled_info = self.active_scanners.pop(subscription_id)
            
            return {
                "success": True,
                "subscription_id": subscription_id,
                "scan_code": cancelled_info["scan_code"],
                "duration_seconds": duration,
                "total_updates_received": update_count,
                "message": f"Scanner subscription cancelled for {cancelled_info['scan_code']}"
            }
            
        except Exception as e:
            logger.error(f"Error cancelling scanner subscription {subscription_id}: {e}")
            return {
                "error": f"Failed to cancel scanner subscription: {str(e)}",
                "success": False
            }
    
    async def get_scanner_parameters(self) -> Dict[str, Any]:
        """
        Get available scanner parameters and options.
        
        Returns:
            Dict containing scanner parameters and predefined scans
        """
        try:
            # Common scanner codes
            scanner_codes = {
                "TOP_PERC_GAIN": "Top percentage gainers",
                "TOP_PERC_LOSE": "Top percentage losers", 
                "MOST_ACTIVE": "Most active by volume",
                "HOT_BY_VOLUME": "Hot by volume",
                "TOP_OPEN_PERC_GAIN": "Top opening percentage gainers",
                "TOP_OPEN_PERC_LOSE": "Top opening percentage losers",
                "HIGH_VOLUME_RATE": "High volume relative to average",
                "HIGH_VS_13W_HL": "Near 13-week highs",
                "LOW_VS_13W_HL": "Near 13-week lows",
                "TOP_TRADE_COUNT": "Top trade count",
                "TOP_TRADE_RATE": "Top trade rate",
                "HALTED": "Halted stocks"
            }
            
            # Location codes
            location_codes = {
                "STK.US.MAJOR": "US Major Exchanges",
                "STK.US.NASDAQ": "NASDAQ",
                "STK.US": "All US Exchanges", 
                "STK.NASDAQ.NMS": "NASDAQ NMS",
                "STK.NYSE": "NYSE",
                "STK.AMEX": "AMEX",
                "OPT.US": "US Options"
            }
            
            # Instrument types
            instruments = {
                "STK": "Stocks",
                "OPT": "Options",
                "FUT": "Futures",
                "CASH": "Forex",
                "IND": "Indices",
                "CFD": "CFDs"
            }
            
            return {
                "success": True,
                "scanner_codes": scanner_codes,
                "location_codes": location_codes,
                "instruments": instruments,
                "predefined_scans": self.predefined_scans,
                "filter_options": {
                    "market_cap_above": "Minimum market cap",
                    "market_cap_below": "Maximum market cap",
                    "price_above": "Minimum price (maps to abovePrice)",
                    "price_below": "Maximum price (maps to belowPrice)", 
                    "volume_above": "Minimum volume (maps to aboveVolume)",
                    "average_option_volume_above": "Minimum average option volume",
                    "stock_type_filter": "Stock type filter"
                },
                "max_rows": 50,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting scanner parameters: {e}")
            return {
                "error": f"Failed to get scanner parameters: {str(e)}",
                "success": False
            }
    
    async def get_active_scanners(self) -> Dict[str, Any]:
        """
        Get all active scanner subscriptions.
        
        Returns:
            Dict containing active scanner details
        """
        try:
            current_time = datetime.now(timezone.utc)
            scanner_list = []
            
            for sub_id, sub_info in self.active_scanners.items():
                duration = (current_time - sub_info["start_time"]).total_seconds()
                
                scanner_list.append({
                    "subscription_id": sub_id,
                    "scan_code": sub_info["scan_code"],
                    "instrument": sub_info["instrument"], 
                    "location_code": sub_info["location_code"],
                    "number_of_rows": sub_info["number_of_rows"],
                    "filters": sub_info["filters"],
                    "start_time": sub_info["start_time"].isoformat(),
                    "duration_seconds": duration,
                    "update_count": sub_info.get("update_count", 0),
                    "status": sub_info["status"]
                })
            
            return {
                "success": True,
                "active_scanners": scanner_list,
                "total_active": len(scanner_list),
                "timestamp": current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting active scanners: {e}")
            return {
                "error": f"Failed to get active scanners: {str(e)}",
                "success": False
            }
    
    def _enhance_scanner_results(self, scan_results: List[Dict], scan_code: str) -> List[Dict]:
        """
        Enhance scanner results with additional analysis.
        
        Args:
            scan_results: Raw scanner results
            scan_code: Scanner code used
            
        Returns:
            Enhanced results with additional metrics
        """
        try:
            enhanced = []
            
            for result in scan_results:
                enhanced_result = result.copy()
                
                # Add relative metrics based on scan type
                if scan_code in ["TOP_PERC_GAIN", "TOP_PERC_LOSE"]:
                    # Add momentum indicators for gainers/losers
                    enhanced_result["momentum_category"] = self._categorize_momentum(result)
                
                elif scan_code == "MOST_ACTIVE":
                    # Add volume analysis for most active
                    enhanced_result["volume_category"] = self._categorize_volume(result)
                
                elif scan_code in ["HIGH_VS_13W_HL", "LOW_VS_13W_HL"]:
                    # Add technical analysis for highs/lows
                    enhanced_result["technical_strength"] = self._assess_technical_strength(result)
                
                # Add general risk assessment
                enhanced_result["risk_level"] = self._assess_risk_level(result)
                
                enhanced.append(enhanced_result)
            
            return enhanced
            
        except Exception as e:
            logger.error(f"Error enhancing scanner results: {e}")
            return scan_results  # Return original if enhancement fails
    
    def _categorize_momentum(self, result: Dict) -> str:
        """Categorize momentum based on percentage change."""
        try:
            change_pct = result.get("change_percent", 0)
            if change_pct > 10:
                return "explosive"
            elif change_pct > 5:
                return "strong"
            elif change_pct > 2:
                return "moderate"
            elif change_pct > 0:
                return "mild"
            elif change_pct > -2:
                return "mild_negative"
            elif change_pct > -5:
                return "moderate_negative"
            elif change_pct > -10:
                return "strong_negative"
            else:
                return "severe_negative"
        except:
            return "unknown"
    
    def _categorize_volume(self, result: Dict) -> str:
        """Categorize volume activity."""
        try:
            volume = result.get("volume", 0)
            avg_volume = result.get("average_volume", 1)
            
            if avg_volume > 0:
                volume_ratio = volume / avg_volume
                if volume_ratio > 5:
                    return "extremely_high"
                elif volume_ratio > 3:
                    return "very_high"
                elif volume_ratio > 2:
                    return "high"
                elif volume_ratio > 1.5:
                    return "above_average"
                else:
                    return "normal"
            else:
                return "unknown"
        except:
            return "unknown"
    
    def _assess_technical_strength(self, result: Dict) -> str:
        """Assess technical strength for stocks near highs/lows."""
        try:
            # This is a simplified assessment - in practice would use more indicators
            price = result.get("last_price", 0)
            high_52w = result.get("high_52w", price)
            low_52w = result.get("low_52w", price)
            
            if high_52w > low_52w:
                price_position = (price - low_52w) / (high_52w - low_52w)
                if price_position > 0.9:
                    return "very_strong"
                elif price_position > 0.7:
                    return "strong"
                elif price_position > 0.3:
                    return "neutral"
                elif price_position > 0.1:
                    return "weak"
                else:
                    return "very_weak"
            else:
                return "neutral"
        except:
            return "unknown"
    
    def _assess_risk_level(self, result: Dict) -> str:
        """Assess general risk level of a stock."""
        try:
            # Simple risk assessment based on volatility and market cap
            market_cap = result.get("market_cap", 0)
            volatility = result.get("volatility", 0)
            
            risk_score = 0
            
            # Market cap factor
            if market_cap < 300_000_000:  # Small cap
                risk_score += 3
            elif market_cap < 2_000_000_000:  # Mid cap
                risk_score += 2
            else:  # Large cap
                risk_score += 1
            
            # Volatility factor
            if volatility > 5:
                risk_score += 3
            elif volatility > 3:
                risk_score += 2
            elif volatility > 1:
                risk_score += 1
            
            if risk_score <= 2:
                return "low"
            elif risk_score <= 4:
                return "moderate"
            else:
                return "high"
        except:
            return "unknown"

