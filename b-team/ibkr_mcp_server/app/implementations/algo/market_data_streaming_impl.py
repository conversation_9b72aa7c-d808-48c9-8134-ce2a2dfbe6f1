"""
Market Data Streaming Implementation

Layer 3 implementation for market data streaming operations.
Focused on real-time data subscription and depth analysis.
"""
import logging
from typing import Dict, List, Any, Optional

from services.algo import (
    MarketDataStreamingService,
    DataType
)

logger = logging.getLogger('market-data-streaming-impl')


class MarketDataStreamingImpl:
    """Market data streaming business logic implementation"""
    
    def __init__(self, market_data_service: MarketDataStreamingService):
        self.market_data_service = market_data_service
        self.logger = logger
    
    async def subscribe_streaming_data(
        self,
        symbol: str,
        data_types: List[str],
        generic_tick_list: str = ""
    ) -> Dict[str, Any]:
        """
        Subscribe to streaming market data
        
        Args:
            symbol: Symbol to subscribe to
            data_types: List of data types as strings
            generic_tick_list: IB generic tick list
            
        Returns:
            Subscription details
        """
        try:
            # Convert string data types to enum
            data_type_enums = [DataType(dt) for dt in data_types]
            
            result = await self.market_data_service.subscribe_market_data(
                symbol=symbol,
                data_types=data_type_enums,
                generic_tick_list=generic_tick_list
            )
            
            self.logger.info(f"Successfully subscribed to {symbol} market data")
            return result
            
        except ValueError as e:
            self.logger.error(f"Invalid data type in {data_types}: {str(e)}")
            return {"status": "error", "message": f"Invalid data type: {str(e)}"}
        except Exception as e:
            self.logger.error(f"Failed to subscribe to market data for {symbol}: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def unsubscribe_streaming_data(self, subscription_id: str) -> Dict[str, Any]:
        """
        Unsubscribe from streaming market data
        
        Args:
            subscription_id: Subscription ID to cancel
            
        Returns:
            Unsubscribe result
        """
        try:
            result = await self.market_data_service.unsubscribe_market_data(subscription_id)
            self.logger.info(f"Successfully unsubscribed from {subscription_id}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to unsubscribe from {subscription_id}: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def get_market_depth(self, symbol: str, num_rows: int = 5) -> Dict[str, Any]:
        """
        Get Level II market depth data
        
        Args:
            symbol: Symbol to get depth for
            num_rows: Number of depth levels
            
        Returns:
            Market depth data
        """
        try:
            if num_rows <= 0:
                return {"status": "error", "message": "Number of rows must be positive"}
            
            if num_rows > 20:  # Reasonable limit
                self.logger.warning(f"Limiting depth rows from {num_rows} to 20")
                num_rows = 20
            
            result = await self.market_data_service.get_market_depth(symbol, num_rows)
            self.logger.debug(f"Retrieved market depth for {symbol} with {num_rows} rows")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to get market depth for {symbol}: {str(e)}")
            return {"status": "error", "message": str(e)}
