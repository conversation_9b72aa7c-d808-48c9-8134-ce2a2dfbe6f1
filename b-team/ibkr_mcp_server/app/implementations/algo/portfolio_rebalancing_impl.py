"""
Portfolio Rebalancing Implementation

Layer 3 implementation for portfolio management and optimization.
Focused on rebalancing and portfolio optimization operations.
"""
import logging
from typing import Dict, List, Any, Optional

from services.algo import (
    PortfolioRebalancingService,
    RebalancingStrategy,
    RebalancingConfig,
    AssetAllocation
)

logger = logging.getLogger('portfolio-rebalancing-impl')


class PortfolioRebalancingImpl:
    """Portfolio rebalancing and optimization business logic implementation"""
    
    def __init__(self, portfolio_service: PortfolioRebalancingService):
        self.portfolio_service = portfolio_service
        self.logger = logger
    
    async def rebalance_portfolio(
        self,
        portfolio_name: str,
        target_weights: Dict[str, float],
        tolerance: float = 0.05
    ) -> Dict[str, Any]:
        """
        Rebalance portfolio to target weights
        
        Args:
            portfolio_name: Name of portfolio to rebalance
            target_weights: Target allocation weights by symbol
            tolerance: Rebalancing tolerance threshold
            
        Returns:
            Rebalancing execution result
        """
        try:
            # Validate weights sum to approximately 1.0
            total_weight = sum(target_weights.values())
            if abs(total_weight - 1.0) > 0.01:
                return {
                    "status": "error", 
                    "message": f"Target weights sum to {total_weight:.3f}, expected ~1.0"
                }
            
            # Validate tolerance
            if tolerance <= 0 or tolerance > 0.5:
                return {
                    "status": "error",
                    "message": "Tolerance must be between 0 and 0.5"
                }
            
            # Create target allocations from weights
            from decimal import Decimal
            target_allocations = [
                AssetAllocation(
                    symbol=symbol,
                    target_weight=Decimal(str(weight))
                )
                for symbol, weight in target_weights.items()
            ]
            
            # Create rebalancing config
            config = RebalancingConfig(
                strategy=RebalancingStrategy.THRESHOLD,
                target_allocations=target_allocations,
                account=portfolio_name,  # Using portfolio_name as account for now
                rebalance_threshold=Decimal(str(tolerance))
            )
            
            result = await self.portfolio_service.rebalance_portfolio(config)
            
            # Convert result to dict format
            return {
                "status": "success",
                "trades_executed": result.trades_executed,
                "pre_weights": {k: float(v) for k, v in result.pre_rebalance_weights.items()},
                "post_weights": {k: float(v) for k, v in result.post_rebalance_weights.items()},
                "total_value": float(result.total_value),
                "transaction_costs": float(result.transaction_costs)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to rebalance portfolio {portfolio_name}: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    async def optimize_portfolio(
        self,
        symbols: List[str],
        optimization_type: str = "mean_variance",
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Optimize portfolio allocation
        
        Args:
            symbols: List of symbols to include in portfolio
            optimization_type: Type of optimization (mean_variance, risk_parity, etc.)
            constraints: Optional optimization constraints
            
        Returns:
            Optimized portfolio weights and metrics
        """
        try:
            if not symbols:
                return {"status": "error", "message": "Symbol list cannot be empty"}
            
            # Validate optimization type
            valid_types = ["mean_variance", "risk_parity", "equal_weight", "minimum_variance", "maximum_sharpe"]
            if optimization_type not in valid_types:
                return {
                    "status": "error", 
                    "message": f"Invalid optimization type. Valid options: {valid_types}"
                }
            
            # Set default constraints if none provided
            if constraints is None:
                constraints = {
                    "max_weight": 0.4,  # No single asset > 40%
                    "min_weight": 0.01,  # Minimum 1% allocation
                    "risk_tolerance": "moderate"
                }
            
            result = await self.portfolio_service.optimize_portfolio(
                symbols=symbols,
                optimization_method=optimization_type,
                constraints=constraints
            )
            
            self.logger.info(f"Optimized portfolio with {len(symbols)} symbols using {optimization_type}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to optimize portfolio: {str(e)}")
            return {"status": "error", "message": str(e)}
