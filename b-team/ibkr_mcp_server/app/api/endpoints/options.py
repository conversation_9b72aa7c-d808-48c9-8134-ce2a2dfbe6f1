"""
Options Trading API Endpoints

Advanced options trading capabilities including Greeks calculations,
strategy analysis, position management, and educational resources.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime, date
from decimal import Decimal

from ...models.options_models import OptionStrategy, STRATEGY_TEMPLATES
from ...services.options.greeks_calculator import OptionsGreeksCalculator, VolatilityAnalysis
from ...services.options.strategy_builder import StrategyBuilderService
from ...services.ibkr_service import IBKRService

router = APIRouter(prefix="/options", tags=["options-trading"])

# Initialize services
greeks_calculator = OptionsGreeksCalculator()

# Request/Response Models

class GreeksCalculationRequest(BaseModel):
    symbol: str = Field(..., description="Option symbol")
    underlying_symbol: str = Field(..., description="Underlying symbol")
    option_type: str = Field(..., description="Option type (CALL/PUT)")
    strike: float = Field(..., description="Strike price")
    expiration: date = Field(..., description="Expiration date")
    underlying_price: float = Field(..., description="Current underlying price")
    risk_free_rate: float = Field(default=0.05, description="Risk-free rate")

class PositionAnalysisRequest(BaseModel):
    position_id: str = Field(..., description="Position ID to analyze")
    include_greeks: bool = Field(default=True, description="Include Greeks in analysis")
    include_scenario_analysis: bool = Field(default=False, description="Include scenario analysis")

# Dependency to get services
async def get_services():
    ibkr_service = IBKRService()
    strategy_builder = StrategyBuilderService(ibkr_service)
    return ibkr_service, strategy_builder

# API Endpoints

@router.get("/strategies/templates")
async def get_strategy_templates() -> Dict[str, Any]:
    """
    Get all available options strategy templates
    
    Returns predefined strategy templates with descriptions,
    market outlook, and risk profiles.
    """
    try:
        templates = []
        
        for strategy_type, template in STRATEGY_TEMPLATES.items():
            templates.append({
                "name": strategy_type.value,
                "description": template["description"],
                "market_outlook": template["market_outlook"],
                "max_profit": template["max_profit"],
                "max_loss": template["max_loss"],
                "strategy_type": strategy_type.value
            })
        
        return {
            "success": True,
            "templates": templates,
            "total_strategies": len(templates)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/greeks/calculate")
async def calculate_option_greeks(
    request: GreeksCalculationRequest
) -> Dict[str, Any]:
    """
    Calculate Greeks for an option contract
    
    Computes Delta, Gamma, Theta, Vega, and Rho using Black-Scholes model
    with real-time market data integration.
    """
    try:
        from ...models.options_models import OptionContract, OptionType
        
        # Create option contract
        option = OptionContract(
            symbol=request.symbol,
            underlying=request.underlying_symbol,
            option_type=OptionType(request.option_type),
            strike=Decimal(str(request.strike)),
            expiration=request.expiration
        )
        
        # Calculate Greeks
        greeks = greeks_calculator.calculate_greeks(
            symbol=request.symbol,
            option_type=request.option_type,
            strike=request.strike,
            expiration=request.expiration.strftime("%Y%m%d"),
            underlying_price=request.underlying_price,
            risk_free_rate=request.risk_free_rate
        )
        
        return {
            "success": True,
            "greeks": greeks.get("greeks", {}),
            "option_details": {
                "symbol": request.symbol,
                "underlying": request.underlying_symbol,
                "type": request.option_type,
                "strike": request.strike,
                "expiration": request.expiration.isoformat(),
                "underlying_price": request.underlying_price
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/positions")
async def get_active_options_positions(
    services = Depends(get_services)
) -> Dict[str, Any]:
    """
    Get all active options positions
    
    Returns a list of all currently active options positions
    with basic status information.
    """
    try:
        ibkr_service, strategy_builder = services
        
        positions = await strategy_builder.get_active_positions()
        
        return {
            "success": True,
            "positions": positions,
            "total_positions": len(positions)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/positions/analyze")
async def analyze_options_position(
    request: PositionAnalysisRequest,
    services = Depends(get_services)
) -> Dict[str, Any]:
    """
    Get detailed analysis for an options position
    
    Provides comprehensive analysis including P&L, risk metrics,
    current Greeks, and actionable recommendations.
    """
    try:
        ibkr_service, strategy_builder = services
        
        analysis = await strategy_builder.get_position_analysis(request.position_id)
        
        if analysis['status'] != 'success':
            raise HTTPException(status_code=404, detail=analysis['message'])
        
        return {
            "success": True,
            "analysis": analysis['analysis']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/positions/{position_id}/close")
async def close_options_position(
    position_id: str,
    close_method: str = "market",
    services = Depends(get_services)
) -> Dict[str, Any]:
    """
    Close an active options position
    
    Closes all legs of an options position using the specified method.
    """
    try:
        ibkr_service, strategy_builder = services
        
        result = await strategy_builder.close_position(
            position_id=position_id,
            close_method=close_method
        )
        
        return {
            "success": result['status'] == 'success',
            "closure_result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/portfolio/greeks")
async def calculate_portfolio_greeks(
    symbols_and_prices: Dict[str, float],
    services = Depends(get_services)
) -> Dict[str, Any]:
    """
    Calculate aggregate Greeks for entire options portfolio
    
    Computes net portfolio exposure across all Greeks
    and provides hedging recommendations.
    """
    try:
        ibkr_service, strategy_builder = services
        
        # Get all active positions
        positions = [pos for pos in strategy_builder.active_positions.values()]
        
        # Convert prices to Decimal
        underlying_prices = {
            symbol: Decimal(str(price)) 
            for symbol, price in symbols_and_prices.items()
        }
        
        # Calculate portfolio Greeks
        portfolio_greeks = greeks_calculator.calculate_portfolio_greeks(
            positions=positions,
            underlying_prices=underlying_prices
        )
        
        # Generate hedging recommendations
        hedging_recs = greeks_calculator.dynamic_hedging_recommendations(
            portfolio_greeks=portfolio_greeks,
            underlying_prices=underlying_prices
        )
        
        return {
            "success": True,
            "portfolio_greeks": portfolio_greeks,
            "hedging_recommendations": hedging_recs,
            "positions_analyzed": len(positions)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/volatility/analysis/{symbol}")
async def get_volatility_analysis(
    symbol: str,
    lookback_days: int = 30
) -> Dict[str, Any]:
    """
    Get comprehensive volatility analysis for an underlying
    
    Includes implied volatility rank, percentile, and realized volatility
    for options strategy selection.
    """
    try:
        from ...services.options.greeks_calculator import VolatilityAnalysis
        
        vol_analyzer = VolatilityAnalysis()
        
        # This would integrate with market data to get historical IV and prices
        # Simplified implementation
        analysis = {
            "symbol": symbol,
            "iv_rank": 50.0,  # Placeholder
            "iv_percentile": 50.0,  # Placeholder
            "realized_volatility": 0.25,  # Placeholder
            "lookback_days": lookback_days,
            "recommendation": "NEUTRAL"
        }
        
        return {
            "success": True,
            "volatility_analysis": analysis
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/strategies/optimize")
async def optimize_strategy_parameters(
    strategy_type: str,
    underlying_symbol: str,
    market_outlook: str,
    account: str,
    services = Depends(get_services)
) -> Dict[str, Any]:
    """
    Optimize strategy parameters based on market conditions
    
    Uses market data and volatility analysis to suggest optimal
    parameters for the specified strategy type.
    """
    try:
        # This would implement sophisticated optimization
        # Based on current market conditions, IV rank, etc.
        
        optimization_result = {
            "strategy_type": strategy_type,
            "underlying_symbol": underlying_symbol,
            "optimized_parameters": {
                "days_to_expiration": 30,
                "delta_target": 0.25,
                "wing_width": 10
            },
            "confidence_score": 0.85,
            "market_conditions": {
                "iv_environment": "ELEVATED",
                "trend": "NEUTRAL",
                "volatility": "MEDIUM"
            },
            "reasoning": [
                "High IV environment favors premium selling strategies",
                "Neutral trend suggests range-bound strategies",
                "30 DTE provides good theta decay balance"
            ]
        }
        
        return {
            "success": True,
            "optimization": optimization_result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/education/strategy/{strategy_type}")
async def get_strategy_education(strategy_type: str) -> Dict[str, Any]:
    """
    Get educational information about an options strategy
    
    Provides detailed explanation, examples, and best practices
    for the specified strategy type.
    """
    try:
        from ...models.options_models import STRATEGY_TEMPLATES
        
        try:
            strategy_enum = OptionStrategy(strategy_type)
            template = STRATEGY_TEMPLATES.get(strategy_enum, {})
        except ValueError:
            raise HTTPException(status_code=404, detail=f"Strategy type {strategy_type} not found")
        
        education_content = {
            "strategy_type": strategy_type,
            "description": template.get("description", ""),
            "market_outlook": template.get("market_outlook", ""),
            "max_profit": template.get("max_profit", ""),
            "max_loss": template.get("max_loss", ""),
            "breakeven": template.get("breakeven", ""),
            "legs": template.get("legs", []),
            "optimal_conditions": {
                "iv_environment": template.get("optimal_iv", ""),
                "time_to_expiration": "30-45 days typical",
                "market_conditions": template.get("market_outlook", "")
            },
            "risk_considerations": [
                "Time decay effects",
                "Volatility risk",
                "Assignment risk (for short options)",
                "Liquidity considerations"
            ],
            "examples": {
                "entry_criteria": "High IV rank (>50th percentile)",
                "management": "Close at 25-50% max profit",
                "adjustment": "Roll strikes if tested early"
            }
        }
        
        return {
            "success": True,
            "education": education_content
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
