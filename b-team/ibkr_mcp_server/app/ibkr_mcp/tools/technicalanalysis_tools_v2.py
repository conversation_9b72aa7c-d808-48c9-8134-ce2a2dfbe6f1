"""
TechnicalAnalysis Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import List, Dict, Any
from ibkr_mcp.domains.technicalanalysis_domain import TechnicalAnalysisDomain

class TechnicalAnalysisToolsV2:
    """MCP Tools interface for TechnicalAnalysis domain"""
    
    def __init__(self):
        self.domain = TechnicalAnalysisDomain()

    def get_available_tools(self) -> list:
        """Get list of available tools"""
        return [
            'calculate_technical_indicators',
            'identify_chart_patterns',
            'analyze_trend_strength',
            'generate_trading_signals',
            'perform_market_scan'
        ]

    async def calculate_technical_indicators(self, symbol: str, indicators: List[str], period: int = 20, lookback_days: int = 252) -> dict:
        """Calculate technical indicators for a symbol"""
        return await self.domain.call_method('calculate_technical_indicators', symbol=symbol, indicators=indicators, period=period, lookback_days=lookback_days)

    async def identify_chart_patterns(self, symbol: str, pattern_types: List[str] = [], lookback_days: int = 100) -> dict:
        """Identify chart patterns in price data"""
        return await self.domain.call_method('identify_chart_patterns', symbol=symbol, pattern_types=pattern_types, lookback_days=lookback_days)

    async def analyze_trend_strength(self, symbol: str, timeframe: str = "daily", lookback_days: int = 50) -> dict:
        """Analyze trend strength and direction"""
        return await self.domain.call_method('analyze_trend_strength', symbol=symbol, timeframe=timeframe, lookback_days=lookback_days)

    async def generate_trading_signals(self, symbol: str, signal_types: List[str] = [], sensitivity: str = "medium") -> dict:
        """Generate trading signals based on technical analysis"""
        return await self.domain.call_method('generate_trading_signals', symbol=symbol, signal_types=signal_types, sensitivity=sensitivity)

    async def perform_market_scan(self, symbols: List[str], scan_criteria: Dict[str, Any]) -> dict:
        """Perform technical analysis scan across multiple symbols"""
        return await self.domain.call_method('perform_market_scan', symbols=symbols, scan_criteria=scan_criteria)

