"""
VolatilitySurface Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import List, Dict, Any
from ibkr_mcp.domains.volatility_surface_domain import VolatilitySurfaceDomain

class VolatilitySurfaceToolsV2:
    """MCP Tools interface for VolatilitySurface domain"""
    
    def __init__(self):
        self.domain = VolatilitySurfaceDomain()

    def get_available_tools(self) -> list:
        """Get list of available tools"""
        return [
            'build_volatility_surface',
            'analyze_volatility_smile',
            'analyze_term_structure',
            'detect_volatility_arbitrage'
        ]

    async def build_volatility_surface(self, underlying_symbol: str, strikes: List[float] = [], expiries: List[str] = [], surface_type: str = "implied_volatility") -> dict:
        """Build comprehensive volatility surface for analysis"""
        return await self.domain.call_method('build_volatility_surface', underlying_symbol=underlying_symbol, strikes=strikes, expiries=expiries, surface_type=surface_type)

    async def analyze_volatility_smile(self, underlying_symbol: str, expiry: str, strikes: List[float] = []) -> dict:
        """Analyze volatility smile for specific expiry"""
        return await self.domain.call_method('analyze_volatility_smile', underlying_symbol=underlying_symbol, expiry=expiry, strikes=strikes)

    async def analyze_term_structure(self, underlying_symbol: str, strike: float, expiries: List[str] = []) -> dict:
        """Analyze volatility term structure for specific strike"""
        return await self.domain.call_method('analyze_term_structure', underlying_symbol=underlying_symbol, strike=strike, expiries=expiries)

    async def detect_volatility_arbitrage(self, underlying_symbol: str, min_profit_threshold: float = 0.01, include_calendar_spreads: bool = True) -> dict:
        """Detect volatility arbitrage opportunities"""
        return await self.domain.call_method('detect_volatility_arbitrage', underlying_symbol=underlying_symbol, min_profit_threshold=min_profit_threshold, include_calendar_spreads=include_calendar_spreads)
