"""
Scanner Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional
from ibkr_mcp.domains.scanner_domain import ScannerDomain

class ScannerToolsV2:
    """MCP Tools interface for Scanner domain"""
    
    def __init__(self):
        self.domain = ScannerDomain()
    
    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "run_market_scanner",
            "get_scanner_parameters",
            "run_fundamental_scanner",
            "run_technical_scanner",
            "run_custom_scanner"
        ]
    
    async def run_market_scanner(
        self,
        scan_code: str = "TOP_PERC_GAIN",
        instrument: str = "STK",
        location_code: str = "STK.US.MAJOR",
        number_of_rows: int = 25,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run market scanner to find securities based on specified criteria"""
        return await self.domain.run_market_scanner(scan_code=scan_code, instrument=instrument, location_code=location_code, number_of_rows=number_of_rows, filters=filters)

    async def get_scanner_parameters(self) -> Dict[str, Any]:
        """Get available scanner parameters including scan codes, instruments, and locations"""
        return await self.domain.get_scanner_parameters()

    async def run_fundamental_scanner(
        self,
        scan_code: str = "HIGH_GROWTH_RATE",
        filters: Optional[Dict[str, Any]] = None,
        number_of_rows: int = 25
    ) -> Dict[str, Any]:
        """Run fundamental analysis scanner to find securities based on financial metrics"""
        return await self.domain.run_fundamental_scanner(scan_code=scan_code, filters=filters, number_of_rows=number_of_rows)

    async def run_technical_scanner(
        self,
        scan_code: str = "BREAKING_OUT",
        timeframe: str = "1day",
        filters: Optional[Dict[str, Any]] = None,
        number_of_rows: int = 25
    ) -> Dict[str, Any]:
        """Run technical analysis scanner to find securities based on chart patterns and indicators"""
        return await self.domain.run_technical_scanner(scan_code=scan_code, timeframe=timeframe, filters=filters, number_of_rows=number_of_rows)

    async def run_custom_scanner(
        self,
        criteria: Dict[str, Any],
        number_of_rows: int = 25
    ) -> Dict[str, Any]:
        """Run custom scanner with user-defined criteria combining multiple filters"""
        return await self.domain.run_custom_scanner(criteria=criteria, number_of_rows=number_of_rows)

    async def get_scanner_subscriptions(self) -> Dict[str, Any]:
        """Get all active scanner subscriptions and their status"""
        return await self.domain.get_scanner_subscriptions()
    
    async def analyze_scanner_results(self, scan_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze scanner results and provide insights"""
        return await self.domain.analyze_scanner_results(scan_results=scan_results)
    
    async def save_scanner_preset(self, name: str, criteria: Dict[str, Any]) -> Dict[str, Any]:
        """Save scanner criteria as a preset for future use"""
        return await self.domain.save_scanner_preset(name=name, criteria=criteria)
    
    async def get_scanner_presets(self) -> Dict[str, Any]:
        """Get all saved scanner presets"""
        return await self.domain.get_scanner_presets()

