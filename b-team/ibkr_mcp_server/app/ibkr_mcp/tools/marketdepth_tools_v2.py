"""
MarketDepth Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, Callable
from ibkr_mcp.domains.marketdepth_domain import MarketDepthDomain

class MarketDepthToolsV2:
    """MCP Tools interface for MarketDepth domain"""
    
    def __init__(self):
        self.domain = MarketDepthDomain()
    
    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "get_market_depth",
            "stream_market_depth",
            "stop_market_depth_stream",
            "analyze_order_book"
        ]
    
    async def get_market_depth(
        self,
        symbol: str,
        num_rows: int = 5,
        is_smart_depth: bool = False
    ) -> Dict[str, Any]:
        """Get Level II market depth data (order book)"""
        return await self.domain.execute('get_market_depth', symbol=symbol, num_rows=num_rows, is_smart_depth=is_smart_depth)
    
    async def stream_market_depth(
        self,
        symbol: str,
        num_rows: int = 5,
        is_smart_depth: bool = False,
        callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Stream real-time market depth updates"""
        return await self.domain.execute('stream_market_depth', symbol=symbol, num_rows=num_rows, is_smart_depth=is_smart_depth, callback=callback)
    
    async def cancel_market_depth(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel market depth streaming subscription"""
        return await self.domain.execute('cancel_market_depth', subscription_id=subscription_id)
    
    async def get_order_book_analysis(
        self,
        symbol: str,
        num_rows: int = 5
    ) -> Dict[str, Any]:
        """Get detailed order book analysis and metrics"""
        return await self.domain.execute('get_order_book_analysis', symbol=symbol, num_rows=num_rows)
    
    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """Get all active market depth subscriptions"""
        return await self.domain.execute('get_active_subscriptions')

    async def get_active_market_depth_subscriptions(self) -> Dict[str, Any]:
        """Get all active market depth subscriptions (alias for compatibility)"""
        return await self.get_active_subscriptions()

