"""
OptionsTrading Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, Tuple
from ibkr_mcp.domains.optionstrading_domain import OptionsTradingDomain

class OptionsTradingToolsV2:
    """MCP Tools interface for OptionsTrading domain"""
    
    def __init__(self):
        self.domain = OptionsTradingDomain()
    
    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "get_options_chain",
            "calculate_greeks",
            "analyze_option_strategy",
            "get_implied_volatility_surface",
            "create_options_strategy"
        ]
    
    async def get_options_chain(
        self,
        symbol: str,
        expiration: Optional[str] = None,
        strike_range: Optional[Tuple[float, float]] = None,
        option_type: str = "BOTH",
        exchange: str = "SMART"
    ) -> Dict[str, Any]:
        """Get options chain for a symbol"""
        return await self.domain.delegate_async('get_options_chain', symbol=symbol, expiration=expiration, strike_range=strike_range, option_type=option_type, exchange=exchange)
    
    async def calculate_greeks(
        self,
        symbol: str,
        option_type: str,
        strike: float,
        expiration: str,
        underlying_price: float,
        risk_free_rate: float = 0.02,
        dividend_yield: float = 0.0,
        volatility: Optional[float] = None
    ) -> Dict[str, Any]:
        """Calculate option Greeks using Black-Scholes model"""
        return await self.domain.delegate_async('calculate_greeks', symbol=symbol, option_type=option_type, strike=strike, expiration=expiration, underlying_price=underlying_price, risk_free_rate=risk_free_rate, dividend_yield=dividend_yield, volatility=volatility)
    
    async def analyze_option_strategy(
        self,
        strategy_name: str,
        legs: list,
        underlying_price: float,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        num_points: int = 100
    ) -> Dict[str, Any]:
        """Analyze complex options strategy with P&L analysis"""
        return await self.domain.delegate_async('analyze_option_strategy', strategy_name=strategy_name, legs=legs, underlying_price=underlying_price, min_price=min_price, max_price=max_price, num_points=num_points)
    
    async def get_implied_volatility_surface(
        self,
        symbol: str,
        expirations: Optional[list] = None,
        strikes: Optional[list] = None
    ) -> Dict[str, Any]:
        """Construct implied volatility surface"""
        return await self.domain.delegate_async('get_implied_volatility_surface', symbol=symbol, expirations=expirations, strikes=strikes)
    
    async def create_options_strategy(
        self,
        strategy_type: str,
        symbol: str,
        underlying_price: float,
        target_profit: Optional[float] = None,
        max_loss: Optional[float] = None,
        expiration_preference: str = "30-45",
        exchange: str = "SMART"
    ) -> Dict[str, Any]:
        """Create predefined options strategy"""
        return await self.domain.delegate_async('create_options_strategy', strategy_type=strategy_type, symbol=symbol, underlying_price=underlying_price, target_profit=target_profit, max_loss=max_loss, expiration_preference=expiration_preference, exchange=exchange)

