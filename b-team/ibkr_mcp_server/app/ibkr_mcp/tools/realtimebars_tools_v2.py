"""
RealtimeBars Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, Callable
from ibkr_mcp.domains.realtimebars_domain import RealtimeBarsDomain

class RealtimeBarsToolsV2:
    """MCP Tools interface for RealtimeBars domain"""
    
    def __init__(self):
        self.domain = RealtimeBarsDomain()
    
    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "request_real_time_bars",
            "cancel_real_time_bars",
            "get_historical_real_time_bars",
            "get_active_subscriptions",
            "get_bar_analysis"
        ]
    
    async def request_real_time_bars(
        self,
        symbol: str,
        bar_size: str = "5 secs",
        what_to_show: str = "TRADES",
        use_rth: bool = True,
        duration_hours: int = 1,
        callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Request real-time bar data streaming for a symbol"""
        return await self.domain.execute('request_real_time_bars', symbol=symbol, bar_size=bar_size, what_to_show=what_to_show, use_rth=use_rth, duration_hours=duration_hours, callback=callback)
    
    async def cancel_real_time_bars(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel real-time bars subscription"""
        return await self.domain.execute('cancel_real_time_bars', subscription_id=subscription_id)
    
    async def get_historical_real_time_bars(
        self,
        symbol: str,
        duration: str = "1 D",
        bar_size: str = "5 secs",
        what_to_show: str = "TRADES",
        use_rth: bool = True,
        format_date: int = 1
    ) -> Dict[str, Any]:
        """Get historical real-time bar data (5-second bars) for a symbol"""
        return await self.domain.execute('get_historical_real_time_bars', symbol=symbol, duration=duration, bar_size=bar_size, what_to_show=what_to_show, use_rth=use_rth, format_date=format_date)
    
    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """Get all active real-time bars subscriptions"""
        return await self.domain.execute('get_active_subscriptions')
    
    async def get_bar_analysis(
        self,
        symbol: str,
        lookback_minutes: int = 30
    ) -> Dict[str, Any]:
        """Get analysis of recent real-time bar data for a symbol"""
        return await self.domain.execute('get_bar_analysis', symbol=symbol, lookback_minutes=lookback_minutes)

    async def start_real_time_bars(
        self,
        symbol: str,
        bar_size: str = "5 secs",
        what_to_show: str = "TRADES",
        use_rth: bool = True,
        duration_hours: int = 1,
        callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Start real-time bar data streaming (alias for request_real_time_bars)"""
        return await self.request_real_time_bars(
            symbol=symbol,
            bar_size=bar_size,
            what_to_show=what_to_show,
            use_rth=use_rth,
            duration_hours=duration_hours,
            callback=callback
        )

    async def stop_real_time_bars(self, subscription_id: str) -> Dict[str, Any]:
        """Stop real-time bars streaming (alias for cancel_real_time_bars)"""
        return await self.cancel_real_time_bars(subscription_id)

    async def get_recent_bars(
        self,
        symbol: str,
        count: int = 100,
        bar_size: str = "5 secs"
    ) -> Dict[str, Any]:
        """Get recent real-time bars for a symbol"""
        return await self.get_historical_real_time_bars(
            symbol=symbol,
            duration=f"{count * 5} S",  # Approximate duration based on count
            bar_size=bar_size
        )

    async def analyze_real_time_bars(
        self,
        symbol: str,
        lookback_minutes: int = 30
    ) -> Dict[str, Any]:
        """Analyze real-time bar patterns and statistics"""
        return await self.get_bar_analysis(
            symbol=symbol,
            lookback_minutes=lookback_minutes
        )

    async def get_active_realtime_subscriptions(self) -> Dict[str, Any]:
        """Get list of active real-time bar subscriptions (alias for compatibility)"""
        return await self.get_active_subscriptions()

