"""
HistoricalData Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, List, Optional
from ibkr_mcp.domains.historicaldata_domain import HistoricalDataDomain

class HistoricalDataToolsV2:
    """MCP Tools interface for HistoricalData domain"""
    
    def __init__(self):
        self.domain = HistoricalDataDomain()

    def get_available_tools(self) -> list:
        """Get list of available tools"""
        return [
            'get_historical_data',
            'get_historical_data_multiple_timeframes',
            'get_historical_data_with_analysis',
            'export_historical_data',
            'get_historical_volatility'
        ]

    async def get_historical_data(
        self,
        symbol: str,
        timeframe: str,
        duration: str = "1 Y",
        end_date: Optional[str] = None,
        data_type: str = "TRADES",
        use_rth: bool = True
    ) -> Dict[str, Any]:
        """
        Get historical market data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            timeframe: Data timeframe ('1 min', '5 mins', '1 hour', '1 day', etc.)
            duration: How much data to retrieve ('1 D', '1 W', '1 M', '1 Y')
            end_date: End date in YYYYMMDD format (optional, defaults to now)
            data_type: Type of data ('TRADES', 'MIDPOINT', 'BID', 'ASK')
            use_rth: Whether to use regular trading hours only
            
        Returns:
            Dict containing historical data with timestamps, OHLCV values
        """
        return await self.domain.get_historical_data(
            symbol=symbol,
            timeframe=timeframe,
            end_date=end_date,
            what_to_show=data_type,
            use_rth=use_rth,
            duration=duration
        )

    async def get_historical_ticks(
        self,
        symbol: str,
        tick_type: str,
        number_of_ticks: int = 1000,
        start_date: Optional[str] = None,
        use_rth: bool = True
    ) -> Dict[str, Any]:
        """
        Get historical tick-by-tick data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            tick_type: Type of ticks ('TRADES', 'BID_ASK', 'MIDPOINT')
            number_of_ticks: Number of ticks to retrieve (max 1000)
            start_date: Start date in YYYYMMDD format (optional)
            use_rth: Whether to use regular trading hours only
            
        Returns:
            Dict containing tick data with timestamps and tick values
        """
        from datetime import datetime
        end_date = datetime.now().strftime('%Y%m%d %H:%M:%S')
        return await self.domain.get_historical_ticks(
            symbol=symbol,
            start_date=start_date or '',
            end_date=end_date,
            number_of_ticks=number_of_ticks,
            what_to_show=tick_type,
            use_rth=use_rth
        )

    async def get_head_timestamp(
        self,
        symbol: str,
        data_type: str = "TRADES",
        use_rth: bool = True
    ) -> Dict[str, Any]:
        """
        Get the earliest available data timestamp for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            data_type: Type of data ('TRADES', 'MIDPOINT', 'BID', 'ASK')
            use_rth: Whether to use regular trading hours only
            
        Returns:
            Dict containing the earliest available timestamp
        """
        return await self.domain.get_head_timestamp(
            symbol=symbol,
            what_to_show=data_type,
            use_rth=use_rth
        )

    async def get_historical_data_multiple_timeframes(
        self,
        symbol: str,
        timeframes: List[str],
        duration: str = "1 Y",
        what_to_show: str = "TRADES",
        use_rth: bool = True,
        exchange: str = "SMART"  # Note: exchange parameter kept for API compatibility
    ) -> Dict[str, Any]:
        """
        Get historical data for multiple timeframes simultaneously.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            timeframes: List of bar sizes (e.g., ['1 day', '1 hour', '5 mins'])
            duration: How much data to retrieve ('1 D', '1 W', '1 M', '1 Y')
            what_to_show: Type of data ('TRADES', 'MIDPOINT', 'BID', 'ASK')
            use_rth: Whether to use regular trading hours only
            exchange: Exchange to get data from
            
        Returns:
            Dict containing data for all requested timeframes
        """
        results = {}
        for timeframe in timeframes:
            try:
                data = await self.domain.get_historical_data(
                    symbol=symbol,
                    timeframe=timeframe,
                    end_date=None,
                    what_to_show=what_to_show,
                    use_rth=use_rth,
                    duration=duration
                )
                results[timeframe] = data
            except Exception as e:
                results[timeframe] = {
                    "status": "error",
                    "message": f"Failed to get data for {timeframe}: {str(e)}"
                }
        
        return {
            "status": "success",
            "symbol": symbol,
            "timeframes": results,
            "count": len(timeframes)
        }

    async def get_historical_data_with_analysis(
        self,
        symbol: str,
        duration: str = "1 Y",
        bar_size: str = "1 day",
        include_volume_analysis: bool = True,
        include_price_levels: bool = True,
        exchange: str = "SMART"  # Note: exchange parameter kept for API compatibility
    ) -> Dict[str, Any]:
        """
        Get historical data with built-in technical analysis.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            duration: How much data to retrieve
            bar_size: The bar size
            include_volume_analysis: Whether to include volume analysis
            include_price_levels: Whether to calculate support/resistance levels
            exchange: Exchange to get data from
            
        Returns:
            Dict containing historical data plus analysis
        """
        # Get base historical data
        base_data = await self.domain.get_historical_data(
            symbol=symbol,
            timeframe=bar_size,
            end_date=None,
            what_to_show="TRADES",
            use_rth=True,
            duration=duration
        )
        
        if base_data.get("status") != "success":
            return base_data
            
        # Add basic analysis
        analysis = {}
        
        if include_volume_analysis and "data" in base_data:
            volumes = [bar.get("volume", 0) for bar in base_data["data"]]
            if volumes:
                analysis["volume_analysis"] = {
                    "avg_volume": sum(volumes) / len(volumes),
                    "max_volume": max(volumes),
                    "min_volume": min(volumes)
                }
        
        if include_price_levels and "data" in base_data:
            prices = [bar.get("close", 0) for bar in base_data["data"]]
            if prices:
                analysis["price_levels"] = {
                    "current_price": prices[-1] if prices else None,
                    "highest": max(prices),
                    "lowest": min(prices),
                    "average": sum(prices) / len(prices)
                }
        
        base_data["analysis"] = analysis
        return base_data

    async def export_historical_data(
        self,
        symbol: str,
        duration: str = "1 Y",
        bar_size: str = "1 day",
        format: str = "csv",
        include_analysis: bool = False,  # Note: parameter kept for future implementation
        exchange: str = "SMART"  # Note: exchange parameter kept for API compatibility
    ) -> Dict[str, Any]:
        """
        Export historical data to file.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            duration: How much data to retrieve
            bar_size: The bar size
            format: Export format ('csv', 'json', 'excel')
            include_analysis: Whether to include technical analysis
            exchange: Exchange to get data from
            
        Returns:
            Dict containing export status and file path
        """
        # Get historical data
        data = await self.domain.get_historical_data(
            symbol=symbol,
            timeframe=bar_size,
            end_date=None,
            what_to_show="TRADES",
            use_rth=True,
            duration=duration
        )
        
        if data.get("status") != "success":
            return data
            
        # For now, return a placeholder response
        return {
            "status": "success",
            "message": f"Export functionality not yet implemented for {format} format",
            "symbol": symbol,
            "format": format,
            "data_points": len(data.get("data", [])) if "data" in data else 0
        }

    async def get_historical_volatility(
        self,
        symbol: str,
        duration: str = "3 M",
        bar_size: str = "1 day",
        calculation_method: str = "close_to_close",
        exchange: str = "SMART"  # Note: exchange parameter kept for API compatibility
    ) -> Dict[str, Any]:
        """
        Get historical volatility for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'AAPL', 'MSFT')
            duration: How much data to retrieve
            bar_size: The bar size
            calculation_method: Method for volatility calculation
            exchange: Exchange to get data from
            
        Returns:
            Dict containing volatility metrics
        """
        # Get historical data
        data = await self.domain.get_historical_data(
            symbol=symbol,
            timeframe=bar_size,
            end_date=None,
            what_to_show="TRADES",
            use_rth=True,
            duration=duration
        )
        
        if data.get("status") != "success":
            return data
            
        # Calculate basic volatility metrics
        if "data" in data:
            prices = [bar.get("close", 0) for bar in data["data"]]
            if len(prices) > 1:
                returns = []
                for i in range(1, len(prices)):
                    if prices[i-1] != 0:
                        ret = (prices[i] - prices[i-1]) / prices[i-1]
                        returns.append(ret)
                
                if returns:
                    import math
                    mean_return = sum(returns) / len(returns)
                    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
                    volatility = math.sqrt(variance * 252)  # Annualized
                    
                    return {
                        "status": "success",
                        "symbol": symbol,
                        "volatility": volatility,
                        "calculation_method": calculation_method,
                        "data_points": len(returns),
                        "period": duration
                    }
        
        return {
            "status": "error",
            "message": "Insufficient data for volatility calculation"
        }

