"""
RiskManagement Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from ibkr_mcp.domains.riskmanagement_domain import RiskManagementDomain

class RiskManagementToolsV2:
    """MCP Tools interface for RiskManagement domain"""
    
    def __init__(self):
        self.domain = RiskManagementDomain()

    async def calculate_portfolio_var(self, account: str, confidence_level: float = 0.95, time_horizon: int = 1, method: str = "historical") -> dict:
        """Calculate Value at Risk (VaR) for portfolio"""
        return await self.domain.call_method('calculate_portfolio_var', account=account, confidence_level=confidence_level, time_horizon=time_horizon, method=method)

    async def analyze_position_risk(self, symbol: str, position_size: float, position_type: str = "long") -> dict:
        """Analyze risk metrics for a specific position"""
        return await self.domain.call_method('analyze_position_risk', symbol=symbol, position_size=position_size, position_type=position_type)

    async def monitor_risk_limits(self, account: str, var_limit: float = 0.0, drawdown_limit: float = 0.0, concentration_limit: float = 0.0) -> dict:
        """Monitor portfolio against defined risk limits"""
        return await self.domain.call_method('monitor_risk_limits', account=account, var_limit=var_limit, drawdown_limit=drawdown_limit, concentration_limit=concentration_limit)

    async def calculate_correlation_matrix(self, symbols: list, lookback_days: int = 252, frequency: str = "daily") -> dict:
        """Calculate correlation matrix between securities"""
        return await self.domain.call_method('calculate_correlation_matrix', symbols=symbols, lookback_days=lookback_days, frequency=frequency)

    def get_available_tools(self) -> list:
        """Get list of available tools"""
        return [
            'calculate_portfolio_var',
            'analyze_position_risk', 
            'monitor_risk_limits',
            'calculate_correlation_matrix',
            'perform_stress_test'
        ]

    async def perform_stress_test(self, account: str, scenario_type: str = "all", custom_shocks: dict = {}) -> dict:
        """Perform stress testing on portfolio under various scenarios"""
        return await self.domain.call_method('perform_stress_test', account=account, scenario_type=scenario_type, custom_shocks=custom_shocks)

