"""
Contracts Tools - Layer 1: MCP Interface
Pure MCP interface with single-line delegations to domain manager.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional
from ibkr_mcp.domains.contracts_domain import ContractsDomain

class ContractsToolsV2:
    """MCP Tools interface for Contracts domain"""
    
    def __init__(self):
        self.domain = ContractsDomain()
    
    def get_available_tools(self) -> list:
        """Return list of available tools for this domain"""
        return [
            "search_contracts",
            "get_contract_details",
            "search_forex_pairs",
            "search_futures",
            "search_options",
            "search_stocks",
            "get_contract_specifications",
            "validate_contract"
        ]

    async def search_contracts(self, pattern: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Search for contracts matching pattern across all security types"""
        return await self.domain.search_contracts(pattern=pattern, sec_type=sec_type, exchange=exchange, currency=currency)

    async def get_contract_details(self, symbol: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Get detailed contract information including specifications"""
        return await self.domain.get_contract_details(symbol=symbol, sec_type=sec_type, exchange=exchange, currency=currency)

    async def search_forex_pairs(self, base_currency: str = "USD", quote_currency: str = "EUR") -> Dict[str, Any]:
        """Search for forex currency pairs and their specifications"""
        return await self.domain.search_forex_pairs(base_currency=base_currency, quote_currency=quote_currency)

    async def search_futures(self, symbol: str, exchange: str = "CME", currency: str = "USD", include_expired: bool = False) -> Dict[str, Any]:
        """Search for futures contracts with expiry information"""
        return await self.domain.search_futures(symbol=symbol, exchange=exchange, currency=currency, include_expired=include_expired)

    async def search_options(self, underlying_symbol: str, expiry: Optional[str] = None, strike: Optional[float] = None, right: str = "C") -> Dict[str, Any]:
        """Search for options contracts on underlying symbol"""
        return await self.domain.search_options(underlying_symbol=underlying_symbol, expiry=expiry, strike=strike, right=right)

    async def search_stocks(self, pattern: str, exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Search for stock contracts by symbol pattern"""
        return await self.domain.search_stocks(pattern=pattern, exchange=exchange, currency=currency)

    async def get_contract_specifications(self, symbol: str, sec_type: str = "STK", exchange: str = "SMART") -> Dict[str, Any]:
        """Get detailed contract specifications and trading rules"""
        return await self.domain.get_contract_specifications(symbol=symbol, sec_type=sec_type, exchange=exchange)

    async def validate_contract(self, symbol: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict[str, Any]:
        """Validate if a contract exists and is available for trading"""
        return await self.domain.validate_contract(symbol=symbol, sec_type=sec_type, exchange=exchange, currency=currency)
