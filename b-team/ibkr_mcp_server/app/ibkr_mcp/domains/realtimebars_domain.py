"""
RealtimeBars Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.realtimebars.realtimebars_impl import RealtimeBarsImplementation

class RealtimeBarsDomain(BaseDomainManager):
    """Domain manager for RealtimeBars operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': RealtimeBarsImplementation
        }
        
        # This will be injected by the registry
        services = {}  # Services injected during registration
        
        config = DomainConfig(
            name='RealtimeBars',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> list:
        """Return list of available tool names"""
        return [
            'request_real_time_bars',
            'cancel_real_time_bars', 
            'get_historical_real_time_bars',
            'get_active_subscriptions',
            'get_bar_analysis'
        ]
    
    # Implementation delegation methods
    
    async def execute(self, method_name: str, **kwargs) -> Dict[str, Any]:
        """Generic execution method for backward compatibility"""
        impl = self.get_implementation('main_impl')
        if hasattr(impl, method_name):
            method = getattr(impl, method_name)
            return await method(**kwargs)
        else:
            return {"success": False, "error": f"Method {method_name} not found"}
    
    async def request_real_time_bars(self, symbol: str, bar_size: str = "5 secs", 
                                   what_to_show: str = "TRADES", 
                                   use_rth: bool = True, callback=None) -> Dict[str, Any]:
        """Request real-time bars for a symbol"""
        impl = self.get_implementation('main_impl')
        return await impl.request_real_time_bars(symbol=symbol, bar_size=bar_size,
                                                what_to_show=what_to_show, use_rth=use_rth, 
                                                callback=callback)
    
    async def cancel_real_time_bars(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel real-time bars subscription"""
        impl = self.get_implementation('main_impl')
        return await impl.cancel_real_time_bars(subscription_id=subscription_id)
    
    async def get_historical_real_time_bars(self, symbol: str, duration: str = "1 D",
                                          bar_size: str = "5 secs") -> Dict[str, Any]:
        """Get historical real-time bars"""
        impl = self.get_implementation('main_impl')
        return await impl.get_historical_real_time_bars(symbol=symbol, duration=duration,
                                                       bar_size=bar_size)
    
    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """Get all active real-time bar subscriptions"""
        impl = self.get_implementation('main_impl')
        return await impl.get_active_subscriptions()
    
    async def get_bar_analysis(self, symbol: str, bars_data: list) -> Dict[str, Any]:
        """Analyze real-time bars for patterns"""
        impl = self.get_implementation('main_impl')
        return await impl.get_bar_analysis(symbol=symbol, bars_data=bars_data)
