"""
News Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.news.news_impl import NewsImplementation

class NewsDomain(BaseDomainManager):
    """Domain manager for News operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': NewsImplementation
        }
        
        # This will be injected by the registry
        services = {}  # Services injected during registration
        
        config = DomainConfig(
            name='News',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> list:
        """Return list of available tool names"""
        return [
            'request_news_bulletins',
            'cancel_news_bulletins',
            'request_historical_news',
            'request_news_article',
            'get_news_providers',
            'get_active_subscriptions'
        ]
    
    # Implementation delegation methods
    
    async def request_news_bulletins(self, all_msgs: bool = True, provider: Optional[str] = None, callback = None) -> Dict[str, Any]:
        """Request real-time news bulletins streaming"""
        return await self.get_implementation('main_impl').request_news_bulletins(
            all_msgs=all_msgs, provider=provider, callback=callback
        )
    
    async def cancel_news_bulletins(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel news bulletins subscription"""
        return await self.get_implementation('main_impl').cancel_news_bulletins(subscription_id=subscription_id)
    
    async def request_historical_news(self, conid: int, provider_codes: str = "BRFG+DJNL", 
                                    start_date_time: str = "", end_date_time: str = "", 
                                    total_results: int = 10) -> Dict[str, Any]:
        """Request historical news articles for a contract"""
        return await self.get_implementation('main_impl').request_historical_news(
            conid=conid, provider_codes=provider_codes, start_date_time=start_date_time,
            end_date_time=end_date_time, total_results=total_results
        )
    
    async def request_news_article(self, article_id: str, provider_code: str) -> Dict[str, Any]:
        """Request full content of a specific news article"""
        return await self.get_implementation('main_impl').request_news_article(
            article_id=article_id, provider_code=provider_code
        )
    
    async def get_news_providers(self) -> Dict[str, Any]:
        """Get list of available news providers and their details"""
        return await self.get_implementation('main_impl').get_news_providers()
    
    async def get_active_subscriptions(self) -> Dict[str, Any]:
        """Get all active news subscriptions"""
        return await self.get_implementation('main_impl').get_active_subscriptions()
    
    async def get_news_headlines(self, provider: Optional[str] = None, limit: int = 50) -> Dict[str, Any]:
        """Get news headlines from specified provider"""
        # This can be implemented as a wrapper around request_historical_news or other existing methods
        if provider:
            # Use the existing implementation to get recent news
            return await self.get_implementation('main_impl').request_historical_news(
                conid=0, provider_codes=provider, start_date_time="", end_date_time="", total_results=limit
            )
        else:
            return {"error": "Provider required for headlines", "success": False}
    
    async def analyze_news_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of news text"""
        # Use the existing sentiment analysis method
        impl = self.get_implementation('main_impl')
        sentiment = impl._analyze_sentiment(text)
        return {"success": True, "sentiment": sentiment, "text_analyzed": text}

