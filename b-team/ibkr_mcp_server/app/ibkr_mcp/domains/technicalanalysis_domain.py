"""
TechnicalAnalysis Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, List
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.technicalanalysis.technicalanalysis_impl import TechnicalAnalysisImplementation

class TechnicalAnalysisDomain(BaseDomainManager):
    """Domain manager for TechnicalAnalysis operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': TechnicalAnalysisImplementation
        }
        
        # This will be injected by the registry
        services = {}  # Services injected during registration
        
        config = DomainConfig(
            name='TechnicalAnalysis',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> list:
        """Return list of available tool names"""
        return [
            "calculate_technical_indicators",
            "identify_chart_patterns",
            "analyze_trend_strength",
            "generate_trading_signals",
            "perform_market_scan"
        ]
    
    # Implementation delegation methods
    
    async def call_method(self, method_name: str, **kwargs) -> Dict[str, Any]:
        """Generic method calling interface"""
        impl = self.get_implementation('main_impl')
        if hasattr(impl, method_name):
            method = getattr(impl, method_name)
            return await method(**kwargs)
        else:
            return {"success": False, "error": f"Method {method_name} not found"}
    
    async def calculate_technical_indicators(self, symbol: str, 
                                           indicators: Optional[List[str]] = None,
                                           period: str = "1M") -> Dict[str, Any]:
        """Calculate technical indicators for a symbol"""
        impl = self.get_implementation('main_impl')
        return await impl.calculate_technical_indicators(symbol=symbol, 
                                                       indicators=indicators or [],
                                                       period=period)
    
    async def identify_chart_patterns(self, symbol: str, 
                                    patterns: Optional[List[str]] = None,
                                    timeframe: str = "1D") -> Dict[str, Any]:
        """Identify chart patterns in price data"""
        impl = self.get_implementation('main_impl')
        return await impl.identify_chart_patterns(symbol=symbol,
                                                patterns=patterns or [],
                                                timeframe=timeframe)
    
    async def analyze_trend_strength(self, symbol: str, period: str = "1M") -> Dict[str, Any]:
        """Analyze trend strength and direction"""
        impl = self.get_implementation('main_impl')
        return await impl.analyze_trend_strength(symbol=symbol, period=period)
    
    async def generate_trading_signals(self, symbol: str, strategy: str = "momentum",
                                     parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate trading signals based on technical analysis"""
        impl = self.get_implementation('main_impl')
        return await impl.generate_trading_signals(symbol=symbol, strategy=strategy,
                                                 parameters=parameters or {})
    
    async def perform_market_scan(self, criteria: Dict[str, Any],
                                market: str = "US") -> Dict[str, Any]:
        """Perform market scan based on technical criteria"""
        impl = self.get_implementation('main_impl')
        return await impl.perform_market_scan(criteria=criteria, market=market)
