"""
Scanner Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional
import logging
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.scanner.scanner_impl import ScannerImplementation

logger = logging.getLogger(__name__)

class ScannerDomain(BaseDomainManager):
    """Domain manager for Scanner operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': ScannerImplementation
        }
        
        # CRITICAL FIX: Get services from registry or use emergency injection
        from ibkr_mcp.infrastructure.domain_registry import registry
        services = registry.services.copy() if hasattr(registry, 'services') and registry.services else {}
        
        # Emergency injection if no services
        if not services or 'ibkr_service' not in services:
            try:
                from services.ibkr_service import ibkr_service as global_service
                services['ibkr_service'] = global_service
                logger.warning(f"Scanner domain: Using emergency service injection")
            except Exception as e:
                logger.error(f"Scanner domain: Failed to inject service: {e}")
        
        config = DomainConfig(
            name='Scanner',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> list:
        """Return list of available tool names"""
        return [
            'run_market_scanner',
            'run_predefined_scan', 
            'subscribe_scanner',
            'cancel_scanner_subscription',
            'get_scanner_parameters',
            'get_active_scanners'
        ]
    
    # Implementation delegation methods
    
    async def run_market_scanner(self, scan_code: str = "TOP_PERC_GAIN", instrument: str = "STK", 
                               location_code: str = "STK.US.MAJOR", number_of_rows: int = 25,
                               filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Run custom market scanner with filters"""
        return await self.get_implementation('main_impl').run_market_scanner(
            scan_code=scan_code, instrument=instrument, location_code=location_code,
            number_of_rows=number_of_rows, filters=filters
        )
    
    async def get_scanner_parameters(self) -> Dict[str, Any]:
        """Get available scanner parameters"""
        return await self.get_implementation('main_impl').get_scanner_parameters()
    
    async def run_fundamental_scanner(self, scan_code: str = "HIGH_GROWTH_RATE", 
                                    filters: Optional[Dict[str, Any]] = None,
                                    number_of_rows: int = 25) -> Dict[str, Any]:
        """Run fundamental analysis scanner"""
        return await self.get_implementation('main_impl').run_fundamental_scanner(
            scan_code=scan_code, filters=filters, number_of_rows=number_of_rows
        )
    
    async def run_technical_scanner(self, scan_code: str = "BREAKING_OUT", timeframe: str = "1day",
                                  filters: Optional[Dict[str, Any]] = None,
                                  number_of_rows: int = 25) -> Dict[str, Any]:
        """Run technical analysis scanner"""
        return await self.get_implementation('main_impl').run_technical_scanner(
            scan_code=scan_code, timeframe=timeframe, filters=filters, number_of_rows=number_of_rows
        )
    
    async def run_custom_scanner(self, criteria: Dict[str, Any], number_of_rows: int = 25) -> Dict[str, Any]:
        """Run custom scanner with user-defined criteria"""
        return await self.get_implementation('main_impl').run_custom_scanner(
            criteria=criteria, number_of_rows=number_of_rows
        )
    
    async def get_scanner_subscriptions(self) -> Dict[str, Any]:
        """Get all active scanner subscriptions and their status"""
        return await self.get_implementation('main_impl').get_scanner_subscriptions()
    
    async def analyze_scanner_results(self, scan_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze scanner results and provide insights"""
        # Simple analysis implementation as placeholder
        try:
            results = scan_results.get('results', [])
            if not results:
                return {"success": True, "analysis": {"total_results": 0, "message": "No results to analyze"}}
            
            analysis = {
                "total_results": len(results),
                "sectors": {},
                "average_volume": 0,
                "price_range": {"min": float('inf'), "max": float('-inf')}
            }
            
            total_volume = 0
            for result in results:
                # Count sectors
                sector = result.get('sector', 'Unknown')
                analysis["sectors"][sector] = analysis["sectors"].get(sector, 0) + 1
                
                # Calculate volume stats
                volume = result.get('volume', 0)
                total_volume += volume
                
                # Price range
                price = result.get('price', 0)
                if price > 0:
                    analysis["price_range"]["min"] = min(analysis["price_range"]["min"], price)
                    analysis["price_range"]["max"] = max(analysis["price_range"]["max"], price)
            
            analysis["average_volume"] = total_volume / len(results) if results else 0
            
            return {"success": True, "analysis": analysis}
        except Exception as e:
            return {"success": False, "error": f"Analysis failed: {str(e)}"}
    
    async def save_scanner_preset(self, name: str, criteria: Dict[str, Any]) -> Dict[str, Any]:
        """Save scanner criteria as a preset for future use"""
        # Simple implementation - would normally persist to storage
        return {
            "success": True,
            "preset_name": name,
            "criteria": criteria,
            "message": f"Scanner preset '{name}' saved successfully"
        }
    
    async def get_scanner_presets(self) -> Dict[str, Any]:
        """Get all saved scanner presets"""
        # Placeholder implementation - would normally retrieve from storage
        default_presets = {
            "high_volume_stocks": {
                "scan_code": "MOST_ACTIVE",
                "instrument": "STK",
                "filters": {"volume": {"min": 1000000}}
            },
            "growth_stocks": {
                "scan_code": "HIGH_GROWTH_RATE",
                "instrument": "STK",
                "filters": {"pe_ratio": {"max": 30}}
            },
            "penny_stocks": {
                "scan_code": "LOW_PRICE",
                "instrument": "STK", 
                "filters": {"price": {"min": 0.1, "max": 5.0}}
            }
        }
        
        return {"success": True, "presets": default_presets, "total_presets": len(default_presets)}

