"""
TickByTick Domain Manager - Layer 2: Coordination
Coordinates between MCP interface and focused implementations.
Generated by ArchitectureGenerator.
"""

from typing import Dict, Any, Optional, List
from ibkr_mcp.infrastructure.base_domain import BaseDomainManager, DomainConfig
from implementations.tickbytick.tickbytick_impl import TickByTickImplementation

class TickByTickDomain(BaseDomainManager):
    """Domain manager for TickByTick operations"""
    
    def __init__(self):
        implementations = {
            'main_impl': TickByTickImplementation
        }
        
        # This will be injected by the registry
        services = {}  # Services injected during registration
        
        config = DomainConfig(
            name='TickByTick',
            implementations=implementations,
            services=services
        )
        
        super().__init__(config)
    
    def get_available_tools(self) -> list:
        """Return list of available tool names"""
        return [
            'get_real_time_ticks',
            'get_historical_ticks', 
            'get_tick_types'
        ]
    
    # Implementation delegation methods with enhanced signatures
    
    async def get_real_time_ticks(self, symbol: str, tick_type: str = "Last",
                                 number_of_ticks: int = 0,
                                 ignore_size: bool = False) -> Dict[str, Any]:
        """Get real-time tick-by-tick data for a symbol"""
        return await self.get_implementation('main_impl').get_real_time_ticks(
            symbol=symbol,
            tick_type=tick_type,
            number_of_ticks=number_of_ticks,
            ignore_size=ignore_size
        )
    
    async def get_historical_ticks(self, symbol: str, start_date: str, end_date: str,
                                  tick_type: str = "TRADES", number_of_ticks: int = 1000,
                                  use_rth: bool = True, ignore_size: bool = False) -> Dict[str, Any]:
        """Get historical tick data for a symbol"""
        return await self.get_implementation('main_impl').get_historical_ticks(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            tick_type=tick_type,
            number_of_ticks=number_of_ticks,
            use_rth=use_rth,
            ignore_size=ignore_size
        )
    
    async def get_tick_types(self) -> Dict[str, Any]:
        """Get available tick types and their descriptions"""
        return await self.get_implementation('main_impl').get_tick_types()

