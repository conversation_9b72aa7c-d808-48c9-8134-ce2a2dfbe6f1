"""
Base Domain Manager for Multi-Layer Architecture
Provides common infrastructure for all domain managers.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, Callable
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class DomainConfig:
    """Configuration for domain initialization"""
    name: str
    implementations: Dict[str, Type]
    services: Dict[str, Any]
    error_handlers: Optional[Dict[str, Callable]] = None

class BaseDomainManager(ABC):
    """Abstract base class for all domain managers"""
    
    def __init__(self, config: DomainConfig):
        self.config = config
        self.implementations = {}
        self.services = config.services
        self.is_initialized = False
        self._initialize_implementations()
    
    def _initialize_implementations(self):
        """Initialize all implementations with dependency injection"""
        try:
            # CRITICAL FIX: Emergency service injection if services are empty
            if not self.services or 'ibkr_service' not in self.services:
                logger.warning(f"{self.config.name}: No services provided, attempting emergency injection")
                try:
                    from services.ibkr_service import ibkr_service as global_ibkr_service
                    if not self.services:
                        self.services = {}
                    self.services['ibkr_service'] = global_ibkr_service
                    logger.info(f"{self.config.name}: Emergency injection successful")
                except Exception as e:
                    logger.error(f"{self.config.name}: Emergency injection failed: {e}")
            
            for name, impl_class in self.config.implementations.items():
                # Inject services into implementation
                service_dependencies = self._resolve_dependencies(impl_class)
                self.implementations[name] = impl_class(**service_dependencies)
            
            self.is_initialized = True
            logger.info(f"{self.config.name} domain initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize {self.config.name} domain: {e}")
            raise
    
    def _resolve_dependencies(self, impl_class: Type) -> Dict[str, Any]:
        """Resolve service dependencies for implementation class"""
        dependencies = {}
        
        # Standard service mappings
        if hasattr(impl_class, '__init__'):
            import inspect
            sig = inspect.signature(impl_class.__init__)
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue
                
                # Map parameter names to services
                service_key = self._map_param_to_service(param_name)
                if service_key and service_key in self.services:
                    dependencies[param_name] = self.services[service_key]
        
        return dependencies
    
    def _map_param_to_service(self, param_name: str) -> Optional[str]:
        """Map constructor parameter names to service keys"""
        service_mappings = {
            'ibkr_service': 'ibkr_service',
            'trading_service': 'algorithmic_trading_service',
            'order_service': 'order_management_service',
            'data_service': 'ibkr_service',
            'risk_service': 'ibkr_service'
        }
        return service_mappings.get(param_name)
    
    def check_initialization(self):
        """Verify domain is properly initialized"""
        if not self.is_initialized:
            raise RuntimeError(f"{self.config.name} domain not initialized")
    
    def get_implementation(self, name: str):
        """Get implementation by name with validation"""
        self.check_initialization()
        
        if name not in self.implementations:
            available = list(self.implementations.keys())
            raise ValueError(f"Implementation '{name}' not found. Available: {available}")
        
        return self.implementations[name]
    
    @abstractmethod
    def get_available_tools(self) -> List[str]:
        """Return list of available tool names for this domain"""
        pass
    
    def handle_error(self, error: Exception, context: str) -> Dict[str, Any]:
        """Common error handling across all domains"""
        error_response = {
            "success": False,
            "error": str(error),
            "domain": self.config.name,
            "context": context
        }
        
        logger.error(f"Domain error in {self.config.name}: {error}", exc_info=True)
        
        # Use custom error handler if available
        if self.config.error_handlers and context in self.config.error_handlers:
            return self.config.error_handlers[context](error, error_response)
        
        return error_response
