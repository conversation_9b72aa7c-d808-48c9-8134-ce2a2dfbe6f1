"""
Advanced Order Routing Service

This service provides sophisticated order routing capabilities including
smart order routing, dark pool access, and execution optimization.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from dataclasses import dataclass, field

from ..ibkr_service import IBKRService
from ..order_management_service import OrderManagementService
from ib_async import Contract, Order, TagValue

logger = logging.getLogger('order-routing-service')


class RoutingAlgorithm(Enum):
    """Supported routing algorithms"""
    SMART = "smart"  # IB's Smart routing
    DIRECTED = "directed"  # Direct to specific exchange
    DARK_POOL = "dark_pool"  # Dark pool seeking
    SWEEP = "sweep"  # Sweep multiple venues
    MIDPOINT = "midpoint"  # Midpoint matching
    PEGGED = "pegged"  # Pegged orders
    AUCTION = "auction"  # Auction matching
    CONDITIONAL = "conditional"  # Conditional routing
    LIQUIDITY_SEEKING = "liquidity_seeking"
    COST_AWARE = "cost_aware"  # Minimize total cost


@dataclass
class RoutingConfig:
    """Configuration for order routing"""
    algorithm: RoutingAlgorithm
    symbol: str
    action: str  # BUY or SELL
    quantity: Decimal
    account: str
    urgency: str = "Normal"  # Urgent, Normal, Patient
    dark_pool_opt_out: bool = False
    min_fill_size: Optional[int] = None
    max_display_size: Optional[int] = None
    allowed_exchanges: List[str] = field(default_factory=list)
    prohibited_exchanges: List[str] = field(default_factory=list)
    custom_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RoutingResult:
    """Result of order routing"""
    order_id: int
    routing_algorithm: RoutingAlgorithm
    exchanges_used: List[str]
    fills: List[Dict[str, Any]]
    avg_price: Decimal
    total_commission: Decimal
    execution_time: timedelta
    slippage: Decimal
    metrics: Dict[str, Any] = field(default_factory=dict)


class AdvancedOrderRoutingService:
    """
    Advanced order routing service
    
    Provides sophisticated order routing with multiple algorithms,
    venue optimization, and execution analysis.
    """
    
    def __init__(self, ibkr_service: IBKRService, oms: OrderManagementService):
        self.ibkr_service = ibkr_service
        self.oms = oms
        self._routing_handlers = {
            RoutingAlgorithm.SMART: self._route_smart,
            RoutingAlgorithm.DIRECTED: self._route_directed,
            RoutingAlgorithm.DARK_POOL: self._route_dark_pool,
            RoutingAlgorithm.SWEEP: self._route_sweep,
            RoutingAlgorithm.MIDPOINT: self._route_midpoint,
            RoutingAlgorithm.PEGGED: self._route_pegged,
            RoutingAlgorithm.AUCTION: self._route_auction,
            RoutingAlgorithm.CONDITIONAL: self._route_conditional,
            RoutingAlgorithm.LIQUIDITY_SEEKING: self._route_liquidity_seeking,
            RoutingAlgorithm.COST_AWARE: self._route_cost_aware
        }
        
    async def route_order(self, config: RoutingConfig) -> RoutingResult:
        """
        Route an order using specified algorithm
        
        Args:
            config: Routing configuration
            
        Returns:
            RoutingResult with execution details
        """
        try:
            # Get routing handler
            handler = self._routing_handlers.get(config.algorithm)
            if not handler:
                raise ValueError(f"Unsupported routing algorithm: {config.algorithm}")
            
            # Execute routing
            start_time = datetime.now()
            result = await handler(config)
            
            # Calculate execution metrics
            result.execution_time = datetime.now() - start_time
            result.metrics.update(await self._calculate_execution_metrics(result, config))
            
            return result
            
        except Exception as e:
            logger.error(f"Order routing error: {str(e)}")
            raise
    
    async def analyze_market_microstructure(
        self,
        symbol: str,
        size: int
    ) -> Dict[str, Any]:
        """
        Analyze market microstructure for optimal routing
        
        Args:
            symbol: The ticker symbol
            size: Order size
            
        Returns:
            Market analysis including liquidity, spread, and venue recommendations
        """
        try:
            # Get market data
            market_data = await self.ibkr_service.get_market_data(symbol)
            
            # Get market depth
            # This would use Level 2 data in production
            
            # Analyze liquidity
            liquidity_analysis = await self._analyze_liquidity(symbol, size)
            
            # Recommend venues
            venue_recommendations = self._recommend_venues(
                symbol, size, liquidity_analysis
            )
            
            return {
                "status": "success",
                "symbol": symbol,
                "market_data": market_data,
                "liquidity_analysis": liquidity_analysis,
                "venue_recommendations": venue_recommendations,
                "optimal_algorithm": self._recommend_algorithm(size, liquidity_analysis)
            }
            
        except Exception as e:
            logger.error(f"Market microstructure analysis error: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def create_smart_order(
        self,
        symbol: str,
        action: str,
        quantity: Decimal,
        account: str,
        order_type: str = "LMT",
        price: Optional[float] = None,
        time_in_force: str = "DAY",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a smart order with advanced features
        
        Args:
            symbol: The ticker symbol
            action: BUY or SELL
            quantity: Order quantity
            account: Account ID
            order_type: Order type (LMT, MKT, etc.)
            price: Limit price
            time_in_force: Time in force
            **kwargs: Additional order parameters
            
        Returns:
            Order details
        """
        try:
            # Create contract
            contract = await self.ibkr_service.create_contract(symbol)
            
            # Create order with smart routing
            order = Order()
            order.action = action
            order.orderType = order_type
            order.totalQuantity = float(quantity)
            order.account = account
            order.tif = time_in_force
            
            if price and order_type == "LMT":
                order.lmtPrice = price
            
            # Add smart routing parameters
            order.smartComboRoutingParams = []
            
            # Add any custom parameters
            for key, value in kwargs.items():
                if hasattr(order, key):
                    setattr(order, key, value)
            
            # Place order
            trade = self.ibkr_service.ib.placeOrder(contract, order)
            
            return {
                "status": "success",
                "order_id": trade.order.orderId,
                "symbol": symbol,
                "action": action,
                "quantity": float(quantity),
                "order_type": order_type
            }
            
        except Exception as e:
            logger.error(f"Smart order creation error: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    # Routing algorithm implementations
    
    async def _route_smart(self, config: RoutingConfig) -> RoutingResult:
        """Route using IB's Smart routing"""
        logger.info(f"Routing order via SMART: {config.symbol} {config.action} {config.quantity}")
        
        # Get current market data
        market_data = await self.ibkr_service.get_market_data(config.symbol)
        current_price = market_data["data"]["data"].get("last", 0)
        
        # Create smart order
        order_result = await self.ibkr_service.place_adaptive_order(
            symbol=config.symbol,
            action=config.action,
            quantity=float(config.quantity),
            price=current_price * (1.001 if config.action == "BUY" else 0.999),
            account=config.account,
            priority=config.urgency
        )
        
        if order_result["status"] == "success":
            # Wait for fills
            await asyncio.sleep(2)
            
            # Get order status
            order_status = await self.oms.get_order_status(order_result["orderId"])
            
            return RoutingResult(
                order_id=order_result["orderId"],
                routing_algorithm=RoutingAlgorithm.SMART,
                exchanges_used=["SMART"],
                fills=[],  # Would be populated from order status
                avg_price=Decimal(str(order_status.get("avg_fill_price", 0))),
                total_commission=Decimal("0"),  # Would calculate from fills
                execution_time=timedelta(seconds=0),
                slippage=Decimal("0")
            )
        else:
            raise Exception(f"Order placement failed: {order_result}")
    
    async def _route_directed(self, config: RoutingConfig) -> RoutingResult:
        """Route directly to specific exchange"""
        logger.info(f"Routing order via DIRECTED: {config.symbol} to {config.allowed_exchanges}")
        
        # Use first allowed exchange or default
        exchange = config.allowed_exchanges[0] if config.allowed_exchanges else "NASDAQ"
        
        # Create contract with specific exchange
        contract = await self.ibkr_service.create_contract(
            config.symbol,
            exchange=exchange
        )
        
        # Create directed order
        order = Order()
        order.action = config.action
        order.orderType = "LMT"
        order.totalQuantity = float(config.quantity)
        order.account = config.account
        
        # Get current price
        market_data = await self.ibkr_service.get_market_data(config.symbol)
        current_price = market_data["data"]["data"].get("last", 0)
        order.lmtPrice = current_price * (1.001 if config.action == "BUY" else 0.999)
        
        # Place order
        trade = self.ibkr_service.ib.placeOrder(contract, order)
        
        return RoutingResult(
            order_id=trade.order.orderId,
            routing_algorithm=RoutingAlgorithm.DIRECTED,
            exchanges_used=[exchange],
            fills=[],
            avg_price=Decimal(str(order.lmtPrice)),
            total_commission=Decimal("0"),
            execution_time=timedelta(seconds=0),
            slippage=Decimal("0")
        )
    
    async def _route_dark_pool(self, config: RoutingConfig) -> RoutingResult:
        """Route to dark pools for large orders"""
        logger.info(f"Routing order via DARK POOL: {config.symbol} {config.quantity}")
        
        # Create dark pool seeking order
        order = Order()
        order.action = config.action
        order.orderType = "LMT"
        order.totalQuantity = float(config.quantity)
        order.account = config.account
        
        # Get midpoint price
        market_data = await self.ibkr_service.get_market_data(config.symbol)
        data = market_data["data"]["data"]
        midpoint = (data.get("bid", 0) + data.get("ask", 0)) / 2
        order.lmtPrice = midpoint
        
        # Add dark pool parameters
        order.algoStrategy = "DarkIce"
        order.algoParams = []
        order.algoParams.append(TagValue("displaySize", str(config.max_display_size or 0)))
        order.algoParams.append(TagValue("startTime", ""))
        order.algoParams.append(TagValue("endTime", ""))
        
        # Create contract
        contract = await self.ibkr_service.create_contract(config.symbol)
        
        # Place order
        trade = self.ibkr_service.ib.placeOrder(contract, order)
        
        return RoutingResult(
            order_id=trade.order.orderId,
            routing_algorithm=RoutingAlgorithm.DARK_POOL,
            exchanges_used=["DARKPOOL"],
            fills=[],
            avg_price=Decimal(str(midpoint)),
            total_commission=Decimal("0"),
            execution_time=timedelta(seconds=0),
            slippage=Decimal("0")
        )
    
    async def _route_sweep(self, config: RoutingConfig) -> RoutingResult:
        """Sweep multiple venues simultaneously"""
        logger.info(f"Routing order via SWEEP: {config.symbol} {config.quantity}")
        
        # Split order across multiple venues
        venues = ["SMART", "NASDAQ", "NYSE", "BATS"]
        slice_size = config.quantity / len(venues)
        
        orders = []
        for venue in venues:
            # Create order for each venue
            order_result = await self.create_smart_order(
                symbol=config.symbol,
                action=config.action,
                quantity=slice_size,
                account=config.account,
                exchange=venue
            )
            if order_result["status"] == "success":
                orders.append(order_result["order_id"])
        
        return RoutingResult(
            order_id=orders[0] if orders else 0,  # Primary order ID
            routing_algorithm=RoutingAlgorithm.SWEEP,
            exchanges_used=venues,
            fills=[],
            avg_price=Decimal("0"),
            total_commission=Decimal("0"),
            execution_time=timedelta(seconds=0),
            slippage=Decimal("0"),
            metrics={"child_orders": orders}
        )
    
    async def _route_midpoint(self, config: RoutingConfig) -> RoutingResult:
        """Route using midpoint matching"""
        logger.info(f"Routing order via MIDPOINT: {config.symbol}")
        
        # Create midpoint order
        order = Order()
        order.action = config.action
        order.orderType = "MID"  # Midpoint order type
        order.totalQuantity = float(config.quantity)
        order.account = config.account
        
        # Create contract
        contract = await self.ibkr_service.create_contract(config.symbol)
        
        # Place order
        trade = self.ibkr_service.ib.placeOrder(contract, order)
        
        return RoutingResult(
            order_id=trade.order.orderId,
            routing_algorithm=RoutingAlgorithm.MIDPOINT,
            exchanges_used=["SMART"],
            fills=[],
            avg_price=Decimal("0"),
            total_commission=Decimal("0"),
            execution_time=timedelta(seconds=0),
            slippage=Decimal("0")
        )
    
    async def _route_pegged(self, config: RoutingConfig) -> RoutingResult:
        """Route using pegged orders"""
        logger.info(f"Routing order via PEGGED: {config.symbol}")
        
        # Create pegged order
        order = Order()
        order.action = config.action
        order.orderType = "PEG MKT"
        order.totalQuantity = float(config.quantity)
        order.account = config.account
        order.auxPrice = 0.01  # Offset amount
        
        # Create contract
        contract = await self.ibkr_service.create_contract(config.symbol)
        
        # Place order
        trade = self.ibkr_service.ib.placeOrder(contract, order)
        
        return RoutingResult(
            order_id=trade.order.orderId,
            routing_algorithm=RoutingAlgorithm.PEGGED,
            exchanges_used=["SMART"],
            fills=[],
            avg_price=Decimal("0"),
            total_commission=Decimal("0"),
            execution_time=timedelta(seconds=0),
            slippage=Decimal("0")
        )
    
    async def _route_auction(self, config: RoutingConfig) -> RoutingResult:
        """Route to auction"""
        # Use the existing auction order implementation
        order_result = await self.ibkr_service.place_auction_order(
            symbol=config.symbol,
            action=config.action,
            quantity=float(config.quantity),
            price=0,  # Market price
            account=config.account
        )
        
        return RoutingResult(
            order_id=order_result.get("orderId", 0),
            routing_algorithm=RoutingAlgorithm.AUCTION,
            exchanges_used=["AUCTION"],
            fills=[],
            avg_price=Decimal("0"),
            total_commission=Decimal("0"),
            execution_time=timedelta(seconds=0),
            slippage=Decimal("0")
        )
    
    async def _route_conditional(self, config: RoutingConfig) -> RoutingResult:
        """Route with conditional logic"""
        logger.info(f"Routing order via CONDITIONAL: {config.symbol}")
        
        # Analyze market conditions
        market_data = await self.ibkr_service.get_market_data(config.symbol)
        data = market_data["data"]["data"]
        spread = data.get("ask", 0) - data.get("bid", 0)
        
        # Choose routing based on conditions
        if spread > 0.05:  # Wide spread
            return await self._route_midpoint(config)
        elif config.quantity > 10000:  # Large order
            return await self._route_dark_pool(config)
        else:
            return await self._route_smart(config)
    
    async def _route_liquidity_seeking(self, config: RoutingConfig) -> RoutingResult:
        """Route seeking liquidity"""
        logger.info(f"Routing order via LIQUIDITY SEEKING: {config.symbol}")
        
        # Create liquidity seeking order
        order = Order()
        order.action = config.action
        order.orderType = "LMT"
        order.totalQuantity = float(config.quantity)
        order.account = config.account
        
        # Get current price
        market_data = await self.ibkr_service.get_market_data(config.symbol)
        current_price = market_data["data"]["data"].get("last", 0)
        order.lmtPrice = current_price
        
        # Add liquidity seeking algo
        order.algoStrategy = "ArrivalPx"
        order.algoParams = []
        order.algoParams.append(TagValue("maxPctVol", "0.1"))
        order.algoParams.append(TagValue("riskAversion", "Neutral"))
        order.algoParams.append(TagValue("startTime", ""))
        order.algoParams.append(TagValue("endTime", ""))
        
        # Create contract
        contract = await self.ibkr_service.create_contract(config.symbol)
        
        # Place order
        trade = self.ibkr_service.ib.placeOrder(contract, order)
        
        return RoutingResult(
            order_id=trade.order.orderId,
            routing_algorithm=RoutingAlgorithm.LIQUIDITY_SEEKING,
            exchanges_used=["SMART"],
            fills=[],
            avg_price=Decimal(str(current_price)),
            total_commission=Decimal("0"),
            execution_time=timedelta(seconds=0),
            slippage=Decimal("0")
        )
    
    async def _route_cost_aware(self, config: RoutingConfig) -> RoutingResult:
        """Route minimizing total cost including fees"""
        logger.info(f"Routing order via COST AWARE: {config.symbol}")
        
        # Analyze costs across venues
        # This would include maker/taker fees, rebates, etc.
        
        # For now, use adaptive with patient priority
        return await self._route_smart(config)
    
    # Helper methods
    
    async def _analyze_liquidity(
        self,
        symbol: str,
        size: int
    ) -> Dict[str, Any]:
        """Analyze liquidity for a symbol"""
        market_data = await self.ibkr_service.get_market_data(symbol)
        data = market_data["data"]["data"]
        
        return {
            "bid_size": data.get("bid_size", 0),
            "ask_size": data.get("ask_size", 0),
            "spread": data.get("ask", 0) - data.get("bid", 0),
            "volume": data.get("volume", 0),
            "liquidity_score": self._calculate_liquidity_score(data, size)
        }
    
    def _calculate_liquidity_score(self, market_data: Dict, size: int) -> float:
        """Calculate liquidity score"""
        # Simplified scoring
        volume = market_data.get("volume", 0)
        spread = market_data.get("ask", 0) - market_data.get("bid", 0)
        
        if volume > 0 and spread > 0:
            return min(1.0, volume / (size * 10))
        return 0.0
    
    def _recommend_venues(
        self,
        symbol: str,
        size: int,
        liquidity_analysis: Dict
    ) -> List[str]:
        """Recommend venues based on analysis"""
        venues = ["SMART"]
        
        if size > 10000:
            venues.append("DARKPOOL")
        
        if liquidity_analysis["spread"] > 0.05:
            venues.append("MIDPOINT")
        
        return venues
    
    def _recommend_algorithm(
        self,
        size: int,
        liquidity_analysis: Dict
    ) -> str:
        """Recommend routing algorithm"""
        if size > 50000:
            return RoutingAlgorithm.DARK_POOL.value
        elif liquidity_analysis["spread"] > 0.10:
            return RoutingAlgorithm.MIDPOINT.value
        elif size < 1000:
            return RoutingAlgorithm.SMART.value
        else:
            return RoutingAlgorithm.LIQUIDITY_SEEKING.value
    
    async def _calculate_execution_metrics(
        self,
        result: RoutingResult,
        config: RoutingConfig
    ) -> Dict[str, Any]:
        """Calculate execution quality metrics"""
        # Get arrival price (price when order was placed)
        # Calculate implementation shortfall
        # Calculate effective spread
        
        return {
            "implementation_shortfall": 0.0,
            "effective_spread": 0.0,
            "price_improvement": 0.0,
            "fill_rate": 100.0
        }
