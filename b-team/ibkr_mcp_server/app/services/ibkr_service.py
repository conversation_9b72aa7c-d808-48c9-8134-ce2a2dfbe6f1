import os
import time
import logging
import asyncio
import nest_asyncio

print("🔥 DEBUG: ibkr_service.py module is being loaded/reloaded!")
from datetime import datetime
from typing import List, Dict, Optional, Any, Union, TYPE_CHECKING
from fastapi import HTTPException

# Use TYPE_CHECKING to avoid circular imports
if TYPE_CHECKING:
    from order_management_service import OrderManagementService

from decimal import Decimal
from dotenv import load_dotenv

# Import ib_async components
from ib_async import IB, Contract, Stock, Option, TagValue, Trade, OrderState
from ib_async.order import Order, LimitOrder, MarketOrder, StopOrder
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type


try:
    nest_asyncio.apply()
except ValueError as e:
    print(f"nest_asyncio.apply() skipped: {e}")

# Configure logging
logger = logging.getLogger('ibkr-service')

load_dotenv(override=True)

class IBKRConnectionError(Exception):
    """Exception raised for IBKR connection issues"""
    pass

class IBKRTimeoutError(Exception):
    """Exception raised for IBKR timeout issues"""
    pass

class IBKRDataError(Exception):
    """Exception raised for IBKR data issues"""
    pass

class IBKROrderError(Exception):
    """Exception raised for IBKR order issues"""
    pass

class IBKRService:
    def __init__(self):
        self.ib = IB()
        self.order_management_service_delegate: Optional['OrderManagementService'] = None # Delegate for order management events
        logger.debug(f"Created IB object: {type(self.ib)}")
        logger.debug(f"IB object has placeOrder: {hasattr(self.ib, 'placeOrder')}")
        logger.debug(f"IB object has placeOrderAsync: {hasattr(self.ib, 'placeOrderAsync')}")
        self.host = os.getenv("IBKR_HOST", "127.0.0.1")
        raw_port = os.getenv("IBKR_PORT", "7496").split()[0]
        self.port = int(raw_port)
        self.client_id = int(os.getenv("IBKR_CLIENT_ID", "0"))
        self.connected = False
        self.connection_time = None
        self.connection_attempts = 0
        self.max_connection_attempts = 3
        self.reconnect_delay = 5  # seconds
        self.last_error = None
        self.readonly = True # Default to readonly

        self._register_event_handlers()
        self._next_order_id = 1

    async def initialize(self):
        """Initialize the service without connecting to TWS"""
        logger.info("Initializing IBKR service")
        # Reset connection state
        self.connection_attempts = 0
        self.last_error = None
        return True

    def set_order_management_service(self, oms_delegate: 'OrderManagementService'):
        self.order_management_service_delegate = oms_delegate

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type(IBKRConnectionError)
    ) # The client_id_override parameter was removed as it's set on the instance by the tool
    async def connect(self, force: bool = False, readonly: Optional[bool] = None): # Added readonly parameter
        """
        Connect to IBKR TWS or Gateway with retry logic

        Args:
            force: If True, force reconnection even if already connected
        """
        # If already connected and not forcing reconnection, return
        if self.connected and not force:
            logger.debug("Already connected to IBKR")
            return True

        # If forcing reconnection, disconnect first
        if self.connected and force:
            await self.disconnect()

        # Update readonly setting if provided
        if readonly is not None:
            self.readonly = readonly

        self.connection_attempts += 1

        try:
            logger.info(f"Connecting to IBKR at {self.host}:{self.port} (attempt {self.connection_attempts})")
            await self.ib.connectAsync(
                host=self.host, # Use self.host
                port=self.port,
                clientId=self.client_id, # Uses the instance's client_id
                readonly=self.readonly,
                timeout=20
            )

            # Verify connection is working by making a simple request
            await self.ib.reqCurrentTimeAsync()

            self.connected = True
            self.connection_time = datetime.now()
            self.connection_attempts = 0  # Reset counter on success
            logger.info(f"Connected to IBKR at {self.host}:{self.port}")
            return True

        except asyncio.TimeoutError as e:
            self.connected = False
            self.last_error = str(e)
            logger.error(f"Timeout connecting to IBKR: {str(e)}")
            raise IBKRTimeoutError(f"Timeout connecting to IBKR: {str(e)}")

        except Exception as e:
            self.connected = False
            self.last_error = str(e)
            logger.error(f"Failed to connect to IBKR: {str(e)}")

            # Check if TWS is not running or not connected to IB servers
            if "Not connected" in str(e) or "Connection refused" in str(e):
                raise IBKRConnectionError(f"TWS/Gateway not running or not accessible: {str(e)}")
            else:
                raise IBKRConnectionError(f"Failed to connect to IBKR: {str(e)}")

    def _register_event_handlers(self):
        """Register handlers for ib_async events"""
        self.ib.orderStatusEvent += self._on_order_status
        self.ib.openOrderEvent += self._on_open_order # Correct: _on_open_order handles individual open orders
        # self.ib.openOrderEndEvent is not available in ib_async 1.0.3 as per docs and error.
        # The _on_open_order_end method and corresponding handle_open_order_end in OrderManagementService
        # will not be called by this event. reqAllOpenOrdersAsync() completion implies all orders are fetched.
        # Add other relevant handlers here, e.g., self.ib.execDetailsEvent += self._on_exec_details
        logger.debug("IBKRService event handlers registered")

    def _on_order_status(self, trade: Trade):
        """Handle order status updates from ib_async and delegate"""
        logger.debug(f"Received orderStatusEvent for OrderID {trade.order.orderId}, Status: {trade.orderStatus.status}")
        if self.order_management_service_delegate:
            self.order_management_service_delegate.handle_order_status_update(
                order_id=trade.order.orderId,
                status_str=trade.orderStatus.status,
                filled=float(trade.orderStatus.filled), # ib_async uses Decimal, convert to float
                remaining=float(trade.orderStatus.remaining), # ib_async uses Decimal, convert to float
                avg_fill_price=trade.orderStatus.avgFillPrice,
                perm_id=trade.order.permId,
                parent_id=trade.order.parentId,
                last_fill_price=trade.orderStatus.lastFillPrice,
                client_id=trade.order.clientId,
                why_held=trade.orderStatus.whyHeld,
                mkt_cap_price=getattr(trade.orderStatus, 'mktCapPrice', None) # mktCapPrice might not always be present
            )

    def _on_open_order(self, trade: Trade):
        """Handle open order information from ib_async and delegate"""
        logger.debug(f"Received openOrderEvent for OrderID {trade.order.orderId}, Symbol: {trade.contract.symbol}")
        if self.order_management_service_delegate:
            self.order_management_service_delegate.handle_open_order(trade.order.orderId, trade.contract, trade.order, trade.orderStatus)

    def _on_open_order_end(self):
        """Handle end of open order list from ib_async and delegate"""
        # This method is unlikely to be called if openOrderEndEvent is not available
        # in the ib_async version being used.
        logger.debug("Received _on_open_order_end call (potentially unused if no openOrderEndEvent)")
        if self.order_management_service_delegate and hasattr(self.order_management_service_delegate, 'handle_open_order_end'):
             self.order_management_service_delegate.handle_open_order_end()

    async def disconnect(self):
        """Disconnect from IBKR asynchronously"""
        if self.connected:
            logger.info("Disconnecting from IBKR")
            # Use asyncio.to_thread for potentially blocking operations
            await asyncio.to_thread(self.ib.disconnect)
            self.connected = False
            logger.info("Disconnected from IBKR")
            return True
        return False

    async def check_connection(self):
        """Check if connection is still valid and reconnect if needed with timeout handling"""
        if not self.connected:
            logger.info("Not connected to IBKR, attempting to connect")
            return await self.connect()

        try:
            # Try a simple request to verify connection with timeout
            timeout_duration = 10  # 10 seconds timeout

            # Check if ib_insync connection is active
            if hasattr(self.ib, 'isConnected') and not self.ib.isConnected():
                logger.warning("IBKR connection lost (isConnected returned False)")
                self.connected = False
                return await self.connect(force=True)

            # Try to get current time as a simple connection test
            async def test_connection():
                return await asyncio.to_thread(self.ib.reqCurrentTime)

            try:
                await asyncio.wait_for(test_connection(), timeout=timeout_duration)
                logger.debug("Connection to IBKR is valid and responsive")
                return True
            except asyncio.TimeoutError:
                logger.warning("Connection test timed out, reconnecting...")
                self.connected = False
                return await self.connect(force=True)

        except Exception as e:
            logger.warning(f"Connection check failed: {str(e)}, reconnecting...")
            self.connected = False
            return await self.connect(force=True)

    async def ensure_connected(self):
        print("DEBUG: ensure_connected method called - FIXES ARE LOADED!")
        """Ensure connection is established and stable"""
        return await self.check_connection()

    async def get_portfolio(self) -> List[Dict]:
        """Get portfolio positions with connection management"""
        try:
            await self.check_connection()

            logger.info("Fetching portfolio positions")
            positions = await asyncio.to_thread(self.ib.positions)

            # Format portfolio data
            portfolio_data = []
            for pos in positions:
                position_data = {
                    'symbol': pos.contract.symbol,
                    'secType': pos.contract.secType,
                    'exchange': pos.contract.exchange,
                    'currency': pos.contract.currency,
                    'position': pos.position,
                    'avgCost': pos.avgCost,
                    'marketPrice': getattr(pos, 'marketPrice', 0),
                    'marketValue': getattr(pos, 'marketValue', 0),
                    'unrealizedPNL': getattr(pos, 'unrealizedPNL', 0),
                    'realizedPNL': getattr(pos, 'realizedPNL', 0)
                }
                portfolio_data.append(position_data)

            logger.info(f"Retrieved {len(portfolio_data)} portfolio positions")
            return portfolio_data

        except Exception as e:
            logger.error(f"Failed to get portfolio: {str(e)}")
            raise IBKRDataError(f"Failed to get portfolio: {str(e)}")

    async def get_accounts(self) -> Dict:
        """Get managed accounts information"""
        try:
            await self.check_connection()

            logger.info("Fetching managed accounts")
            managed_accounts = self.ib.managedAccounts()

            # Format accounts data
            accounts_list = []
            for account_id in managed_accounts:
                accounts_list.append({
                    'accountId': account_id,
                    'accountType': 'INDIVIDUAL'  # Default type, could be enhanced
                })

            logger.info(f"Retrieved {len(accounts_list)} managed accounts")
            return {
                'status': 'success',
                'accounts': accounts_list,
                'default_account': managed_accounts[0] if managed_accounts else None
            }

        except Exception as e:
            logger.error(f"Failed to get accounts: {str(e)}")
            raise IBKRDataError(f"Failed to get accounts: {str(e)}")

    async def get_account_summary(self, tags: Optional[List[str]] = None) -> Dict:
        """Get account summary with connection management"""
        try:
            await self.check_connection()

            if tags is None:
                tags = ["NetLiquidation", "TotalCashValue", "SettledCash",
                        "AccruedCash", "BuyingPower", "EquityWithLoanValue",
                        "PreviousDayEquityWithLoanValue", "GrossPositionValue",
                        "RegTMargin", "InitMarginReq", "MaintMarginReq",
                        "AvailableFunds", "ExcessLiquidity", "Cushion",
                        "FullInitMarginReq", "FullMaintMarginReq", "FullAvailableFunds",
                        "FullExcessLiquidity", "LookAheadNextChange",
                        "LookAheadInitMarginReq", "LookAheadMaintMarginReq",
                        "LookAheadAvailableFunds", "LookAheadExcessLiquidity",
                        "HighestSeverity", "DayTradesRemaining", "Leverage"]

            logger.info("Fetching account summary")
            account_summary = await self.ib.accountSummaryAsync()

            # Convert to dictionary format
            summary_dict = {}
            for item in account_summary:
                summary_dict[item.tag] = {
                    'value': item.value,
                    'currency': item.currency
                }

            logger.info("Retrieved account summary")
            return summary_dict

        except Exception as e:
            logger.error(f"Failed to get account summary: {str(e)}")
            raise IBKRDataError(f"Failed to get account summary: {str(e)}")

    async def fetch_portfolio_details(self) -> Dict:
        """Fetch portfolio details from IBKR (legacy method)"""
        try:
            portfolio_data = await self.get_portfolio()
            account_summary = await self.get_account_summary()

            return {
                'positions': portfolio_data,
                'account_summary': account_summary
            }

        except Exception as e:
            logger.error(f"Failed to fetch portfolio details: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to fetch portfolio: {str(e)}"
            )

    async def create_contract(self, symbol: str, sec_type: str = "STK", 
                             exchange: str = "SMART", currency: str = "USD", **kwargs) -> Contract:
        """
        Create a contract object for trading
        
        Args:
            symbol (str): The ticker symbol
            sec_type (str): Security type (STK, OPT, FUT, CASH, etc.)
            exchange (str): Exchange (default: SMART)
            currency (str): Currency (default: USD)
            **kwargs: Additional contract parameters
                - For options: expiry, strike, right
                - For futures: expiry, multiplier
                
        Returns:
            Contract: IB contract object
        """
        try:
            contract = Contract()
            contract.symbol = symbol
            contract.secType = sec_type
            contract.exchange = exchange
            contract.currency = currency
            
            # Add security-specific parameters
            if sec_type == "OPT":
                if not all(k in kwargs for k in ['expiry', 'strike', 'right']):
                    raise ValueError("Options require expiry, strike, and right parameters")
                contract.lastTradeDateOrContractMonth = kwargs['expiry']
                contract.strike = kwargs['strike']
                contract.right = kwargs['right']
            
            elif sec_type == "FUT":
                if 'expiry' not in kwargs:
                    raise ValueError("Futures require expiry parameter")
                contract.lastTradeDateOrContractMonth = kwargs['expiry']
                if 'multiplier' in kwargs:
                    contract.multiplier = kwargs['multiplier']
            
            # Add any other parameters
            for key, value in kwargs.items():
                if key not in ['expiry', 'strike', 'right', 'multiplier'] and hasattr(contract, key):
                    setattr(contract, key, value)
            
            logger.debug(f"Created contract: {contract}")
            return contract
        
        except Exception as e:
            logger.error(f"Failed to create contract: {str(e)}")
            raise IBKROrderError(f"Failed to create contract: {str(e)}")

    async def create_order(self, order_type: str, symbol: str, action: str,
                         quantity: Union[int, float, Decimal], account: str, # Added account parameter
                         price: Optional[float] = None, **kwargs) -> Dict:
        """
        Creates and places an order with Interactive Brokers TWS.

        Args:
            order_type (str): Type of order ("MKT", "LMT", "STP", "auction", "adaptive", "bracket")
            symbol (str): The ticker symbol
            action (str): "BUY" or "SELL"
            quantity: Number of shares/contracts
            account (str): The account ID to place the order in
            price: Primary price point for the order
            **kwargs: Additional parameters based on order type
                - For limit: price (required)
                - For stop: stop_price (required)
                - For adaptive: priority ("Urgent", "Normal", "Patient")
                - For bracket: profit_price, stop_price
                - For all: exchange (defaults to "SMART"), time_in_force

        Returns:
            dict: Order status information including order ID

        Raises:
            ConnectionError: If not connected to TWS
            ValueError: If invalid parameters are provided
            IBKROrderError: For TWS API specific errors
        """
        try:
            # Check connection
            await self.check_connection()

            # Create contract
            exchange = kwargs.get("exchange", "SMART")
            contract = await self.create_contract(symbol, exchange=exchange)

            # Convert quantity to Decimal if it's not already
            if not isinstance(quantity, Decimal):
                quantity = Decimal(str(quantity))

            # Handle different order types
            if order_type.upper() == "MKT":
                order = Order()
                order.action = action
                order.orderType = "MKT"
                order.totalQuantity = float(quantity)
                order.account = str(account)

                # Set time in force if provided
                time_in_force = kwargs.get("time_in_force", "DAY")
                order.tif = time_in_force

                # Place the order
                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed market order: {trade.order.orderId} for {symbol}")

                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Market order placed for {symbol}"
                }

            elif order_type.upper() == "LMT":
                if price is None:
                    raise ValueError("Price is required for limit orders")

                order = Order()
                order.action = action
                order.orderType = "LMT"
                order.totalQuantity = float(quantity)
                order.lmtPrice = float(price)
                order.account = str(account)

                # Set time in force if provided
                time_in_force = kwargs.get("time_in_force", "DAY")
                order.tif = time_in_force

                # Place the order
                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed limit order: {trade.order.orderId} for {symbol} @ {price}")

                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Limit order placed for {symbol} @ {price}"
                }

            elif order_type.upper() == "ADAPTIVE":
                if price is None:
                    raise ValueError("Price is required for adaptive orders")

                order = Order()
                order.action = action
                order.orderType = "LMT"
                order.totalQuantity = float(quantity)
                order.lmtPrice = float(price)
                order.account = str(account)

                # Set adaptive algorithm
                order.algoStrategy = "Adaptive"
                order.algoParams = []
                priority = kwargs.get("priority", "Normal")
                order.algoParams.append(TagValue("adaptivePriority", priority))

                # Set time in force if provided
                time_in_force = kwargs.get("time_in_force", "DAY")
                order.tif = time_in_force

                # Place the order
                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed adaptive order: {trade.order.orderId} for {symbol} @ {price} (priority: {priority})")

                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Adaptive order placed for {symbol} @ {price}"
                }

            elif order_type.upper() == "AUCTION":
                if price is None:
                    raise ValueError("Price is required for auction orders")

                order = Order()
                order.action = action
                order.orderType = "LMT"
                order.totalQuantity = float(quantity)
                order.lmtPrice = float(price)
                order.account = str(account)

                # Set auction order parameters
                order.tif = "AUC"  # Auction time in force

                # Place the order
                trade = self.ib.placeOrder(contract, order)
                logger.info(f"Placed auction order: {trade.order.orderId} for {symbol} @ {price}")

                return {
                    "status": "success",
                    "orderId": trade.order.orderId,
                    "message": f"Auction order placed for {symbol} @ {price}"
                }

            else:
                raise ValueError(f"Unknown order type: {order_type}")

        except Exception as e:
            logger.error(f"Failed to create order: {str(e)}")
            raise IBKROrderError(f"Failed to create order: {str(e)}")

    async def place_adaptive_order(self, symbol: str, action: str, quantity: Union[int, float, Decimal],
                                 price: float, account: str, priority: str = "Normal",
                                 exchange: str = "SMART") -> Dict:
        """
        Place an Adaptive algorithm order

        Args:
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            price: Limit price
            account: The account ID to place the order in
            priority: Priority setting ("Urgent", "Normal", "Patient")
            exchange: Exchange (default: SMART)

        Returns:
            dict: Order status information
        """
        logger.info(f"Placing adaptive order for {symbol}: {action} {quantity} @ {price} (priority: {priority})")
        return await self.create_order("adaptive", symbol, action, quantity, account, price,
                                     priority=priority, exchange=exchange)

    async def place_bracket_order(
        self,
        symbol: str,
        action: str,
        quantity: Union[int, float, Decimal],
        entry_price: float,
        profit_price: float,
        stop_price: float,
        account: str,
        exchange: str = "SMART"
    ) -> Dict:
        """
        Place a bracket order with entry, profit target, and stop loss

        Args:
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            entry_price: Entry order price
            profit_price: Profit target price
            stop_price: Stop loss price
            account: The account ID to place the order in
            exchange: Exchange (default: SMART)

        Returns:
            dict: Order status information with all order IDs
        """
        try:
            await self.check_connection()

            # Create contract
            contract = await self.create_contract(symbol, exchange=exchange)

            # Convert quantity to float for ib_async
            quantity_float = float(quantity)

            # Create bracket order using ib_async helper
            logger.info(f"Creating bracket order for {symbol}: {action} {quantity_float} @ entry={entry_price}, profit={profit_price}, stop={stop_price}")

            bracket = self.ib.bracketOrder(
                action=action,
                quantity=quantity_float,
                limitPrice=entry_price,
                takeProfitPrice=profit_price,
                stopLossPrice=stop_price
            )

            # Set account for all orders in the bracket
            for order in bracket:
                order.account = str(account)

            # Place all orders in the bracket
            order_ids = []
            trades = []

            for order in bracket:
                trade = self.ib.placeOrder(contract, order)
                trades.append(trade)
                order_ids.append(trade.order.orderId)
                logger.info(f"Placed bracket order component: {trade.order.orderId} ({order.orderType})")

            logger.info(f"Successfully placed bracket order for {symbol}: parent={order_ids[0]}, profit={order_ids[1]}, stop={order_ids[2]}")

            return {
                "status": "success",
                "orderId": order_ids[0],  # Return parent order ID as primary
                "parentOrderId": order_ids[0],
                "profitOrderId": order_ids[1],
                "stopOrderId": order_ids[2],
                "allOrderIds": order_ids,
                "message": f"Bracket order placed for {symbol}: entry @ {entry_price}, profit @ {profit_price}, stop @ {stop_price}"
            }

        except Exception as e:
            logger.error(f"Failed to place bracket order for {symbol}: {str(e)}")
            raise IBKROrderError(f"Failed to place bracket order: {str(e)}")

    async def place_auction_order(self, symbol: str, action: str, quantity: Union[int, float, Decimal],
                               price: float, account: str, exchange: str = "SMART") -> Dict:
        """
        Place an Auction order

        Args:
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            price: Limit price
            account: The account ID to place the order in
            exchange: Exchange (default: SMART)

        Returns:
            dict: Order status information
        """
        logger.info(f"Placing auction order for {symbol}: {action} {quantity} @ {price}")
        return await self.create_order("auction", symbol, action, quantity, account, price, exchange=exchange)

    async def get_historical_data(
        self,
        symbol: str,
        duration: str = "1 Y",
        bar_size: str = "1 day",
        what_to_show: str = "TRADES",
        use_rth: bool = True,
        exchange: str = "SMART"
    ) -> List[Dict]:
        """
        Get historical market data for a symbol

        Args:
            symbol: The ticker symbol
            duration: Duration string (e.g., "1 Y", "6 M", "1 D")
            bar_size: Bar size (e.g., "1 day", "1 hour", "5 mins")
            what_to_show: Data type ("TRADES", "MIDPOINT", "BID", "ASK")
            use_rth: Use regular trading hours only
            exchange: Exchange (default: SMART)

        Returns:
            List of historical bars
        """
        try:
            await self.check_connection()

            # Create contract
            contract = await self.create_contract(symbol, exchange=exchange)

            # Request historical data
            logger.info(f"Requesting historical data for {symbol}: {duration}, {bar_size}")
            bars = await self.ib.reqHistoricalDataAsync(
                contract=contract,
                endDateTime='',  # Use current time
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow=what_to_show,
                useRTH=use_rth,
                formatDate=1
            )

            # Convert to list of dictionaries
            historical_data = []
            for bar in bars:
                bar_data = {
                    "date": bar.date.strftime("%Y-%m-%d %H:%M:%S") if hasattr(bar.date, 'strftime') else str(bar.date),
                    "open": float(bar.open),
                    "high": float(bar.high),
                    "low": float(bar.low),
                    "close": float(bar.close),
                    "volume": int(bar.volume),
                    "average": float(bar.average) if bar.average else 0.0,
                    "barCount": int(bar.barCount) if bar.barCount else 0
                }
                historical_data.append(bar_data)

            logger.info(f"Retrieved {len(historical_data)} historical bars for {symbol}")
            return historical_data

        except Exception as e:
            logger.error(f"Failed to get historical data for {symbol}: {str(e)}")
            raise IBKRDataError(f"Failed to get historical data: {str(e)}")

    async def get_market_data(self, symbol: str, exchange: str = "SMART") -> Dict:
        """
        Get current market data for a symbol

        Args:
            symbol: The ticker symbol
            exchange: Exchange (default: SMART)

        Returns:
            Market data dictionary
        """
        try:
            await self.check_connection()

            # Create contract
            contract = await self.create_contract(symbol, exchange=exchange)

            # Request market data snapshot
            logger.info(f"Requesting market data for {symbol}")
            ticker = self.ib.reqMktData(contract, snapshot=True)

            # Wait for data to be populated
            await asyncio.sleep(2)

            # Extract market data
            market_data = {
                "symbol": symbol,
                "bid": float(ticker.bid) if ticker.bid and ticker.bid > 0 else 0.0,
                "ask": float(ticker.ask) if ticker.ask and ticker.ask > 0 else 0.0,
                "last": float(ticker.last) if ticker.last and ticker.last > 0 else 0.0,
                "close": float(ticker.close) if ticker.close and ticker.close > 0 else 0.0,
                "bid_size": int(ticker.bidSize) if ticker.bidSize else 0,
                "ask_size": int(ticker.askSize) if ticker.askSize else 0,
                "last_size": int(ticker.lastSize) if ticker.lastSize else 0,
                "volume": int(ticker.volume) if ticker.volume else 0,
                "high": float(ticker.high) if ticker.high and ticker.high > 0 else 0.0,
                "low": float(ticker.low) if ticker.low and ticker.low > 0 else 0.0
            }

            # Cancel the market data subscription
            self.ib.cancelMktData(contract)

            logger.info(f"Retrieved market data for {symbol}: last={market_data['last']}")
            return {
                "status": "success",
                "data": {
                    "data": market_data
                }
            }

        except Exception as e:
            logger.error(f"Failed to get market data for {symbol}: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }

# Create a singleton instance
ibkr_service = IBKRService()