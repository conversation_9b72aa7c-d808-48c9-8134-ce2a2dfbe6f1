# /Users/<USER>/projects/b-team/ibkr_mcp_server/app/services/order_management_service.py
import asyncio
import logging
import math
from typing import Dict, Any, Callable, Optional, Union
from datetime import datetime
from ib_async import IB, Contract, Order as IBOrder, OrderStatus as IBOrderStatus # Use OrderStatus instead of OrderState
from decimal import Decimal

from ibkr_mcp_server.app.models.order_models import OrderStatus, OrderInfo
from ibkr_mcp_server.app.services.ibkr_service import IBKRService, IBKROrderError, IBKRConnectionError # Import existing service

logger = logging.getLogger('order-management-service') # Dedicated logger

class OrderManagementService:
    def __init__(self, ibkr_svc: IBKRService):
        self.ibkr_service = ibkr_svc # Reference to the main IBKRService
        self.ib: IB = ibkr_svc.ib # Convenience reference to the ib_async IB instance
        
        self._order_status_cache: Dict[int, OrderInfo] = {}
        self._active_orders_cache: Dict[int, OrderInfo] = {} # Stores OrderInfo objects
        self._order_event_callbacks: Dict[int, Callable[[OrderInfo], None]] = {}
        self._order_events: Dict[int, asyncio.Event] = {} # For async waiting

        self._open_orders_complete_event = asyncio.Event()

    async def _get_order_event(self, order_id: int) -> asyncio.Event:
        if order_id not in self._order_events:
            self._order_events[order_id] = asyncio.Event()
        return self._order_events[order_id]

    # --- Methods called by IBKRService (EWrapper delegates) ---
    def handle_order_status_update(self, order_id: int, status_str: str, filled: float,
                                   remaining: float, avg_fill_price: float, perm_id: int,
                                   parent_id: int, last_fill_price: float, client_id: int,
                                   why_held: str, mkt_cap_price: Optional[float]):
        logger.info(f"Handling order status update for OrderID {order_id}: {status_str}")
        
        # Sanitize infinity values that IBKR sometimes returns
        def sanitize_numeric_value(value: float) -> float:
            """Convert infinity/NaN values to reasonable defaults"""
            if value is None:
                return 0.0
            if not isinstance(value, (int, float)):
                return 0.0
            if not math.isfinite(value):  # Catches inf, -inf, and NaN
                return 0.0
            return float(value)
        
        # Sanitize all numeric values
        filled = sanitize_numeric_value(filled)
        remaining = sanitize_numeric_value(remaining)
        avg_fill_price = sanitize_numeric_value(avg_fill_price)
        last_fill_price = sanitize_numeric_value(last_fill_price)
        if mkt_cap_price is not None:
            mkt_cap_price = sanitize_numeric_value(mkt_cap_price)
        
        order_info = self._order_status_cache.get(order_id, OrderInfo(order_id=order_id))
        order_info.update_status(status_str, filled, remaining, avg_fill_price, perm_id,
                                 parent_id, last_fill_price, client_id, why_held, mkt_cap_price)
        self._order_status_cache[order_id] = order_info

        # Also update active orders cache if it's a terminal state
        if order_info.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.INACTIVE]:
            self._active_orders_cache.pop(order_id, None)
        elif order_id in self._active_orders_cache: # Update existing active order
             self._active_orders_cache[order_id].status = order_info.status
             self._active_orders_cache[order_id].filled = order_info.filled
             self._active_orders_cache[order_id].remaining = order_info.remaining
             # ... update other relevant fields ...
             self._active_orders_cache[order_id].timestamp = datetime.now()


        if order_id in self._order_event_callbacks:
            try:
                self._order_event_callbacks[order_id](order_info)
            except Exception as e:
                logger.error(f"Error in order status callback for {order_id}: {e}")
        
        if order_id in self._order_events:
            self._order_events[order_id].set() # Signal anyone waiting on this order_id

    def handle_open_order(self, order_id: int, contract: Contract, ib_order: IBOrder, ib_order_status: IBOrderStatus):
        logger.info(f"Handling open order: OrderID {order_id}, Symbol: {contract.symbol}")
        
        # Calculate filled and remaining quantities safely
        filled_qty = 0.0
        remaining_qty = 0.0
        
        if hasattr(ib_order, 'filledQuantity') and ib_order.filledQuantity is not None:
            filled_qty = self._sanitize_value(float(ib_order.filledQuantity))
        
        if hasattr(ib_order, 'totalQuantity') and ib_order.totalQuantity is not None:
            total_qty = self._sanitize_value(float(ib_order.totalQuantity))
            remaining_qty = max(0.0, total_qty - filled_qty)
        
        order_info = OrderInfo(
            order_id=order_id,
            status=OrderStatus(ib_order_status.status), # Convert from string
            filled=filled_qty,
            remaining=remaining_qty,
            avg_fill_price=self._sanitize_value(ib_order_status.avgFillPrice) if hasattr(ib_order_status, 'avgFillPrice') else 0.0,
            perm_id=ib_order.permId if hasattr(ib_order, 'permId') else 0,
            client_id=ib_order.clientId if hasattr(ib_order, 'clientId') else 0,
            contract=contract,
            order=ib_order,
            order_state=ib_order_status,
            timestamp=datetime.now()
        )
        self._active_orders_cache[order_id] = order_info
        self._order_status_cache[order_id] = order_info # Keep status cache consistent

        if order_id in self._order_event_callbacks:
            try:
                self._order_event_callbacks[order_id](order_info)
            except Exception as e:
                logger.error(f"Error in open_order callback for {order_id}: {e}")
        if order_id in self._order_events:
            self._order_events[order_id].set()


    def handle_open_order_end(self):
        logger.info("Handling open order end.")
        self._open_orders_complete_event.set()

    # --- Public API for MCP Tools ---
    async def cancel_order(self, order_id: int) -> Dict[str, Any]:
        logger.info(f"Attempting to cancel order {order_id}")
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}

        # Check if order is already known and in a terminal state
        if order_id in self._order_status_cache:
            cached_status = self._order_status_cache[order_id].status
            if cached_status == OrderStatus.CANCELLED:
                return {"status": "success", "order_id": order_id, "message": "Order already cancelled."}
            if cached_status == OrderStatus.FILLED:
                return {"status": "error", "order_id": order_id, "message": "Order already filled, cannot cancel."}
        
        event = await self._get_order_event(order_id)
        event.clear() # Clear previous event state if any

        final_status_info: Optional[OrderInfo] = None

        def status_callback(order_info: OrderInfo):
            nonlocal final_status_info
            if order_info.status in [OrderStatus.CANCELLED, OrderStatus.API_CANCELLED, OrderStatus.FILLED, OrderStatus.INACTIVE]:
                logger.info(f"Cancel operation for {order_id} received terminal status: {order_info.status.value}")
                final_status_info = order_info
                event.set()
        
        self._order_event_callbacks[order_id] = status_callback
        
        try:
            # ib_async's cancelOrder expects the Order object, not just the ID.
            # We need to find the order object first from the active orders
            existing_order_info = self._active_orders_cache.get(order_id)
            if not existing_order_info:
                # Try to refresh the cache by requesting open orders
                logger.info(f"Order {order_id} not in active cache, requesting open orders.")
                await self.get_active_orders()
                existing_order_info = self._active_orders_cache.get(order_id)
            
            if not existing_order_info or not existing_order_info.order:
                # Fallback: Use ib_async cancelOrder directly with order_id
                # Create a minimal order object with just the orderId for cancellation
                from ib_async.order import Order as IBOrder
                cancel_order = IBOrder()
                cancel_order.orderId = order_id
                self.ib.cancelOrder(cancel_order)
                logger.info(f"Cancel request sent for order {order_id} using minimal order object")
            else:
                # Use ib_async's cancelOrder with the existing order object
                self.ib.cancelOrder(existing_order_info.order)
                logger.info(f"Cancel request sent for order {order_id} using existing order object")

            await asyncio.wait_for(event.wait(), timeout=10.0)
            
            if final_status_info and final_status_info.status in [OrderStatus.CANCELLED, OrderStatus.API_CANCELLED]:
                message = f"Order {order_id} cancelled successfully."
                status = "success"
            elif final_status_info and final_status_info.status == OrderStatus.FILLED:
                message = f"Order {order_id} was filled before cancellation could complete."
                status = "error"
            else: # Timeout or other status
                message = f"Cancellation status for order {order_id} uncertain or not yet confirmed cancelled. Last known: {final_status_info.status.value if final_status_info else 'Unknown'}"
                status = "pending" # Or "error" depending on desired strictness

            return {"status": status, "order_id": order_id, "message": message, "timestamp": datetime.now().isoformat()}

        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for cancellation confirmation for order {order_id}")
            return {"status": "error", "order_id": order_id, "message": f"Timeout cancelling order {order_id}", "timestamp": datetime.now().isoformat()}
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}", exc_info=True)
            return {"status": "error", "order_id": order_id, "message": f"Error cancelling order: {str(e)}", "timestamp": datetime.now().isoformat()}
        finally:
            self._order_event_callbacks.pop(order_id, None)
            self._order_events.pop(order_id, None)

    async def modify_order(self, order_id: int, parameters: Dict[str, Any]) -> Dict[str, Any]:
        logger.info(f"Attempting to modify order {order_id} with params: {parameters}")
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}

        # Fetch the existing order details from active_orders_cache or request if not found
        existing_order_info = self._active_orders_cache.get(order_id)
        if not existing_order_info or not existing_order_info.order:
            logger.info(f"Order {order_id} not in active cache, requesting open orders.")
            await self.get_active_orders() # This will populate the cache
            existing_order_info = self._active_orders_cache.get(order_id)
        if not existing_order_info or not existing_order_info.order:
            return {'status': 'error', 'order_id': order_id, 'message': f'Order {order_id} not found or not active.', 'timestamp': datetime.now().isoformat()}
        
        contract: Contract = existing_order_info.contract or Contract()
        order: IBOrder = existing_order_info.order # This is an ib_async.Order object

        # Apply modifications
        modified_fields = []
        if 'quantity' in parameters:
            # ib_async.Order.totalQuantity expects float
            new_quantity = float(parameters['quantity'])
            order.totalQuantity = new_quantity
            modified_fields.append("quantity")
        if 'price' in parameters:
            order.lmtPrice = float(parameters['price'])
            modified_fields.append("price")
        if 'stop_price' in parameters: # For STP LMT or TRAIL orders
            order.auxPrice = float(parameters['stop_price'])
            modified_fields.append("stop_price")
        # Add more modifiable parameters as needed (e.g., order.orderType)

        if not modified_fields:
            return {'status': 'success', 'order_id': order_id, 'message': 'No parameters provided to modify.', 'timestamp': datetime.now().isoformat()}

        event = await self._get_order_event(order_id)
        event.clear()
        final_status_info: Optional[OrderInfo] = None

        def status_callback(order_info: OrderInfo):
            nonlocal final_status_info
            # Submitted or PreSubmitted usually indicates acceptance of modification
            if order_info.status in [OrderStatus.SUBMITTED, OrderStatus.PRE_SUBMITTED]:
                logger.info(f"Modify operation for {order_id} received status: {order_info.status.value}")
                final_status_info = order_info
                event.set()
            elif order_info.status in [OrderStatus.CANCELLED, OrderStatus.FILLED, OrderStatus.INACTIVE]:
                logger.warning(f"Modify operation for {order_id} resulted in terminal status: {order_info.status.value}")
                final_status_info = order_info # Capture terminal state if modification leads to it
                event.set()

        self._order_event_callbacks[order_id] = status_callback

        try:
            # ib_async.placeOrder can also modify if orderId is the same and permId matches
            # Use placeOrder (not placeOrderAsync which doesn't exist)
            trade = self.ib.placeOrder(contract, order)
            logger.info(f"Modification request sent for order {order_id}. New/Updated OrderId: {trade.order.orderId if trade else 'Unknown'}")
            
            await asyncio.wait_for(event.wait(), timeout=10.0)

            if final_status_info and final_status_info.status in [OrderStatus.SUBMITTED, OrderStatus.PRE_SUBMITTED]:
                message = f"Order {order_id} modified successfully. Modified fields: {', '.join(modified_fields)}."
                status = "success"
            else:
                message = f"Modification status for order {order_id} uncertain. Last known: {final_status_info.status.value if final_status_info else 'Unknown'}"
                status = "pending" # Or "error"

            return {"status": status, "order_id": order_id, "message": message, "modified_parameters": parameters, "timestamp": datetime.now().isoformat()}

        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for modification confirmation for order {order_id}")
            return {"status": "error", "order_id": order_id, "message": f"Timeout modifying order {order_id}", "timestamp": datetime.now().isoformat()}
        except Exception as e:
            logger.error(f"Error modifying order {order_id}: {e}", exc_info=True)
            return {"status": "error", "order_id": order_id, "message": f"Error modifying order: {str(e)}", "timestamp": datetime.now().isoformat()}
        finally:
            self._order_event_callbacks.pop(order_id, None)
            self._order_events.pop(order_id, None)

    def _sanitize_value(self, value: float) -> float:
        """Convert infinity/NaN values to reasonable defaults"""
        if value is None:
            return 0.0
        if not isinstance(value, (int, float)):
            return 0.0
        if not math.isfinite(value):  # Catches inf, -inf, and NaN
            return 0.0
        return float(value)
    
    async def get_order_status(self, order_id: int) -> Dict[str, Any]:
        logger.info(f"Getting status for order {order_id}")
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}

        # Use the class method for sanitization

        if order_id in self._order_status_cache:
            order_info = self._order_status_cache[order_id]
            logger.debug(f"Order {order_id} found in cache: {order_info.status.value}")
            return {
                'status': 'success',
                'order_id': order_id,
                'order_status': order_info.status.value,
                'filled': self._sanitize_value(order_info.filled),
                'remaining': self._sanitize_value(order_info.remaining),
                'avg_fill_price': self._sanitize_value(order_info.avg_fill_price),
                'last_fill_price': self._sanitize_value(order_info.last_fill_price),
                'why_held': order_info.why_held,
                'timestamp': order_info.timestamp.isoformat()
            }

        logger.info(f"Order {order_id} not in cache, requesting open orders to refresh.")
        
        # Create or get an event for this order_id
        if order_id not in self._order_events:
            self._order_events[order_id] = asyncio.Event()
        event = self._order_events[order_id]
        event.clear()
        
        # We need to request open orders to refresh our cache
        # This will trigger orderStatus callbacks which will update our cache
        # If the order exists, we should receive an update
        self.ib.reqAllOpenOrders()  # This will trigger openOrder and orderStatus events
        
        try:
            await asyncio.wait_for(event.wait(), timeout=5.0)  # Wait for *any* update related to this orderId
            if order_id in self._order_status_cache:
                order_info = self._order_status_cache[order_id]
                return {
                    'status': 'success',
                    'order_id': order_id,
                    'order_status': order_info.status.value,
                    'filled': self._sanitize_value(order_info.filled),
                    'remaining': self._sanitize_value(order_info.remaining),
                    'avg_fill_price': self._sanitize_value(order_info.avg_fill_price),
                    'last_fill_price': self._sanitize_value(order_info.last_fill_price),
                    'why_held': order_info.why_held,
                    'timestamp': order_info.timestamp.isoformat()
                }
            else:  # Event triggered but order not in cache (shouldn't happen if event is for this order_id)
                raise asyncio.TimeoutError("Order status update not received in cache.")
        except asyncio.TimeoutError:
            logger.warning(f"Timeout or order {order_id} not found after requesting open orders.")
            return {'status': 'error', 'order_id': order_id, 'message': 'Order not found or timeout getting status', 'timestamp': datetime.now().isoformat()}
        except Exception as e:
            logger.error(f"Error in get_order_status for {order_id}: {e}", exc_info=True)
            return {'status': 'error', 'order_id': order_id, 'message': f'Error getting order status: {str(e)}', 'timestamp': datetime.now().isoformat()}
        finally:
            # Only remove the event if we're completely done with it
            self._order_events.pop(order_id, None)

    async def get_active_orders(self) -> Dict[str, Any]:
        logger.info("Getting all active orders.")
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}

        self._active_orders_cache.clear() # Clear before refresh
        # _open_orders_complete_event is not reliably set if openOrderEndEvent is unavailable.
        # self._open_orders_complete_event.clear() 
        # Implementation based on ibpy_native (https://github.com/Devtography/ibpy_native)
        # Copyright (c) Devtography
        # Licensed under the Apache License, Version 2.0
        # See: https://www.apache.org/licenses/LICENSE-2.0
        try:
            logger.info("Calling self.ib.reqAllOpenOrdersAsync() to fetch active orders.")
            # reqAllOpenOrdersAsync() returns an awaitable List[Trade].
            # ib_async handles the underlying openOrderEnd from TWS.
            # During this call, openOrderEvent handlers populate _active_orders_cache.
            await self.ib.reqAllOpenOrdersAsync()
            logger.info("self.ib.reqAllOpenOrdersAsync() completed. Processing cached orders.")
            
            # At this point, _active_orders_cache should be populated by _on_open_order via handle_open_order.
        
            active_orders_list = []
            for order_id, order_info_obj in list(self._active_orders_cache.items()): # Use list() for safe iteration
                contract = order_info_obj.contract
                ib_order = order_info_obj.order # This is an ib_async.Order object
                
                active_orders_list.append({
                    'order_id': order_id,
                    'symbol': contract.symbol if contract else "N/A",
                    'exchange': contract.exchange if contract else "N/A",
                    'action': ib_order.action if ib_order else "N/A",
                    'quantity': float(ib_order.totalQuantity) if ib_order else 0.0,
                    'order_type': ib_order.orderType if ib_order else "N/A",
                    'limit_price': ib_order.lmtPrice if ib_order and ib_order.lmtPrice != float('inf') else None,
                    'stop_price': ib_order.auxPrice if ib_order and ib_order.auxPrice != float('inf') else None,
                    'status': order_info_obj.status.value,
                    'time_in_force': ib_order.tif if ib_order else "N/A",
                    'filled': order_info_obj.filled,
                    'remaining': order_info_obj.remaining,
                    'avg_fill_price': order_info_obj.avg_fill_price,
                    'timestamp': order_info_obj.timestamp.isoformat()
                })
            
            return {'status': 'success', 'orders': active_orders_list, 'count': len(active_orders_list), 'timestamp': datetime.now().isoformat()}
        except Exception as e:
            logger.error(f"Error getting active orders: {e}", exc_info=True)
            return {'status': 'error', 'message': f'Error getting active orders: {str(e)}', 'timestamp': datetime.now().isoformat()}
    
    async def place_order(self, order_type: str, symbol: str, action: str, 
                         quantity: Union[int, float, Decimal], account: str, 
                         price: Optional[float] = None, **kwargs) -> Dict[str, Any]:
        """
        Place a new order with comprehensive error handling and event management
        
        Args:
            order_type: Type of order ("market", "limit", "stop", "bracket", "adaptive", "auction")
            symbol: The ticker symbol
            action: "BUY" or "SELL"
            quantity: Number of shares/contracts
            account: The account ID to place the order in
            price: Price for limit orders
            **kwargs: Additional order parameters (stop_price, profit_price, time_in_force, etc.)
            
        Returns:
            dict: Order placement result with order_id and status
        """
        logger.info(f"Placing {order_type} order for {symbol}: {action} {quantity} @ {price}")
        
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}
        
        try:
            # Delegate to IBKRService's create_order method which handles all order types
            result = await self.ibkr_service.create_order(
                order_type=order_type,
                symbol=symbol, 
                action=action,
                quantity=quantity,
                account=account,
                price=price,
                **kwargs
            )
            
            if result.get("status") == "success":
                order_id = result.get("orderId")
                if order_id:
                    # Set up event handling for the new order
                    if order_id not in self._order_events:
                        self._order_events[order_id] = asyncio.Event()
                    
                    # Wait briefly for initial order status update
                    try:
                        await asyncio.wait_for(self._order_events[order_id].wait(), timeout=2.0)
                        
                        # Get updated order info from cache
                        if order_id in self._order_status_cache:
                            order_info = self._order_status_cache[order_id]
                            result.update({
                                "order_status": order_info.status.value,
                                "filled": self._sanitize_value(order_info.filled),
                                "remaining": self._sanitize_value(order_info.remaining),
                                "timestamp": order_info.timestamp.isoformat()
                            })
                    except asyncio.TimeoutError:
                        logger.warning(f"Initial status update timeout for order {order_id}")
                        # Continue with original result - this is not a failure
                        
                    logger.info(f"Order {order_id} placed successfully")
                    
            return result
            
        except Exception as e:
            logger.error(f"Error placing {order_type} order for {symbol}: {e}", exc_info=True)
            return {
                "status": "error", 
                "message": f"Error placing order: {str(e)}", 
                "timestamp": datetime.now().isoformat()
            }

    async def get_order_history(self, symbol: Optional[str] = None, 
                               account: Optional[str] = None,
                               start_date: Optional[str] = None,
                               end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        Get order execution history (filled orders) with optional filtering
        
        Args:
            symbol: Filter by specific symbol (optional)
            account: Filter by specific account (optional) 
            start_date: Start date filter in 'YYYYMMDD-HH:MM:SS' format (optional)
            end_date: End date filter in 'YYYYMMDD-HH:MM:SS' format (optional)
            
        Returns:
            dict: Execution history with fills and commission data
        """
        logger.info(f"Getting order history - symbol: {symbol}, account: {account}")
        
        if not self.ibkr_service.connected:
            return {"status": "error", "message": "Not connected to TWS."}
        
        try:
            # Create execution filter based on parameters
            from ib_async.objects import ExecutionFilter
            
            exec_filter = ExecutionFilter()
            if symbol:
                exec_filter.symbol = symbol
            if account:
                exec_filter.acctCode = account
            if start_date:
                exec_filter.time = start_date
            # Note: ExecutionFilter doesn't have end_date, we'll filter client-side if needed
            
            logger.debug(f"Requesting executions with filter: symbol={exec_filter.symbol}, account={exec_filter.acctCode}, time={exec_filter.time}")
            
            # Use ib_async's reqExecutionsAsync to get fills
            fills = await self.ib.reqExecutionsAsync(exec_filter)
            
            logger.info(f"Retrieved {len(fills)} execution records")
            
            # Process fills into standardized format
            executions = []
            for fill in fills:
                execution = fill.execution
                contract = fill.contract
                commission_report = fill.commissionReport
                
                # Apply client-side date filtering if end_date specified
                if end_date:
                    exec_time_str = execution.time
                    # Convert execution time to comparable format
                    try:
                        # Execution time format is typically YYYYMMDD HH:MM:SS
                        exec_time_formatted = str(exec_time_str).replace('-', ' ')
                        end_date_formatted = str(end_date).replace('-', ' ')
                        exec_datetime = datetime.strptime(exec_time_formatted, '%Y%m%d %H:%M:%S')
                        end_datetime = datetime.strptime(end_date_formatted, '%Y%m%d %H:%M:%S')
                        
                        if exec_datetime > end_datetime:
                            continue  # Skip this execution
                    except ValueError:
                        logger.warning(f"Could not parse execution time for filtering: {exec_time_str}")
                        # Include the execution if we can't parse the time
                
                execution_info = {
                    "execution_id": execution.execId,
                    "order_id": execution.orderId,
                    "symbol": contract.symbol if contract else "N/A",
                    "exchange": execution.exchange,
                    "side": execution.side,
                    "shares": float(execution.shares) if execution.shares else 0.0,
                    "price": execution.price,
                    "execution_time": execution.time,
                    "account": execution.acctNumber,
                    "perm_id": execution.permId,
                    "client_id": execution.clientId,
                    "liquidation": bool(execution.liquidation),
                    "cumulative_quantity": float(execution.cumQty) if execution.cumQty else 0.0,
                    "average_price": execution.avgPrice,
                    "order_ref": execution.orderRef,
                    "ev_rule": execution.evRule,
                    "ev_multiplier": execution.evMultiplier,
                    "model_code": execution.modelCode,
                    "last_liquidity": execution.lastLiquidity,
                    "pending_price_revision": execution.pendingPriceRevision
                }
                
                # Add commission information if available
                if commission_report:
                    execution_info.update({
                        "commission": commission_report.commission,
                        "commission_currency": commission_report.currency,
                        "realized_pnl": commission_report.realizedPNL,
                        "yield": commission_report.yield_,
                        "yield_redemption_date": commission_report.yieldRedemptionDate
                    })
                else:
                    execution_info.update({
                        "commission": 0.0,
                        "commission_currency": "",
                        "realized_pnl": 0.0,
                        "yield": 0.0,
                        "yield_redemption_date": 0
                    })
                
                # Add contract details
                if contract:
                    execution_info.update({
                        "contract_id": contract.conId,
                        "security_type": contract.secType,
                        "primary_exchange": contract.primaryExchange,
                        "currency": contract.currency,
                        "local_symbol": contract.localSymbol,
                        "trading_class": contract.tradingClass
                    })
                
                executions.append(execution_info)
            
            # Sort executions by time (most recent first)
            executions.sort(key=lambda x: x["execution_time"], reverse=True)
            
            # Calculate summary statistics
            total_executions = len(executions)
            total_shares = sum(exec_info["shares"] for exec_info in executions)
            total_commission = sum(exec_info["commission"] for exec_info in executions)
            total_realized_pnl = sum(exec_info["realized_pnl"] for exec_info in executions)
            
            return {
                "status": "success",
                "executions": executions,
                "summary": {
                    "total_executions": total_executions,
                    "total_shares": total_shares,
                    "total_commission": total_commission,
                    "total_realized_pnl": total_realized_pnl,
                    "filtered_by": {
                        "symbol": symbol,
                        "account": account,
                        "start_date": start_date,
                        "end_date": end_date
                    }
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting order history: {e}", exc_info=True)
            return {
                "status": "error", 
                "message": f"Error getting order history: {str(e)}", 
                "timestamp": datetime.now().isoformat()
            }
