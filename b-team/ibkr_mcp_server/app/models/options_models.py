"""
Options Models

Data models for options trading, Greeks calculations, and strategy definitions.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
from datetime import datetime, date
from enum import Enum
import numpy as np


class OptionType(Enum):
    """Option types"""
    CALL = "CALL"
    PUT = "PUT"


class OptionStrategy(Enum):
    """Supported options strategies"""
    COVERED_CALL = "covered_call"
    PROTECTIVE_PUT = "protective_put"
    CASH_SECURED_PUT = "cash_secured_put"
    BULL_CALL_SPREAD = "bull_call_spread"
    BEAR_PUT_SPREAD = "bear_put_spread"
    IRON_CONDOR = "iron_condor"
    IRON_BUTTERFLY = "iron_butterfly"
    STRADDLE = "straddle"
    STRANGLE = "strangle"
    COLLAR = "collar"
    BUTTERFLY_SPREAD = "butterfly_spread"
    CALENDAR_SPREAD = "calendar_spread"
    DIAGONAL_SPREAD = "diagonal_spread"
    RATIO_SPREAD = "ratio_spread"
    JADE_LIZARD = "jade_lizard"
    REVERSE_IRON_CONDOR = "reverse_iron_condor"


class Greeks(Enum):
    """The Greeks for options pricing"""
    DELTA = "delta"
    GAMMA = "gamma"
    THETA = "theta"
    VEGA = "vega"
    RHO = "rho"


@dataclass
class OptionContract:
    """Represents a single option contract"""
    symbol: str
    underlying: str
    option_type: OptionType
    strike: Decimal
    expiration: date
    multiplier: int = 100
    exchange: str = "SMART"
    currency: str = "USD"
    
    # Market data
    bid: Optional[Decimal] = None
    ask: Optional[Decimal] = None
    last: Optional[Decimal] = None
    volume: Optional[int] = None
    open_interest: Optional[int] = None
    implied_volatility: Optional[float] = None
    
    # Greeks
    delta: Optional[float] = None
    gamma: Optional[float] = None
    theta: Optional[float] = None
    vega: Optional[float] = None
    rho: Optional[float] = None
    
    @property
    def mid_price(self) -> Optional[Decimal]:
        """Calculate mid price from bid/ask"""
        if self.bid is not None and self.ask is not None:
            return (self.bid + self.ask) / 2
        return None
    
    @property
    def intrinsic_value(self) -> Decimal:
        """Calculate intrinsic value"""
        if not hasattr(self, '_underlying_price') or self._underlying_price is None:
            return Decimal('0')
        
        # We've already checked that _underlying_price is not None
        underlying_price = self._underlying_price  # This is now known to be a Decimal
        
        if self.option_type == OptionType.CALL:
            return max(Decimal('0'), underlying_price - self.strike)
        else:  # PUT
            return max(Decimal('0'), self.strike - underlying_price)
    
    def set_underlying_price(self, price: Decimal):
        """Set underlying price for calculations"""
        self._underlying_price = price


@dataclass
class OptionLeg:
    """Represents one leg of an options strategy"""
    contract: OptionContract
    action: str  # "BUY" or "SELL"
    quantity: int
    price: Optional[Decimal] = None
    
    @property
    def notional_value(self) -> Decimal:
        """Calculate notional value of the leg"""
        price = self.price or self.contract.mid_price or Decimal('0')
        return abs(price * self.quantity * self.contract.multiplier)
    
    @property
    def delta_exposure(self) -> float:
        """Calculate delta exposure for this leg"""
        if self.contract.delta is None:
            return 0.0
        
        multiplier = 1 if self.action == "BUY" else -1
        return self.contract.delta * self.quantity * multiplier
    
    @property
    def gamma_exposure(self) -> float:
        """Calculate gamma exposure for this leg"""
        if self.contract.gamma is None:
            return 0.0
        
        multiplier = 1 if self.action == "BUY" else -1
        return self.contract.gamma * self.quantity * multiplier


@dataclass
class OptionsStrategy:
    """Complete options strategy definition"""
    name: str
    strategy_type: OptionStrategy
    legs: List[OptionLeg]
    underlying_symbol: str
    created_at: datetime = field(default_factory=datetime.now)
    
    # Strategy metrics
    max_profit: Optional[Decimal] = None
    max_loss: Optional[Decimal] = None
    breakeven_points: List[Decimal] = field(default_factory=list)
    probability_of_profit: Optional[float] = None
    
    # Risk metrics
    net_delta: Optional[float] = None
    net_gamma: Optional[float] = None
    net_theta: Optional[float] = None
    net_vega: Optional[float] = None
    net_premium: Optional[Decimal] = None
    
    def calculate_net_greeks(self) -> Dict[str, float]:
        """Calculate net Greeks across all legs"""
        greeks = {
            'delta': sum(leg.delta_exposure for leg in self.legs),
            'gamma': sum(leg.gamma_exposure for leg in self.legs),
            'theta': sum(leg.contract.theta or 0 for leg in self.legs),
            'vega': sum(leg.contract.vega or 0 for leg in self.legs),
            'rho': sum(leg.contract.rho or 0 for leg in self.legs)
        }
        
        # Update instance variables
        self.net_delta = greeks['delta']
        self.net_gamma = greeks['gamma']
        self.net_theta = greeks['theta']
        self.net_vega = greeks['vega']
        
        return greeks
    
    def calculate_net_premium(self) -> Decimal:
        """Calculate net premium paid/received"""
        net_premium = Decimal('0')
        
        for leg in self.legs:
            price = leg.price or leg.contract.mid_price or Decimal('0')
            multiplier = 1 if leg.action == "SELL" else -1  # Selling receives premium
            net_premium += price * leg.quantity * leg.contract.multiplier * multiplier
        
        self.net_premium = net_premium
        return net_premium
    
    @property
    def is_net_credit(self) -> bool:
        """Check if strategy is net credit (receives premium)"""
        return (self.net_premium or Decimal('0')) > 0
    
    @property
    def is_delta_neutral(self, threshold: float = 0.1) -> bool:
        """Check if strategy is approximately delta neutral"""
        return abs(self.net_delta or 0) <= threshold


@dataclass
class OptionsPosition:
    """Represents an active options position"""
    strategy: OptionsStrategy
    account: str
    position_id: str
    entry_time: datetime = field(default_factory=datetime.now)
    
    # Position tracking
    filled_legs: List[int] = field(default_factory=list)  # Indices of filled legs
    partial_fills: Dict[int, int] = field(default_factory=dict)  # leg_index: filled_quantity
    
    # P&L tracking
    unrealized_pnl: Optional[Decimal] = None
    realized_pnl: Optional[Decimal] = None
    
    @property
    def is_fully_filled(self) -> bool:
        """Check if all legs are fully filled"""
        return len(self.filled_legs) == len(self.strategy.legs)
    
    @property
    def fill_ratio(self) -> float:
        """Calculate what percentage of the strategy is filled"""
        if not self.strategy.legs:
            return 0.0
        
        total_legs = len(self.strategy.legs)
        filled_count = len(self.filled_legs)
        
        # Account for partial fills
        partial_count = sum(
            min(1.0, qty / self.strategy.legs[idx].quantity) 
            for idx, qty in self.partial_fills.items() 
            if idx not in self.filled_legs and idx < len(self.strategy.legs)
        )
        
        return (filled_count + partial_count) / total_legs


@dataclass
class VolatilitySurface:
    """Volatility surface data for options pricing"""
    underlying: str
    timestamp: datetime
    strikes: List[Decimal]
    expirations: List[date]
    implied_volatilities: Dict[tuple, float]  # (strike, expiration) -> IV
    
    def get_iv(self, strike: Decimal, expiration: date) -> Optional[float]:
        """Get implied volatility for specific strike/expiration"""
        return self.implied_volatilities.get((strike, expiration))
    
    def interpolate_iv(self, strike: Decimal, expiration: date) -> Optional[float]:
        """Interpolate IV for strikes/expirations not exactly in surface"""
        # Simple linear interpolation - in production would use more sophisticated methods
        if (strike, expiration) in self.implied_volatilities:
            return self.implied_volatilities[(strike, expiration)]
        
        # Find nearest strikes and dates for interpolation
        # This is a simplified version - real implementation would be more robust
        return None


@dataclass
class OptionsChain:
    """Complete options chain for an underlying"""
    underlying: str
    expiration: date
    calls: List[OptionContract]
    puts: List[OptionContract]
    underlying_price: Optional[Decimal] = None
    
    def get_strikes(self) -> List[Decimal]:
        """Get all available strikes"""
        call_strikes = [c.strike for c in self.calls]
        put_strikes = [p.strike for p in self.puts]
        return sorted(list(set(call_strikes + put_strikes)))
    
    def get_call(self, strike: Decimal) -> Optional[OptionContract]:
        """Get call option for specific strike"""
        for call in self.calls:
            if call.strike == strike:
                return call
        return None
    
    def get_put(self, strike: Decimal) -> Optional[OptionContract]:
        """Get put option for specific strike"""
        for put in self.puts:
            if put.strike == strike:
                return put
        return None
    
    def get_atm_strike(self) -> Optional[Decimal]:
        """Get at-the-money strike"""
        if self.underlying_price is None:
            return None
        
        strikes = self.get_strikes()
        if not strikes:
            return None
        
        # We've already checked that underlying_price is not None
        underlying_price = self.underlying_price  # This is now known to be a Decimal
        
        # Find closest strike to underlying price
        return min(strikes, key=lambda x: abs(x - underlying_price))


@dataclass
class OptionsAnalytics:
    """Advanced analytics for options positions and strategies"""
    position: OptionsPosition
    
    # Risk metrics
    var_95: Optional[Decimal] = None  # 95% Value at Risk
    expected_move: Optional[Decimal] = None
    probability_of_profit: Optional[float] = None
    
    # Time decay analysis
    theta_decay_1d: Optional[Decimal] = None
    theta_decay_7d: Optional[Decimal] = None
    
    # Volatility analysis
    iv_rank: Optional[float] = None  # Implied Volatility Rank
    iv_percentile: Optional[float] = None
    
    # Greeks sensitivity
    delta_sensitivity: Optional[float] = None
    gamma_risk: Optional[float] = None
    vega_risk: Optional[float] = None


# Strategy Templates for Common Patterns
STRATEGY_TEMPLATES = {
    OptionStrategy.COVERED_CALL: {
        "description": "Own stock, sell call above current price",
        "legs": [
            {"action": "OWN", "type": "STOCK", "quantity": 100},
            {"action": "SELL", "type": "CALL", "quantity": 1}
        ],
        "market_outlook": "Neutral to slightly bullish",
        "max_profit": "Strike - Stock Cost + Premium",
        "max_loss": "Stock Cost - Premium",
        "breakeven": "Stock Cost - Premium"
    },
    
    OptionStrategy.IRON_CONDOR: {
        "description": "Sell OTM put spread and call spread",
        "legs": [
            {"action": "BUY", "type": "PUT", "strike": "far_otm"},
            {"action": "SELL", "type": "PUT", "strike": "near_otm"},
            {"action": "SELL", "type": "CALL", "strike": "near_otm"},
            {"action": "BUY", "type": "CALL", "strike": "far_otm"}
        ],
        "market_outlook": "Neutral - expect low volatility",
        "max_profit": "Net Premium Received",
        "max_loss": "Strike Width - Net Premium",
        "optimal_iv": "High (sell when IV is elevated)"
    },
    
    OptionStrategy.STRADDLE: {
        "description": "Buy call and put at same strike",
        "legs": [
            {"action": "BUY", "type": "CALL", "strike": "atm"},
            {"action": "BUY", "type": "PUT", "strike": "atm"}
        ],
        "market_outlook": "High volatility expected",
        "max_profit": "Unlimited",
        "max_loss": "Total Premium Paid",
        "breakeven": ["Strike + Premium", "Strike - Premium"]
    }
}
