"""
Interactive Brokers MCP Server

This server implements the Model Context Protocol (MCP) to provide
Claude Desktop with access to Interactive Brokers' TWS API through
the existing IBKRService.

Clean implementation following 3-layer architecture:
Layer 1: MCP Interface (this file)
Layer 2: Domain Manager 
Layer 3: Implementation
"""
from mcp.server import FastMCP
import logging
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

# Import the existing service
from ibkr_mcp_server.app.services.ibkr_service import ibkr_service

# Import the order management service
from ibkr_mcp_server.app.services.order_management_service import OrderManagementService # Import the class

# Import algorithmic trading services
from ibkr_mcp_server.app.services.algo import (
    AlgorithmicTradingService,
    MarketDataStreamingService,
    PortfolioRebalancingService,
    AdvancedOrderRoutingService,
    StrategyExecutionService
)

# Import domain tools v2 (Layer 1: MCP Interface) - COMMENTED OUT DUE TO IMPORT ISSUES
# These imports are causing module not found errors, will implement simplified version
# from ibkr_mcp_server.app.ibkr_mcp.tools.historicaldata_tools_v2 import HistoricalDataToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.tickbytick_tools_v2 import TickByTickToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.marketdepth_tools_v2 import MarketDepthToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.realtimebars_tools_v2 import RealtimeBarsToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.news_tools_v2 import NewsToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.scanner_tools_v2 import ScannerToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.optionstrading_tools_v2 import OptionsTradingToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.riskmanagement_tools_v2 import RiskManagementToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.technicalanalysis_tools_v2 import TechnicalAnalysisToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.volatility_surface_tools_v2 import VolatilitySurfaceToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.algorithmictrading_tools_v2 import AlgorithmicTradingToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.ordermanagement_tools_v2 import OrderManagementToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.account_tools_v2 import AccountToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.contracts_tools_v2 import ContractsToolsV2
# from ibkr_mcp_server.app.ibkr_mcp.tools.backtesting_tools_v2 import BacktestingToolsV2

# Configure logging with standardized timestamp format
# Logging is configured by setup_logging() later in the file
logger = logging.getLogger('ibkr-mcp')

# Server lifespan management
@asynccontextmanager
async def lifespan(server):
    """
    Manage the server lifespan with EARLY service registration
    """
    global oms_instance, algo_service, market_data_service, rebalancing_service, routing_service, strategy_service
    global historical_data_tools_v2, tickbytick_tools_v2, marketdepth_tools_v2, realtimebars_tools_v2, news_tools_v2, scanner_tools_v2, optionstrading_tools_v2, riskmanagement_tools_v2, technicalanalysis_tools_v2, volatility_surface_tools_v2, algorithmictrading_tools_v2, ordermanagement_tools_v2, account_tools_v2, contracts_tools_v2, backtesting_tools_v2
    
    # Startup
    logger.info("IBKR MCP Server starting up with EARLY service registration")
    
    # CRITICAL FIX: Register services IMMEDIATELY
    from ibkr_mcp_server.app.ibkr_mcp.infrastructure.domain_registry import registry
    from ibkr_mcp_server.app.services.ibkr_service import ibkr_service
    
    # Register ibkr_service immediately
    early_services = {'ibkr_service': ibkr_service}
    registry.register_services(early_services)
    logger.info("EARLY SERVICE REGISTRATION: ibkr_service registered")

    # Initialize the service if needed
    try:
        # Initialize the service
        await ibkr_service.initialize()
        logger.info("IBKR Service initialized")

        # Initialize OrderManagementService and link it to IBKRService
        oms_instance = OrderManagementService(ibkr_svc=ibkr_service)
        ibkr_service.set_order_management_service(oms_delegate=oms_instance)
        logger.info("OrderManagementService initialized and linked to IBKRService")

        # Initialize algorithmic trading services
        algo_service = AlgorithmicTradingService(ibkr_service, oms_instance)
        market_data_service = MarketDataStreamingService(ibkr_service)
        rebalancing_service = PortfolioRebalancingService(ibkr_service, oms_instance, algo_service)
        routing_service = AdvancedOrderRoutingService(ibkr_service, oms_instance)
        strategy_service = StrategyExecutionService(ibkr_service, oms_instance, algo_service, market_data_service)
        logger.info("Algorithmic trading services initialized")
        
        logger.info("Algorithmic trading tools will be initialized with proper services")

        # Initialize domain tools v2 (Layer 1: MCP Interface) with service injection
        # CRITICAL FIX: Inject services directly into domain implementations
        from ibkr_mcp_server.app.ibkr_mcp.infrastructure.domain_registry import registry

        # Register services in the registry
        services = {
            'ibkr_service': ibkr_service,
            'order_management_service': oms_instance,
            'algorithmic_trading_service': algo_service,
            'market_data_service': market_data_service,
            'rebalancing_service': rebalancing_service,
            'routing_service': routing_service,
            'strategy_service': strategy_service
        }
        registry.register_services(services)
        logger.info(f"Registered {len(services)} services in domain registry")

        # CRITICAL FIX: Initialize domain tools with proper service injection
        # We need to inject services BEFORE domain instantiation
        logger.info("Initializing domain tools with service injection...")

        # Create a factory function that injects services into domains
        def create_domain_with_services(domain_class):
            """Create domain instance with services properly injected"""
            # Get the domain's implementation classes
            temp_domain = domain_class()
            implementations = temp_domain.config.implementations

            # Create new config with services
            from ibkr_mcp_server.app.ibkr_mcp.infrastructure.base_domain import DomainConfig
            config_with_services = DomainConfig(
                name=temp_domain.config.name,
                implementations=implementations,
                services=services
            )

            # Create new domain with services
            return domain_class.__new__(domain_class)

        # Initialize domain tools with service injection
        historical_data_tools_v2 = HistoricalDataToolsV2()
        tickbytick_tools_v2 = TickByTickToolsV2()
        marketdepth_tools_v2 = MarketDepthToolsV2()
        realtimebars_tools_v2 = RealtimeBarsToolsV2()
        news_tools_v2 = NewsToolsV2()
        scanner_tools_v2 = ScannerToolsV2()
        optionstrading_tools_v2 = OptionsTradingToolsV2()
        riskmanagement_tools_v2 = RiskManagementToolsV2()
        technicalanalysis_tools_v2 = TechnicalAnalysisToolsV2()
        volatility_surface_tools_v2 = VolatilitySurfaceToolsV2()
        algorithmictrading_tools_v2 = AlgorithmicTradingToolsV2()
        ordermanagement_tools_v2 = OrderManagementToolsV2()
        account_tools_v2 = AccountToolsV2()
        contracts_tools_v2 = ContractsToolsV2()
        backtesting_tools_v2 = BacktestingToolsV2()

        # CRITICAL FIX: Manually inject services into domain implementations
        # This bypasses the broken registry system for immediate functionality
        _inject_services_into_domains(services)

        logger.info("Domain tools v2 initialized with service injection")

    except Exception as e:
        logger.error(f"Failed to initialize IBKR MCP Server: {e}")
        raise

    yield

    # Shutdown
    logger.info("IBKR MCP Server shutting down")
    try:
        if ibkr_service:
            await ibkr_service.disconnect()
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

def _inject_services_into_domains(services: Dict[str, Any]):
    """
    CRITICAL FIX: Manually inject services into domain implementations
    This bypasses the broken registry system for immediate functionality
    """
    logger.info("Manually injecting services into domain implementations...")

    # Get all domain tool instances
    domain_tools = [
        historical_data_tools_v2, tickbytick_tools_v2, marketdepth_tools_v2,
        realtimebars_tools_v2, news_tools_v2, scanner_tools_v2,
        optionstrading_tools_v2, riskmanagement_tools_v2, technicalanalysis_tools_v2,
        volatility_surface_tools_v2, algorithmictrading_tools_v2, ordermanagement_tools_v2,
        account_tools_v2, contracts_tools_v2, backtesting_tools_v2
    ]

    # Inject services into each domain's implementations
    for tool in domain_tools:
        if hasattr(tool, 'domain') and hasattr(tool.domain, 'implementations'):
            for impl_name, impl_instance in tool.domain.implementations.items():
                # Inject ibkr_service if the implementation expects it
                if hasattr(impl_instance, 'ibkr_service'):
                    impl_instance.ibkr_service = services.get('ibkr_service')
                    logger.info(f"Injected ibkr_service into {tool.domain.config.name}.{impl_name}")

                # Inject other services as needed
                for service_name, service_instance in services.items():
                    if hasattr(impl_instance, service_name):
                        setattr(impl_instance, service_name, service_instance)
                        logger.debug(f"Injected {service_name} into {tool.domain.config.name}.{impl_name}")

        # Also inject services directly into the domain's services dict
        if hasattr(tool, 'domain') and hasattr(tool.domain, 'services'):
            tool.domain.services.update(services)
            logger.debug(f"Updated services dict for {tool.domain.config.name}")

    logger.info("Service injection completed")

# Initialize the FastMCP server with lifespan
mcp = FastMCP("IBKR Trading Server", lifespan=lifespan)

# ============================================================================
# LAYER 1: MCP WRAPPER FUNCTIONS (Clean 3-Layer Architecture Delegation)
# ============================================================================

# HistoricalData Domain MCP Wrappers
@mcp.tool()
async def get_historical_data(
    symbol: str,
    duration: str = "1 Y",
    bar_size: str = "1 day",
    what_to_show: str = "TRADES",
    use_rth: bool = True,
    format_date: int = 1,
    exchange: str = "SMART"
) -> Dict:
    """
    Get historical market data for a symbol
    
    Args:
        symbol: The ticker symbol (e.g., 'AAPL', 'TSLA')
        duration: How far back to retrieve data (e.g., '1 Y', '6 M', '30 D')
        bar_size: The bar size (e.g., '1 day', '1 hour', '5 mins')
        what_to_show: Type of data ('TRADES', 'MIDPOINT', 'BID', 'ASK')
        use_rth: Whether to use regular trading hours only
        format_date: Date format (1 for yyyyMMdd HH:mm:ss, 2 for epoch time)
        exchange: Exchange to get data from
        
    Returns:
        Dict containing historical data with OHLCV information
    """
    return await historical_data_tools_v2.get_historical_data(
        symbol=symbol,
        timeframe=bar_size,
        duration=duration,
        end_date=None,
        data_type=what_to_show,
        use_rth=use_rth
    )

@mcp.tool()
async def get_historical_data_multiple_timeframes(
    symbol: str,
    timeframes: List[str],
    duration: str = "1 Y",
    what_to_show: str = "TRADES",
    use_rth: bool = True,
    exchange: str = "SMART"
) -> Dict:
    """
    Get historical data for multiple timeframes simultaneously
    
    Args:
        symbol: The ticker symbol
        timeframes: List of bar sizes (e.g., ['1 day', '1 hour', '5 mins'])
        duration: How far back to retrieve data
        what_to_show: Type of data to retrieve
        use_rth: Whether to use regular trading hours only
        exchange: Exchange to get data from
        
    Returns:
        Dict containing data for all requested timeframes
    """
    return await historical_data_tools_v2.get_historical_data_multiple_timeframes(
        symbol=symbol,
        timeframes=timeframes,
        duration=duration,
        what_to_show=what_to_show,
        use_rth=use_rth
    )

@mcp.tool()
async def get_historical_data_with_analysis(
    symbol: str,
    duration: str = "1 Y",
    bar_size: str = "1 day",
    include_volume_analysis: bool = True,
    include_price_levels: bool = True,
    exchange: str = "SMART"
) -> Dict:
    """
    Get historical data with built-in technical analysis
    
    Args:
        symbol: The ticker symbol
        duration: How far back to retrieve data
        bar_size: The bar size
        include_volume_analysis: Whether to include volume analysis
        include_price_levels: Whether to calculate support/resistance levels
        exchange: Exchange to get data from
        
    Returns:
        Dict containing historical data plus analysis
    """
    return await historical_data_tools_v2.get_historical_data_with_analysis(
        symbol=symbol,
        duration=duration,
        bar_size=bar_size,
        include_volume_analysis=include_volume_analysis,
        include_price_levels=include_price_levels
    )

@mcp.tool()
async def export_historical_data(
    symbol: str,
    duration: str = "1 Y",
    bar_size: str = "1 day",
    format: str = "csv",
    include_analysis: bool = False,
    exchange: str = "SMART"
) -> Dict:
    """
    Export historical data to file
    
    Args:
        symbol: The ticker symbol
        duration: How far back to retrieve data
        bar_size: The bar size
        format: Export format ('csv', 'json', 'excel')
        include_analysis: Whether to include technical analysis
        exchange: Exchange to get data from
        
    Returns:
        Dict containing export status and file path
    """
    return await historical_data_tools_v2.export_historical_data(
        symbol=symbol,
        duration=duration,
        bar_size=bar_size,
        format=format,
        include_analysis=include_analysis
    )

@mcp.tool()
async def get_historical_volatility(
    symbol: str,
    duration: str = "3 M",
    bar_size: str = "1 day",
    calculation_method: str = "close_to_close",
    exchange: str = "SMART"
) -> Dict:
    """
    Calculate historical volatility for a symbol
    
    Args:
        symbol: The ticker symbol
        duration: How far back to retrieve data
        bar_size: The bar size
        calculation_method: Method for volatility calculation
        exchange: Exchange to get data from
        
    Returns:
        Dict containing volatility analysis
    """
    return await historical_data_tools_v2.get_historical_volatility(
        symbol=symbol,
        duration=duration,
        bar_size=bar_size,
        calculation_method=calculation_method
    )

# TickByTick Domain MCP Wrappers
@mcp.tool()
async def start_tick_by_tick_data(
    symbol: str,
    tick_type: str = "AllLast",
    number_of_ticks: int = 0,
    ignore_size: bool = False,
    exchange: str = "SMART"
) -> Dict:
    """
    Start receiving tick-by-tick data for a symbol
    
    Args:
        symbol: The ticker symbol
        tick_type: Type of tick data ('AllLast', 'Last', 'BidAsk', 'MidPoint')
        number_of_ticks: Maximum number of ticks (0 for unlimited)
        ignore_size: Whether to ignore size information
        exchange: Exchange to get data from
        
    Returns:
        Dict containing subscription status
    """
    return await tickbytick_tools_v2.start_tick_by_tick_data(
        symbol=symbol,
        tick_type=tick_type,
        number_of_ticks=number_of_ticks,
        ignore_size=ignore_size
    )

@mcp.tool()
async def stop_tick_by_tick_data(symbol: str) -> Dict:
    """
    Stop receiving tick-by-tick data for a symbol
    
    Args:
        symbol: The ticker symbol
        
    Returns:
        Dict containing unsubscription status
    """
    return await tickbytick_tools_v2.stop_tick_by_tick_data(
        symbol=symbol,
        subscription_id=f"tick_{symbol}"
    )

@mcp.tool()
async def get_recent_tick_data(
    symbol: str,
    count: int = 100
) -> Dict:
    """
    Get recent tick-by-tick data from buffer
    
    Args:
        symbol: The ticker symbol
        count: Maximum number of recent ticks to return
        
    Returns:
        Dict containing recent tick data
    """
    return await tickbytick_tools_v2.get_recent_tick_data(
        symbol=symbol,
        count=count
    )

@mcp.tool()
async def analyze_tick_data(
    symbol: str,
    lookback_minutes: int = 5
) -> Dict:
    """
    Analyze recent tick data for patterns and statistics
    
    Args:
        symbol: The ticker symbol
        lookback_minutes: Time window for analysis in minutes
        
    Returns:
        Dict containing tick data analysis
    """
    return await tickbytick_tools_v2.analyze_tick_data(
        symbol=symbol,
        lookback_minutes=lookback_minutes
    )

@mcp.tool()
async def get_active_tick_subscriptions() -> Dict:
    """
    Get list of active tick-by-tick subscriptions
    
    Returns:
        Dict containing list of active subscriptions
    """
    return await tickbytick_tools_v2.get_active_tick_subscriptions()

# MarketDepth Domain MCP Wrappers
@mcp.tool()
async def get_market_depth(
    symbol: str,
    num_rows: int = 10,
    is_smart_depth: bool = True,
    exchange: str = "SMART"
) -> Dict:
    """
    Get current market depth (Level II) data for a symbol
    
    Args:
        symbol: The ticker symbol
        num_rows: Number of depth rows to retrieve
        is_smart_depth: Whether to use smart depth routing
        exchange: Exchange to get data from
        
    Returns:
        Dict containing market depth data
    """
    return await marketdepth_tools_v2.get_market_depth(
        symbol=symbol,
        num_rows=num_rows,
        is_smart_depth=is_smart_depth
    )

@mcp.tool()
async def stream_market_depth(
    symbol: str,
    num_rows: int = 10,
    is_smart_depth: bool = True,
    exchange: str = "SMART"
) -> Dict:
    """
    Start streaming market depth data for a symbol
    
    Args:
        symbol: The ticker symbol
        num_rows: Number of depth rows to stream
        is_smart_depth: Whether to use smart depth routing
        exchange: Exchange to stream from
        
    Returns:
        Dict containing stream subscription status
    """
    return await marketdepth_tools_v2.stream_market_depth(
        symbol=symbol,
        num_rows=num_rows,
        is_smart_depth=is_smart_depth
    )

@mcp.tool()
async def cancel_market_depth(symbol: str, exchange: str = "SMART") -> Dict:
    """
    Cancel market depth subscription for a symbol
    
    Args:
        symbol: The ticker symbol
        exchange: Exchange the subscription is on
        
    Returns:
        Dict containing cancellation status
    """
    return await marketdepth_tools_v2.cancel_market_depth(
        subscription_id=f"depth_{symbol}"
    )

@mcp.tool()
async def get_order_book_analysis(
    symbol: str,
    analysis_depth: int = 10,
    exchange: str = "SMART"
) -> Dict:
    """
    Analyze order book for imbalances and liquidity
    
    Args:
        symbol: The ticker symbol
        analysis_depth: Number of levels to analyze
        exchange: Exchange to analyze
        
    Returns:
        Dict containing order book analysis
    """
    return await marketdepth_tools_v2.get_order_book_analysis(
        symbol=symbol,
        num_rows=analysis_depth
    )

@mcp.tool()
async def get_active_market_depth_subscriptions() -> Dict:
    """
    Get list of active market depth subscriptions
    
    Returns:
        Dict containing list of active market depth subscriptions
    """
    return await marketdepth_tools_v2.get_active_market_depth_subscriptions()

# RealTimeBars Domain MCP Wrappers
@mcp.tool()
async def start_real_time_bars(
    symbol: str,
    bar_size: str = "5 secs",
    what_to_show: str = "TRADES",
    use_rth: bool = True,
    exchange: str = "SMART"
) -> Dict:
    """
    Start receiving real-time 5-second bars for a symbol
    
    Args:
        symbol: The ticker symbol
        bar_size: Bar size (must be "5 secs")
        what_to_show: Type of data ('TRADES', 'MIDPOINT', 'BID', 'ASK')
        use_rth: Whether to use regular trading hours only
        exchange: Exchange to get data from
        
    Returns:
        Dict containing subscription status
    """
    return await realtimebars_tools_v2.start_real_time_bars(
        symbol=symbol,
        bar_size=bar_size,
        what_to_show=what_to_show,
        use_rth=use_rth
    )

@mcp.tool()
async def stop_real_time_bars(symbol: str, exchange: str = "SMART") -> Dict:
    """
    Stop receiving real-time bars for a symbol
    
    Args:
        symbol: The ticker symbol
        exchange: Exchange the subscription is on
        
    Returns:
        Dict containing unsubscription status
    """
    return await realtimebars_tools_v2.stop_real_time_bars(
        subscription_id=f"rtb_{symbol}"
    )

@mcp.tool()
async def get_recent_bars(
    symbol: str,
    count: int = 20,
    exchange: str = "SMART"
) -> Dict:
    """
    Get recent real-time bars from buffer
    
    Args:
        symbol: The ticker symbol
        count: Number of recent bars to return
        exchange: Exchange to get data from
        
    Returns:
        Dict containing recent bar data
    """
    return await realtimebars_tools_v2.get_recent_bars(
        symbol=symbol,
        count=count
    )

@mcp.tool()
async def analyze_real_time_bars(
    symbol: str,
    analysis_period: int = 50,
    include_volume: bool = True,
    exchange: str = "SMART"
) -> Dict:
    """
    Analyze recent real-time bars for patterns
    
    Args:
        symbol: The ticker symbol
        analysis_period: Number of bars to analyze
        include_volume: Whether to include volume analysis
        exchange: Exchange to analyze data from
        
    Returns:
        Dict containing real-time bar analysis
    """
    return await realtimebars_tools_v2.analyze_real_time_bars(
        symbol=symbol,
        lookback_minutes=analysis_period  # Convert bars to minutes (5 sec bars)
    )

@mcp.tool()
async def get_active_realtime_subscriptions() -> Dict:
    """
    Get list of active real-time bar subscriptions
    
    Returns:
        Dict containing list of active subscriptions
    """
    return await realtimebars_tools_v2.get_active_realtime_subscriptions()

# News Domain MCP Wrappers
@mcp.tool()
async def get_news_headlines(
    symbol: str = "",
    news_source: str = "",
    max_results: int = 10,
    start_date: str = "",
    end_date: str = ""
) -> Dict:
    """
    Get news headlines for a symbol or general market news
    
    Args:
        symbol: The ticker symbol (empty for general news)
        news_source: Specific news source filter
        max_results: Maximum number of headlines to return
        start_date: Start date for news search (YYYY-MM-DD)
        end_date: End date for news search (YYYY-MM-DD)
        
    Returns:
        Dict containing news headlines
    """
    return await news_tools_v2.get_news_headlines(
        provider=news_source if news_source else None,
        limit=max_results
    )

@mcp.tool()
async def get_news_article(article_id: str) -> Dict:
    """
    Get full news article content by ID
    
    Args:
        article_id: The news article ID from headlines
        
    Returns:
        Dict containing full article content
    """
    return await news_tools_v2.get_news_article(
        article_id=article_id,
        provider_code="BRFG"
    )

@mcp.tool()
async def stream_news(
    symbols: List[str] = [],
    news_sources: List[str] = [],
    keywords: List[str] = []
) -> Dict:
    """
    Start streaming news updates
    
    Args:
        symbols: List of symbols to monitor
        news_sources: List of news sources to monitor
        keywords: List of keywords to filter by
        
    Returns:
        Dict containing stream subscription status
    """
    return await news_tools_v2.stream_news(
        provider=news_sources[0] if news_sources else None
    )

@mcp.tool()
async def stop_news_stream() -> Dict:
    """
    Stop streaming news updates
    
    Returns:
        Dict containing stream stop status
    """
    return await news_tools_v2.stop_news_stream(subscription_id="all")

@mcp.tool()
async def analyze_news_sentiment(
    symbol: str,
    time_period_hours: int = 24,
    include_social_media: bool = False
) -> Dict:
    """
    Analyze news sentiment for a symbol
    
    Args:
        symbol: The ticker symbol
        time_period_hours: Time period to analyze
        include_social_media: Whether to include social media sentiment
        
    Returns:
        Dict containing sentiment analysis
    """
    return await news_tools_v2.analyze_news_sentiment(
        text=f"News analysis for {symbol}"
    )

@mcp.tool()
async def get_news_sources() -> Dict:
    """
    Get list of available news sources
    
    Returns:
        Dict containing available news sources
    """
    return await news_tools_v2.get_news_sources()

# Scanner Domain MCP Wrappers
@mcp.tool()
async def run_market_scanner(
    scan_type: str = "TOP_PERC_GAIN",
    instrument: str = "STK",
    location: str = "STK.US.MAJOR",
    max_results: int = 50,
    min_price: float = 1.0,
    max_price: Optional[float] = None,
    min_volume: int = 100000
) -> Dict:
    """
    Run market scanner to find securities matching criteria
    
    Args:
        scan_type: Type of scan to run
        instrument: Instrument type ('STK', 'OPT', 'FUT', etc.)
        location: Market location/exchange
        max_results: Maximum number of results
        min_price: Minimum price filter
        max_price: Maximum price filter
        min_volume: Minimum volume filter
        
    Returns:
        Dict containing scanner results
    """
    return await scanner_tools_v2.run_market_scanner(
        scan_code=scan_type,
        instrument=instrument,
        location_code=location,
        number_of_rows=max_results,
        filters={"min_price": min_price, "max_price": max_price, "min_volume": min_volume}
    )

@mcp.tool()
async def get_scanner_parameters() -> Dict:
    """
    Get available scanner parameters and scan types
    
    Returns:
        Dict containing scanner parameters
    """
    return await scanner_tools_v2.get_scanner_parameters()

@mcp.tool()
async def run_custom_scanner(
    criteria: Dict,
    max_results: int = 50
) -> Dict:
    """
    Run custom scanner with specific criteria
    
    Args:
        criteria: Custom scanner criteria dictionary
        max_results: Maximum number of results
        
    Returns:
        Dict containing custom scanner results
    """
    return await scanner_tools_v2.run_custom_scanner(
        criteria=criteria,
        number_of_rows=max_results
    )

@mcp.tool()
async def analyze_scanner_results(
    scan_results: List[Dict],
    analysis_type: str = "momentum"
) -> Dict:
    """
    Analyze scanner results for additional insights
    
    Args:
        scan_results: Results from previous scanner run
        analysis_type: Type of analysis to perform
        
    Returns:
        Dict containing analysis of scanner results
    """
    return await scanner_tools_v2.analyze_scanner_results(
        scan_results={"results": scan_results}
    )

@mcp.tool()
async def save_scanner_preset(
    name: str,
    scan_type: str,
    criteria: Dict
) -> Dict:
    """
    Save scanner configuration as preset
    
    Args:
        name: Name for the preset
        scan_type: Type of scan
        criteria: Scanner criteria
        
    Returns:
        Dict containing save status
    """
    return await scanner_tools_v2.save_scanner_preset(
        name=name,
        criteria={"scan_type": scan_type, **criteria}
    )

@mcp.tool()
async def get_scanner_presets() -> Dict:
    """
    Get saved scanner presets
    
    Returns:
        Dict containing saved scanner presets
    """
    return await scanner_tools_v2.get_scanner_presets()

# OptionsTrading Domain MCP Wrappers
@mcp.tool()
async def get_options_chain(
    symbol: str,
    expiration_date: str = "",
    strike_range: float = 0.20,
    include_greeks: bool = True,
    exchange: str = "SMART"
) -> Dict:
    """
    Get options chain data for a symbol
    
    Args:
        symbol: The underlying symbol
        expiration_date: Specific expiration date (YYYYMMDD) or empty for all
        strike_range: Range around current price (0.20 = ±20%)
        include_greeks: Whether to include Greeks calculations
        exchange: Exchange to get data from
        
    Returns:
        Dict containing options chain data
    """
    # Convert strike_range from percentage to tuple if provided
    strike_range_tuple = None
    if strike_range > 0:
        # This would need current price to calculate actual range
        # For now, pass None to get all strikes
        strike_range_tuple = None

    return await optionstrading_tools_v2.get_options_chain(
        symbol=symbol,
        expiration=expiration_date,
        strike_range=strike_range_tuple
    )

@mcp.tool()
async def calculate_greeks(
    symbol: str,
    option_type: str,
    strike: float,
    expiration: str,
    underlying_price: float = 0.0,
    risk_free_rate: float = 0.05,
    dividend_yield: float = 0.0
) -> Dict:
    """
    Calculate option Greeks for specific option contract
    
    Args:
        symbol: The underlying symbol
        option_type: 'CALL' or 'PUT'
        strike: Strike price
        expiration: Expiration date (YYYYMMDD)
        underlying_price: Current underlying price (auto-fetch if None)
        risk_free_rate: Risk-free interest rate
        dividend_yield: Dividend yield
        
    Returns:
        Dict containing calculated Greeks
    """
    return await optionstrading_tools_v2.calculate_greeks(
        symbol=symbol,
        option_type=option_type,
        strike=strike,
        expiration=expiration,
        underlying_price=underlying_price,
        risk_free_rate=risk_free_rate,
        dividend_yield=dividend_yield
    )

@mcp.tool()
async def analyze_option_strategy(
    strategy_type: str,
    symbol: str,
    legs: List[Dict],
    underlying_price: float = 0.0,
    max_profit_target: float = 0.0,
    max_loss_limit: float = 0.0
) -> Dict:
    """
    Analyze multi-leg option strategy
    
    Args:
        strategy_type: Type of strategy ('iron_condor', 'butterfly', etc.)
        symbol: The underlying symbol
        legs: List of option legs with strike, expiration, type, action
        underlying_price: Current underlying price
        max_profit_target: Maximum profit target
        max_loss_limit: Maximum loss limit
        
    Returns:
        Dict containing strategy analysis
    """
    return await optionstrading_tools_v2.analyze_option_strategy(
        strategy_name=strategy_type,
        legs=legs,
        underlying_price=underlying_price
    )

@mcp.tool()
async def get_implied_volatility_surface(
    symbol: str,
    expiration_months: int = 6,
    moneyness_range: float = 0.30,
    exchange: str = "SMART"
) -> Dict:
    """
    Get implied volatility surface for options
    
    Args:
        symbol: The underlying symbol
        expiration_months: Number of expiration months to include
        moneyness_range: Moneyness range around ATM (0.30 = ±30%)
        exchange: Exchange to get data from
        
    Returns:
        Dict containing implied volatility surface data
    """
    return await optionstrading_tools_v2.get_implied_volatility_surface(
        symbol=symbol
    )

@mcp.tool()
async def create_options_strategy(
    strategy_type: str,
    symbol: str,
    underlying_price: float,
    target_profit: float,
    max_loss: float,
    expiration_preference: str = "30-45",
    exchange: str = "SMART"
) -> Dict:
    """
    Create optimized options strategy based on criteria
    
    Args:
        strategy_type: Type of strategy ('iron_condor', 'butterfly', 'straddle', etc.)
        symbol: Underlying symbol
        underlying_price: Current underlying price
        target_profit: Target profit amount
        max_loss: Maximum acceptable loss
        expiration_preference: Days to expiration preference (e.g., '30-45')
        exchange: Exchange to trade on
        
    Returns:
        Dict containing strategy details and execution plan
    """
    return await optionstrading_tools_v2.create_options_strategy(
        strategy_type=strategy_type,
        symbol=symbol,
        underlying_price=underlying_price,
        target_profit=target_profit,
        max_loss=max_loss,
        expiration_preference=expiration_preference,
        exchange=exchange
    )

# ============================================================================
# RISKMANAGEMENT DOMAIN - Layer 1: MCP Wrapper Functions
# ============================================================================

@mcp.tool()
async def calculate_portfolio_var(
    account: str,
    confidence_level: float = 0.95,
    time_horizon: int = 1,
    method: str = "historical"
) -> Dict:
    """
    Calculate Value at Risk (VaR) for portfolio
    
    Args:
        account: Account identifier
        confidence_level: Confidence level (0.95 = 95%)
        time_horizon: Time horizon in days
        method: VaR calculation method ('historical', 'parametric', 'monte_carlo')
        
    Returns:
        Dict containing VaR calculations and analysis
    """
    return await riskmanagement_tools_v2.calculate_portfolio_var(
        account=account,
        confidence_level=confidence_level,
        time_horizon=time_horizon,
        method=method
    )

@mcp.tool()
async def analyze_position_risk(
    symbol: str,
    position_size: float,
    position_type: str = "long"
) -> Dict:
    """
    Analyze risk metrics for a specific position
    
    Args:
        symbol: Symbol to analyze
        position_size: Size of position
        position_type: Type of position ('long' or 'short')
        
    Returns:
        Dict containing position risk analysis
    """
    return await riskmanagement_tools_v2.analyze_position_risk(
        symbol=symbol,
        position_size=position_size,
        position_type=position_type
    )

@mcp.tool()
async def monitor_risk_limits(
    account: str,
    var_limit: float = 0.0,
    drawdown_limit: float = 0.0,
    concentration_limit: float = 0.0
) -> Dict:
    """
    Monitor portfolio against defined risk limits
    
    Args:
        account: Account identifier
        var_limit: VaR limit threshold
        drawdown_limit: Maximum drawdown limit
        concentration_limit: Position concentration limit
        
    Returns:
        Dict containing risk limit monitoring results
    """
    return await riskmanagement_tools_v2.monitor_risk_limits(
        account=account,
        var_limit=var_limit or 0.0,
        drawdown_limit=drawdown_limit or 0.0,
        concentration_limit=concentration_limit or 0.0
    )

@mcp.tool()
async def calculate_correlation_matrix(
    symbols: List[str],
    lookback_days: int = 252,
    frequency: str = "daily"
) -> Dict:
    """
    Calculate correlation matrix between securities
    
    Args:
        symbols: List of symbols to analyze
        lookback_days: Number of days for historical data
        frequency: Data frequency ('daily', 'weekly', 'monthly')
        
    Returns:
        Dict containing correlation matrix and analysis
    """
    return await riskmanagement_tools_v2.calculate_correlation_matrix(
        symbols=symbols,
        lookback_days=lookback_days,
        frequency=frequency
    )

@mcp.tool()
async def perform_stress_test(
    account: str,
    scenario_type: str = "all",
    custom_shocks: Dict = {}
) -> Dict:
    """
    Perform stress testing on portfolio under various scenarios
    
    Args:
        account: Account identifier
        scenario_type: Type of stress test ('all', 'financial_crisis', 'market_crash', 'custom')
        custom_shocks: Custom shock parameters for stress testing
        
    Returns:
        Dict containing stress test results
    """
    return await riskmanagement_tools_v2.perform_stress_test(
        account=account,
        scenario_type=scenario_type,
        custom_shocks=custom_shocks or {}
    )

# ============================================================================
# TECHNICALANALYSIS DOMAIN - Layer 1: MCP Wrapper Functions
# ============================================================================

@mcp.tool()
async def calculate_technical_indicators(
    symbol: str,
    indicators: List[str],
    period: int = 20,
    lookback_days: int = 252
) -> Dict:
    """
    Calculate technical indicators for a symbol
    
    Args:
        symbol: Symbol to analyze
        indicators: List of indicators to calculate (SMA, EMA, RSI, MACD, etc.)
        period: Period for indicator calculations
        lookback_days: Number of days of historical data
        
    Returns:
        Dict containing calculated indicators
    """
    return await technicalanalysis_tools_v2.calculate_technical_indicators(
        symbol=symbol,
        indicators=indicators or [],
        period=period,
        lookback_days=lookback_days
    )

@mcp.tool()
async def identify_chart_patterns(
    symbol: str,
    pattern_types: List[str] = [],
    lookback_days: int = 100
) -> Dict:
    """
    Identify chart patterns in price data
    
    Args:
        symbol: Symbol to analyze
        pattern_types: List of pattern types to search for
        lookback_days: Number of days to analyze
        
    Returns:
        Dict containing identified patterns
    """
    return await technicalanalysis_tools_v2.identify_chart_patterns(
        symbol=symbol,
        pattern_types=pattern_types or [],
        lookback_days=lookback_days
    )

@mcp.tool()
async def analyze_trend_strength(
    symbol: str,
    timeframe: str = "daily",
    lookback_days: int = 50
) -> Dict:
    """
    Analyze trend strength and direction
    
    Args:
        symbol: Symbol to analyze
        timeframe: Timeframe for analysis
        lookback_days: Number of days to analyze
        
    Returns:
        Dict containing trend analysis
    """
    return await technicalanalysis_tools_v2.analyze_trend_strength(
        symbol=symbol,
        timeframe=timeframe,
        lookback_days=lookback_days
    )

@mcp.tool()
async def generate_trading_signals(
    symbol: str,
    signal_types: List[str] = [],
    sensitivity: str = "medium"
) -> Dict:
    """
    Generate trading signals based on technical analysis
    
    Args:
        symbol: Symbol to analyze
        signal_types: Types of signals to generate
        sensitivity: Signal sensitivity level
        
    Returns:
        Dict containing trading signals
    """
    return await technicalanalysis_tools_v2.generate_trading_signals(
        symbol=symbol,
        signal_types=signal_types or [],
        sensitivity=sensitivity
    )

@mcp.tool()
async def perform_market_scan(
    symbols: List[str],
    scan_criteria: Dict[str, Any]
) -> Dict:
    """
    Perform technical analysis scan across multiple symbols
    
    Args:
        symbols: List of symbols to scan
        scan_criteria: Criteria for scanning
        
    Returns:
        Dict containing scan results
    """
    return await technicalanalysis_tools_v2.perform_market_scan(
        symbols=symbols,
        scan_criteria=scan_criteria
    )

# ============================================================================
# VOLATILITYSURFACE DOMAIN - Layer 1: MCP Wrapper Functions
# ============================================================================

@mcp.tool()
async def build_volatility_surface(
    underlying_symbol: str,
    strikes: List[float] = [],
    expiries: List[str] = [],
    surface_type: str = "implied_volatility"
) -> Dict:
    """
    Build comprehensive volatility surface for analysis
    
    Args:
        underlying_symbol: The underlying symbol
        strikes: List of strike prices (optional)
        expiries: List of expiry dates (optional) 
        surface_type: Type of surface to build
        
    Returns:
        Dict containing volatility surface data
    """
    return await volatility_surface_tools_v2.build_volatility_surface(
        underlying_symbol=underlying_symbol,
        strikes=strikes or [],
        expiries=expiries or [],
        surface_type=surface_type
    )

@mcp.tool()
async def analyze_volatility_smile(
    underlying_symbol: str,
    expiry: str,
    strikes: List[float] = []
) -> Dict:
    """
    Analyze volatility smile for specific expiry
    
    Args:
        underlying_symbol: The underlying symbol
        expiry: Expiry date to analyze
        strikes: List of strike prices (optional)
        
    Returns:
        Dict containing volatility smile analysis
    """
    return await volatility_surface_tools_v2.analyze_volatility_smile(
        underlying_symbol=underlying_symbol,
        expiry=expiry,
        strikes=strikes or []
    )

@mcp.tool()
async def analyze_term_structure(
    underlying_symbol: str,
    strike: float,
    expiries: List[str] = []
) -> Dict:
    """
    Analyze volatility term structure for specific strike
    
    Args:
        underlying_symbol: The underlying symbol
        strike: Strike price to analyze
        expiries: List of expiry dates (optional)
        
    Returns:
        Dict containing term structure analysis
    """
    return await volatility_surface_tools_v2.analyze_term_structure(
        underlying_symbol=underlying_symbol,
        strike=strike,
        expiries=expiries or []
    )

@mcp.tool()
async def detect_volatility_arbitrage(
    underlying_symbol: str,
    min_profit_threshold: float = 0.01,
    include_calendar_spreads: bool = True
) -> Dict:
    """
    Detect volatility arbitrage opportunities
    
    Args:
        underlying_symbol: The underlying symbol
        min_profit_threshold: Minimum profit threshold
        include_calendar_spreads: Whether to include calendar spreads
        
    Returns:
        Dict containing arbitrage opportunities
    """
    return await volatility_surface_tools_v2.detect_volatility_arbitrage(
        underlying_symbol=underlying_symbol,
        min_profit_threshold=min_profit_threshold,
        include_calendar_spreads=include_calendar_spreads
    )

# ============================================================================
# ALGORITHMICTRADING DOMAIN - Layer 1: MCP Wrapper Functions
# ============================================================================

@mcp.tool()
async def execute_algo_strategy(
    strategy_type: str,
    symbols: List[str],
    max_position_size: float,
    risk_limit: float,
    account: str,
    custom_params: Optional[Dict[str, Any]] = None
) -> Dict:
    """
    Execute an algorithmic trading strategy
    
    Available strategies:
    - statistical_arbitrage: Exploit pricing inefficiencies between related securities
    - market_making: Provide liquidity with bid/ask spreads
    - trend_following: Trade in direction of established trends
    - mean_reversion: Trade expecting price to revert to mean
    - pairs_trading: Trade correlated pairs
    - momentum: Trade based on price momentum
    - vwap: Volume Weighted Average Price execution
    - twap: Time Weighted Average Price execution
    - smart_order_routing: Intelligent order routing
    - iceberg: Hidden size orders
    
    Args:
        strategy_type: Type of strategy to execute
        symbols: List of symbols to trade
        max_position_size: Maximum position size in dollars
        risk_limit: Maximum risk/loss limit
        account: Account ID
        custom_params: Strategy-specific parameters
        
    Returns:
        Dict containing strategy execution result
    """
    return await algorithmictrading_tools_v2.execute_algo_strategy(
        strategy_type=strategy_type,
        symbols=symbols,
        max_position_size=max_position_size,
        risk_limit=risk_limit,
        account=account,
        custom_params=custom_params
    )

@mcp.tool()
async def stop_algo_strategy(strategy_id: str) -> Dict:
    """
    Stop a running algorithmic strategy
    
    Args:
        strategy_id: ID of the strategy to stop
        
    Returns:
        Dict containing operation status
    """
    return await algorithmictrading_tools_v2.stop_algo_strategy(strategy_id=strategy_id)

@mcp.tool()
async def subscribe_streaming_data(
    symbol: str,
    data_types: List[str],
    generic_tick_list: str = ""
) -> Dict:
    """
    Subscribe to real-time streaming market data
    
    Args:
        symbol: Symbol to subscribe to
        data_types: Types of data to stream (e.g., ['BID', 'ASK', 'LAST'])
        generic_tick_list: Additional tick types
        
    Returns:
        Dict containing subscription status
    """
    return await algorithmictrading_tools_v2.subscribe_streaming_data(
        symbol=symbol,
        data_types=data_types,
        generic_tick_list=generic_tick_list
    )

@mcp.tool()
async def unsubscribe_streaming_data(subscription_id: str) -> Dict:
    """
    Unsubscribe from streaming market data
    
    Args:
        subscription_id: ID of subscription to cancel
        
    Returns:
        Dict containing operation status
    """
    return await algorithmictrading_tools_v2.unsubscribe_streaming_data(subscription_id=subscription_id)

@mcp.tool()
async def rebalance_portfolio(
    strategy: str,
    target_allocations: List[Dict[str, Any]],
    account: str,
    rebalance_threshold: float = 0.05,
    force: bool = False
) -> Dict:
    """
    Automatically rebalance portfolio to target allocations
    
    Args:
        strategy: Rebalancing strategy to use
        target_allocations: List of target allocations
        account: Account to rebalance
        rebalance_threshold: Threshold for triggering rebalance
        force: Force rebalancing regardless of threshold
        
    Returns:
        Dict containing rebalancing results
    """
    return await algorithmictrading_tools_v2.rebalance_portfolio(
        strategy=strategy,
        target_allocations=target_allocations,
        account=account,
        rebalance_threshold=rebalance_threshold,
        force=force
    )

@mcp.tool()
async def optimize_portfolio(
    symbols: List[str],
    optimization_method: str = "maximum_sharpe",
    constraints: Optional[Dict[str, Any]] = None
) -> Dict:
    """
    Optimize portfolio allocation using various methods
    
    Args:
        symbols: List of symbols to optimize
        optimization_method: Optimization method (maximum_sharpe, min_variance, etc.)
        constraints: Additional constraints
        
    Returns:
        Dict containing optimized allocations
    """
    return await algorithmictrading_tools_v2.optimize_portfolio(
        symbols=symbols,
        optimization_method=optimization_method,
        constraints=constraints
    )

@mcp.tool()
async def route_smart_order(
    algorithm: str,
    symbol: str,
    action: str,
    quantity: float,
    account: str,
    urgency: str = "Normal",
    custom_params: Optional[Dict[str, Any]] = None
) -> Dict:
    """
    Route order using advanced smart order routing algorithms
    
    Args:
        algorithm: Routing algorithm to use
        symbol: Symbol to trade
        action: BUY or SELL
        quantity: Quantity to trade
        account: Account to use
        urgency: Urgency level (Normal, Patient, Urgent)
        custom_params: Additional parameters
        
    Returns:
        Dict containing routing results
    """
    return await algorithmictrading_tools_v2.route_smart_order(
        algorithm=algorithm,
        symbol=symbol,
        action=action,
        quantity=quantity,
        account=account,
        urgency=urgency,
        custom_params=custom_params
    )

@mcp.tool()
async def analyze_market_microstructure(
    symbol: str,
    size: int
) -> Dict:
    """
    Analyze market microstructure for optimal execution
    
    Args:
        symbol: Symbol to analyze
        size: Order size for impact analysis
        
    Returns:
        Dict containing microstructure analysis
    """
    return await algorithmictrading_tools_v2.analyze_market_microstructure(symbol=symbol, size=size)

@mcp.tool()
async def start_trading_strategy(
    strategy_name: str,
    strategy_id: str,
    account: str,
    symbols: List[str],
    parameters: Dict[str, Any],
    risk_limits: Dict[str, float]
) -> Dict:
    """
    Start a custom trading strategy
    
    Args:
        strategy_name: Name of strategy to start
        strategy_id: Unique ID for this strategy instance
        account: Account to use
        symbols: Symbols to trade
        parameters: Strategy parameters
        risk_limits: Risk limits for the strategy
        
    Returns:
        Dict containing strategy startup status
    """
    return await algorithmictrading_tools_v2.start_trading_strategy(
        strategy_name=strategy_name,
        strategy_id=strategy_id,
        account=account,
        symbols=symbols,
        parameters=parameters,
        risk_limits=risk_limits
    )

@mcp.tool()
async def stop_trading_strategy(strategy_id: str) -> Dict:
    """
    Stop a running trading strategy
    
    Args:
        strategy_id: ID of strategy to stop
        
    Returns:
        Dict containing operation status
    """
    return await algorithmictrading_tools_v2.stop_trading_strategy(strategy_id=strategy_id)

@mcp.tool()
async def get_strategy_status(strategy_id: str) -> Dict:
    """
    Get status of a trading strategy
    
    Args:
        strategy_id: ID of strategy to check
        
    Returns:
        Dict containing strategy status and performance
    """
    return await algorithmictrading_tools_v2.get_strategy_status(strategy_id=strategy_id)

@mcp.tool()
async def get_active_algo_strategies() -> Dict:
    """
    Get list of all active algorithmic strategies
    
    Returns:
        Dict containing list of active strategies
    """
    return await algorithmictrading_tools_v2.get_active_algo_strategies()

# ============================================================================
# ORDER MANAGEMENT TOOLS (CRITICAL - PREVIOUSLY MISSING)
# ============================================================================

@mcp.tool()
async def place_order(
    symbol: str,
    action: str,
    quantity: float,
    order_type: str = "MKT",
    price: Optional[float] = None,
    stop_price: Optional[float] = None,
    time_in_force: str = "DAY",
    account: Optional[str] = None
) -> Dict:
    """
    Place a new order with the specified parameters

    Args:
        symbol: The ticker symbol (e.g., 'AAPL', 'TSLA')
        action: 'BUY' or 'SELL'
        quantity: Number of shares/contracts to trade
        order_type: Order type ('MKT', 'LMT', 'STP', 'BRACKET', 'ADAPTIVE', 'AUCTION')
        price: Limit price (required for LMT orders)
        stop_price: Stop price (required for STP orders)
        time_in_force: Time in force ('DAY', 'GTC', 'IOC', 'FOK')
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing order placement result and order ID
    """
    return await ordermanagement_tools_v2.place_order(
        symbol=symbol,
        action=action,
        quantity=quantity,
        order_type=order_type,
        price=price,
        stop_price=stop_price,
        time_in_force=time_in_force,
        account=account
    )

@mcp.tool()
async def cancel_order(order_id: int) -> Dict:
    """
    Cancel an existing order by its ID

    Args:
        order_id: The order ID to cancel

    Returns:
        Dict containing cancellation status
    """
    return await ordermanagement_tools_v2.cancel_order(order_id=order_id)

@mcp.tool()
async def modify_order(order_id: int, parameters: Dict[str, Any]) -> Dict:
    """
    Modify an existing order with new parameters

    Args:
        order_id: The order ID to modify
        parameters: Dictionary of parameters to modify (price, quantity, etc.)

    Returns:
        Dict containing modification status
    """
    return await ordermanagement_tools_v2.modify_order(order_id=order_id, parameters=parameters)

@mcp.tool()
async def get_order_status(order_id: int) -> Dict:
    """
    Get the current status of an order

    Args:
        order_id: The order ID to check

    Returns:
        Dict containing order status and details
    """
    return await ordermanagement_tools_v2.get_order_status(order_id=order_id)

@mcp.tool()
async def get_active_orders() -> Dict:
    """
    Get all currently active orders

    Returns:
        Dict containing list of active orders
    """
    return await ordermanagement_tools_v2.get_active_orders()

@mcp.tool()
async def get_order_history() -> Dict:
    """
    Get order history for the account

    Returns:
        Dict containing order history
    """
    return await ordermanagement_tools_v2.get_order_history()

@mcp.tool()
async def place_bracket_order(
    symbol: str,
    action: str,
    quantity: float,
    entry_price: float,
    profit_price: float,
    stop_price: float,
    account: Optional[str] = None
) -> Dict:
    """
    Place a bracket order with entry, profit target, and stop loss

    Args:
        symbol: The ticker symbol (e.g., 'AAPL', 'TSLA')
        action: 'BUY' or 'SELL'
        quantity: Number of shares/contracts to trade
        entry_price: Entry order price
        profit_price: Profit target price
        stop_price: Stop loss price
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing bracket order placement result
    """
    return await ordermanagement_tools_v2.place_bracket_order(
        symbol=symbol,
        action=action,
        quantity=quantity,
        entry_price=entry_price,
        profit_price=profit_price,
        stop_price=stop_price,
        account=account
    )

@mcp.tool()
async def place_adaptive_order(
    symbol: str,
    action: str,
    quantity: float,
    price: float,
    priority: str = "Normal",
    account: Optional[str] = None
) -> Dict:
    """
    Place an adaptive algorithm order for better execution

    Args:
        symbol: The ticker symbol (e.g., 'AAPL', 'TSLA')
        action: 'BUY' or 'SELL'
        quantity: Number of shares/contracts to trade
        price: Limit price
        priority: Priority setting ('Urgent', 'Normal', 'Patient')
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing adaptive order placement result
    """
    return await ordermanagement_tools_v2.place_adaptive_order(
        symbol=symbol,
        action=action,
        quantity=quantity,
        price=price,
        priority=priority,
        account=account
    )

@mcp.tool()
async def place_auction_order(
    symbol: str,
    action: str,
    quantity: float,
    price: float,
    account: Optional[str] = None
) -> Dict:
    """
    Place an auction order for pre-market execution

    Args:
        symbol: The ticker symbol (e.g., 'AAPL', 'TSLA')
        action: 'BUY' or 'SELL'
        quantity: Number of shares/contracts to trade
        price: Limit price
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing auction order placement result
    """
    return await ordermanagement_tools_v2.place_auction_order(
        symbol=symbol,
        action=action,
        quantity=quantity,
        price=price,
        account=account
    )

@mcp.tool()
async def start_market_making(
    symbols: List[str],
    mode: str = "neutral",
    parameters: Optional[Dict[str, Any]] = None
) -> Dict:
    """
    Start market making strategy for specified symbols

    Args:
        symbols: List of symbols to make markets in
        mode: Market making mode ('neutral', 'aggressive', 'passive')
        parameters: Optional strategy parameters

    Returns:
        Dict containing market making startup result
    """
    return await ordermanagement_tools_v2.start_market_making(
        symbols=symbols,
        mode=mode,
        parameters=parameters
    )

@mcp.tool()
async def stop_market_making(symbols: Optional[List[str]] = None) -> Dict:
    """
    Stop market making strategy

    Args:
        symbols: Optional list of symbols to stop (stops all if not specified)

    Returns:
        Dict containing market making stop result
    """
    return await ordermanagement_tools_v2.stop_market_making(symbols=symbols)

@mcp.tool()
async def analyze_order_flow(
    symbol: str,
    time_window: int = 300,
    analysis_type: str = "comprehensive"
) -> Dict:
    """
    Analyze order flow patterns and liquidity for a symbol

    Args:
        symbol: The ticker symbol to analyze
        time_window: Time window in seconds for analysis (default: 300)
        analysis_type: Type of analysis ('comprehensive', 'basic', 'advanced')

    Returns:
        Dict containing order flow analysis results
    """
    return await ordermanagement_tools_v2.analyze_order_flow(
        symbol=symbol,
        time_window=time_window,
        analysis_type=analysis_type
    )

# ============================================================================
# ACCOUNT & PORTFOLIO MANAGEMENT TOOLS (CRITICAL - PREVIOUSLY MISSING)
# ============================================================================

@mcp.tool()
async def get_account_summary(account: Optional[str] = None) -> Dict:
    """
    Get comprehensive account summary including balance, positions, P&L

    Args:
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing complete account summary
    """
    return await account_tools_v2.get_account_summary(account=account)

@mcp.tool()
async def get_portfolio_positions(account: Optional[str] = None) -> Dict:
    """
    Get current portfolio positions with market values

    Args:
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing all portfolio positions
    """
    return await account_tools_v2.get_portfolio_positions(account=account)

@mcp.tool()
async def get_account_balance(account: Optional[str] = None) -> Dict:
    """
    Get account cash balance and available funds

    Args:
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing account balance information
    """
    return await account_tools_v2.get_account_balance(account=account)

@mcp.tool()
async def get_buying_power(account: Optional[str] = None) -> Dict:
    """
    Get available buying power for trading

    Args:
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing buying power information
    """
    return await account_tools_v2.get_buying_power(account=account)

@mcp.tool()
async def get_pnl(account: Optional[str] = None) -> Dict:
    """
    Get profit and loss information for account

    Args:
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing P&L information
    """
    return await account_tools_v2.get_pnl(account=account)

@mcp.tool()
async def get_account_info(account: Optional[str] = None) -> Dict:
    """
    Get detailed account information and settings

    Args:
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing detailed account information
    """
    return await account_tools_v2.get_account_info(account=account)

@mcp.tool()
async def get_margin_info(account: Optional[str] = None) -> Dict:
    """
    Get margin requirements and maintenance information

    Args:
        account: Account ID (optional, uses default if not specified)

    Returns:
        Dict containing margin information
    """
    return await account_tools_v2.get_margin_info(account=account)

@mcp.tool()
async def get_cash_balance(account: Optional[str] = None, currency: str = "USD") -> Dict:
    """
    Get cash balance for specific currency

    Args:
        account: Account ID (optional, uses default if not specified)
        currency: Currency code (default: USD)

    Returns:
        Dict containing cash balance for specified currency
    """
    return await account_tools_v2.get_cash_balance(account=account, currency=currency)

# ============================================================================
# CONTRACT RESEARCH TOOLS (CRITICAL - PREVIOUSLY MISSING)
# ============================================================================

@mcp.tool()
async def search_contracts(pattern: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict:
    """
    Search for contracts matching pattern across all security types

    Args:
        pattern: Symbol pattern to search for
        sec_type: Security type ('STK', 'OPT', 'FUT', 'CASH')
        exchange: Exchange to search on (default: SMART)
        currency: Currency (default: USD)

    Returns:
        Dict containing matching contracts
    """
    return await contracts_tools_v2.search_contracts(pattern=pattern, sec_type=sec_type, exchange=exchange, currency=currency)

@mcp.tool()
async def get_contract_details(symbol: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict:
    """
    Get detailed contract information including specifications

    Args:
        symbol: Symbol to get details for
        sec_type: Security type ('STK', 'OPT', 'FUT', 'CASH')
        exchange: Exchange (default: SMART)
        currency: Currency (default: USD)

    Returns:
        Dict containing detailed contract information
    """
    return await contracts_tools_v2.get_contract_details(symbol=symbol, sec_type=sec_type, exchange=exchange, currency=currency)

@mcp.tool()
async def search_forex_pairs(base_currency: str = "USD", quote_currency: str = "EUR") -> Dict:
    """
    Search for forex currency pairs and their specifications

    Args:
        base_currency: Base currency (default: USD)
        quote_currency: Quote currency (default: EUR)

    Returns:
        Dict containing forex pair information
    """
    return await contracts_tools_v2.search_forex_pairs(base_currency=base_currency, quote_currency=quote_currency)

@mcp.tool()
async def search_futures(symbol: str, exchange: str = "CME", currency: str = "USD", include_expired: bool = False) -> Dict:
    """
    Search for futures contracts with expiry information

    Args:
        symbol: Futures symbol to search for
        exchange: Exchange (default: CME)
        currency: Currency (default: USD)
        include_expired: Include expired contracts (default: False)

    Returns:
        Dict containing futures contracts
    """
    return await contracts_tools_v2.search_futures(symbol=symbol, exchange=exchange, currency=currency, include_expired=include_expired)

@mcp.tool()
async def search_options(underlying_symbol: str, expiry: Optional[str] = None, strike: Optional[float] = None, right: str = "C") -> Dict:
    """
    Search for options contracts on underlying symbol

    Args:
        underlying_symbol: Underlying symbol for options
        expiry: Expiry date (YYYYMMDD format, optional)
        strike: Strike price (optional)
        right: Option right ('C' for Call, 'P' for Put)

    Returns:
        Dict containing options contracts
    """
    return await contracts_tools_v2.search_options(underlying_symbol=underlying_symbol, expiry=expiry, strike=strike, right=right)

@mcp.tool()
async def search_stocks(pattern: str, exchange: str = "SMART", currency: str = "USD") -> Dict:
    """
    Search for stock contracts by symbol pattern

    Args:
        pattern: Stock symbol pattern to search for
        exchange: Exchange (default: SMART)
        currency: Currency (default: USD)

    Returns:
        Dict containing stock contracts
    """
    return await contracts_tools_v2.search_stocks(pattern=pattern, exchange=exchange, currency=currency)

@mcp.tool()
async def get_contract_specifications(symbol: str, sec_type: str = "STK", exchange: str = "SMART") -> Dict:
    """
    Get detailed contract specifications and trading rules

    Args:
        symbol: Symbol to get specifications for
        sec_type: Security type ('STK', 'OPT', 'FUT', 'CASH')
        exchange: Exchange (default: SMART)

    Returns:
        Dict containing contract specifications
    """
    return await contracts_tools_v2.get_contract_specifications(symbol=symbol, sec_type=sec_type, exchange=exchange)

@mcp.tool()
async def validate_contract(symbol: str, sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict:
    """
    Validate if a contract exists and is available for trading

    Args:
        symbol: Symbol to validate
        sec_type: Security type ('STK', 'OPT', 'FUT', 'CASH')
        exchange: Exchange (default: SMART)
        currency: Currency (default: USD)

    Returns:
        Dict containing validation result
    """
    return await contracts_tools_v2.validate_contract(symbol=symbol, sec_type=sec_type, exchange=exchange, currency=currency)

# ============================================================================
# BACKTESTING INFRASTRUCTURE TOOLS (CRITICAL - PREVIOUSLY MISSING)
# ============================================================================

@mcp.tool()
async def run_backtest(strategy_config: Dict[str, Any], start_date: str, end_date: str, initial_capital: float = 100000) -> Dict:
    """
    Run a comprehensive backtest on historical data with strategy configuration

    Args:
        strategy_config: Strategy configuration dictionary
        start_date: Start date for backtest (YYYY-MM-DD)
        end_date: End date for backtest (YYYY-MM-DD)
        initial_capital: Initial capital amount (default: 100000)

    Returns:
        Dict containing backtest results and performance metrics
    """
    return await backtesting_tools_v2.run_backtest(strategy_config=strategy_config, start_date=start_date, end_date=end_date, initial_capital=initial_capital)

@mcp.tool()
async def optimize_strategy(strategy_config: Dict[str, Any], parameter_ranges: Dict[str, Any], optimization_metric: str = "sharpe_ratio") -> Dict:
    """
    Optimize strategy parameters using grid search or genetic algorithms

    Args:
        strategy_config: Strategy configuration dictionary
        parameter_ranges: Dictionary of parameter ranges to optimize
        optimization_metric: Metric to optimize (default: sharpe_ratio)

    Returns:
        Dict containing optimization results and best parameters
    """
    return await backtesting_tools_v2.optimize_strategy(strategy_config=strategy_config, parameter_ranges=parameter_ranges, optimization_metric=optimization_metric)

@mcp.tool()
async def walk_forward_analysis(strategy_config: Dict[str, Any], window_size: int = 252, step_size: int = 63) -> Dict:
    """
    Perform walk-forward analysis to test strategy robustness over time

    Args:
        strategy_config: Strategy configuration dictionary
        window_size: Size of optimization window in days (default: 252)
        step_size: Step size for rolling window in days (default: 63)

    Returns:
        Dict containing walk-forward analysis results
    """
    return await backtesting_tools_v2.walk_forward_analysis(strategy_config=strategy_config, window_size=window_size, step_size=step_size)

@mcp.tool()
async def monte_carlo_simulation(strategy_config: Dict[str, Any], num_simulations: int = 1000, confidence_level: float = 0.95) -> Dict:
    """
    Run Monte Carlo simulation to assess strategy risk and return distributions

    Args:
        strategy_config: Strategy configuration dictionary
        num_simulations: Number of simulations to run (default: 1000)
        confidence_level: Confidence level for statistics (default: 0.95)

    Returns:
        Dict containing Monte Carlo simulation results
    """
    return await backtesting_tools_v2.monte_carlo_simulation(strategy_config=strategy_config, num_simulations=num_simulations, confidence_level=confidence_level)

@mcp.tool()
async def calculate_performance_metrics(returns: List[float], benchmark_returns: Optional[List[float]] = None) -> Dict:
    """
    Calculate comprehensive performance metrics including Sharpe, Sortino, Calmar ratios

    Args:
        returns: List of strategy returns
        benchmark_returns: Optional benchmark returns for comparison

    Returns:
        Dict containing comprehensive performance metrics
    """
    return await backtesting_tools_v2.calculate_performance_metrics(returns=returns, benchmark_returns=benchmark_returns)

@mcp.tool()
async def compare_strategies(strategy_results: List[Dict[str, Any]], comparison_metrics: Optional[List[str]] = None) -> Dict:
    """
    Compare multiple strategy results across various performance metrics

    Args:
        strategy_results: List of strategy result dictionaries
        comparison_metrics: List of metrics to compare (optional)

    Returns:
        Dict containing strategy comparison results
    """
    return await backtesting_tools_v2.compare_strategies(strategy_results=strategy_results, comparison_metrics=comparison_metrics)

@mcp.tool()
async def generate_backtest_report(backtest_results: Dict[str, Any], include_charts: bool = True) -> Dict:
    """
    Generate comprehensive backtest report with charts and analysis

    Args:
        backtest_results: Results from backtest run
        include_charts: Whether to include chart data (default: True)

    Returns:
        Dict containing comprehensive backtest report
    """
    return await backtesting_tools_v2.generate_backtest_report(backtest_results=backtest_results, include_charts=include_charts)

@mcp.tool()
async def validate_strategy(strategy_config: Dict[str, Any], validation_period: str = "1Y") -> Dict:
    """
    Validate strategy performance on out-of-sample data

    Args:
        strategy_config: Strategy configuration dictionary
        validation_period: Period for validation (default: 1Y)

    Returns:
        Dict containing strategy validation results
    """
    return await backtesting_tools_v2.validate_strategy(strategy_config=strategy_config, validation_period=validation_period)

# ============================================================================
# CORE MCP SERVER FUNCTIONS (Connection, Health, Status)
# ============================================================================

@mcp.tool()
async def connect_to_tws(readonly: bool = False, reconnect: bool = False) -> Dict:
    """
    Connect to Interactive Brokers TWS or Gateway
    
    Args:
        readonly: Whether to connect in read-only mode
        reconnect: Force reconnection if already connected
        
    Returns:
        Dict containing connection status
    """
    try:
        if reconnect and ibkr_service.connected:
            await ibkr_service.disconnect()
        
        await ibkr_service.connect(readonly=readonly)
        
        return {
            "status": "success",
            "message": f"Connected to TWS {'(read-only)' if readonly else '(trading mode)'}",
            "connected": ibkr_service.connected,
            "readonly": ibkr_service.readonly
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to connect: {str(e)}",
            "connected": False
        }

@mcp.tool()
async def disconnect_from_tws() -> Dict:
    """
    Disconnect from Interactive Brokers TWS or Gateway
    
    Returns:
        Dict containing disconnection status
    """
    try:
        await ibkr_service.disconnect()
        return {
            "status": "success",
            "message": "Disconnected from TWS",
            "connected": ibkr_service.connected
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to disconnect: {str(e)}"
        }

@mcp.tool()
async def get_connection_status() -> Dict:
    """
    Get current connection status to TWS
    
    Returns:
        Dict containing detailed connection information
    """
    try:
        status = {
            "connected": ibkr_service.connected,
            "readonly": ibkr_service.readonly,
            "client_id": getattr(ibkr_service, 'client_id', None),
            "host": getattr(ibkr_service, 'host', None),
            "port": getattr(ibkr_service, 'port', None),
            "accounts": ibkr_service.ib.managedAccounts() if ibkr_service.connected else []
        }
        
        return {
            "status": "success",
            "connection_info": status
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to get connection status: {str(e)}"
        }

@mcp.tool()
async def force_service_injection() -> Dict:
    """
    EMERGENCY FIX: Force inject IBKR service into all domain implementations

    Returns:
        Dict containing injection results
    """
    try:
        from ibkr_mcp_server.app.services.ibkr_service import ibkr_service as global_ibkr_service

        injection_results = {
            "status": "success",
            "injected_domains": [],
            "failed_domains": [],
            "service_status": {
                "connected": global_ibkr_service.connected if global_ibkr_service else False,
                "service_available": bool(global_ibkr_service)
            }
        }

        # Get all domain tool instances
        domain_tools = [
            historical_data_tools_v2, tickbytick_tools_v2, marketdepth_tools_v2,
            realtimebars_tools_v2, news_tools_v2, scanner_tools_v2,
            optionstrading_tools_v2, riskmanagement_tools_v2, technicalanalysis_tools_v2,
            volatility_surface_tools_v2, algorithmictrading_tools_v2, ordermanagement_tools_v2,
            account_tools_v2, contracts_tools_v2, backtesting_tools_v2
        ]

        # Force inject service into each domain's implementations
        for tool in domain_tools:
            try:
                domain_name = tool.domain.config.name
                if hasattr(tool, 'domain') and hasattr(tool.domain, 'implementations'):
                    for impl_name, impl_instance in tool.domain.implementations.items():
                        # Force inject ibkr_service
                        impl_instance.ibkr_service = global_ibkr_service
                        logger.info(f"FORCE INJECTED ibkr_service into {domain_name}.{impl_name}")

                    injection_results["injected_domains"].append({
                        "domain": domain_name,
                        "implementations": list(tool.domain.implementations.keys())
                    })

            except Exception as e:
                injection_results["failed_domains"].append({
                    "domain": getattr(tool.domain, 'config', {}).get('name', 'unknown'),
                    "error": str(e)
                })

        return injection_results

    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to force inject services: {str(e)}"
        }

@mcp.tool()
async def debug_service_injection() -> Dict:
    """
    DEBUG: Check service injection status and try direct access

    Returns:
        Dict containing debug information
    """
    try:
        from ibkr_mcp_server.app.services.ibkr_service import ibkr_service as global_ibkr_service

        debug_info = {
            "status": "success",
            "global_service": {
                "available": bool(global_ibkr_service),
                "connected": global_ibkr_service.connected if global_ibkr_service else False,
                "type": str(type(global_ibkr_service)) if global_ibkr_service else None
            },
            "domain_tools_status": {}
        }

        # Check a few key domain tools
        test_tools = [
            ("contracts", contracts_tools_v2),
            ("account", account_tools_v2),
            ("historical_data", historical_data_tools_v2)
        ]

        for tool_name, tool_instance in test_tools:
            try:
                domain_info = {
                    "domain_available": hasattr(tool_instance, 'domain'),
                    "implementations": {}
                }

                if hasattr(tool_instance, 'domain') and hasattr(tool_instance.domain, 'implementations'):
                    for impl_name, impl in tool_instance.domain.implementations.items():
                        domain_info["implementations"][impl_name] = {
                            "has_ibkr_service": hasattr(impl, 'ibkr_service'),
                            "ibkr_service_value": str(getattr(impl, 'ibkr_service', None)),
                            "ibkr_service_connected": getattr(getattr(impl, 'ibkr_service', None), 'connected', False)
                        }

                debug_info["domain_tools_status"][tool_name] = domain_info

            except Exception as e:
                debug_info["domain_tools_status"][tool_name] = {"error": str(e)}

        # Try direct contract search using global service
        if global_ibkr_service and global_ibkr_service.connected:
            try:
                from ib_async import Stock
                contract = Stock('AAPL', 'SMART', 'USD')
                qualified = await global_ibkr_service.ib.qualifyContractsAsync(contract)
                debug_info["direct_test"] = {
                    "success": True,
                    "qualified_contracts": len(qualified) if qualified else 0
                }
            except Exception as e:
                debug_info["direct_test"] = {
                    "success": False,
                    "error": str(e)
                }

        return debug_info

    except Exception as e:
        return {
            "status": "error",
            "message": f"Debug failed: {str(e)}"
        }

@mcp.tool()
async def direct_contract_search(symbol: str = "AAPL", sec_type: str = "STK", exchange: str = "SMART", currency: str = "USD") -> Dict:
    """
    BYPASS: Direct contract search bypassing domain layer

    Args:
        symbol: Symbol to search for
        sec_type: Security type
        exchange: Exchange
        currency: Currency

    Returns:
        Dict containing contract information
    """
    try:
        # Direct access to global IBKR service
        if not ibkr_service or not ibkr_service.connected:
            return {
                "status": "error",
                "message": "IBKR service not connected",
                "service_available": bool(ibkr_service),
                "service_connected": ibkr_service.connected if ibkr_service else False
            }

        # Create contract directly
        if sec_type == "STK":
            from ib_async import Stock
            contract = Stock(symbol, exchange, currency)
        elif sec_type == "CASH":
            from ib_async import Forex
            contract = Forex(symbol)
        else:
            return {"status": "error", "message": f"Unsupported security type: {sec_type}"}

        # Qualify contract
        qualified_contracts = await ibkr_service.ib.qualifyContractsAsync(contract)

        if not qualified_contracts:
            return {
                "status": "error",
                "message": f"No contracts found for {symbol}",
                "search_params": {"symbol": symbol, "sec_type": sec_type, "exchange": exchange, "currency": currency}
            }

        # Format results
        results = []
        for contract in qualified_contracts:
            results.append({
                "symbol": contract.symbol,
                "sec_type": contract.secType,
                "exchange": contract.exchange,
                "currency": contract.currency,
                "con_id": contract.conId,
                "local_symbol": getattr(contract, 'localSymbol', ''),
                "trading_class": getattr(contract, 'tradingClass', ''),
                "primary_exchange": getattr(contract, 'primaryExchange', '')
            })

        return {
            "status": "success",
            "contracts": results,
            "count": len(results),
            "search_params": {"symbol": symbol, "sec_type": sec_type, "exchange": exchange, "currency": currency}
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Direct contract search failed: {str(e)}",
            "error_type": type(e).__name__
        }

@mcp.tool()
async def direct_account_summary() -> Dict:
    """
    BYPASS: Direct account summary bypassing domain layer

    Returns:
        Dict containing account information
    """
    try:
        # Direct access to global IBKR service
        if not ibkr_service or not ibkr_service.connected:
            return {
                "status": "error",
                "message": "IBKR service not connected",
                "service_available": bool(ibkr_service),
                "service_connected": ibkr_service.connected if ibkr_service else False
            }

        # Get account summary
        account_summary = ibkr_service.ib.accountSummary()
        portfolio = ibkr_service.ib.portfolio()
        positions = ibkr_service.ib.positions()

        # Format account summary
        summary_dict = {}
        for item in account_summary:
            summary_dict[item.tag] = {
                "value": item.value,
                "currency": item.currency,
                "account": item.account
            }

        # Format portfolio
        portfolio_list = []
        for item in portfolio:
            portfolio_list.append({
                "symbol": item.contract.symbol,
                "sec_type": item.contract.secType,
                "position": item.position,
                "market_price": item.marketPrice,
                "market_value": item.marketValue,
                "average_cost": item.averageCost,
                "unrealized_pnl": item.unrealizedPNL,
                "realized_pnl": item.realizedPNL
            })

        return {
            "status": "success",
            "account_summary": summary_dict,
            "portfolio": portfolio_list,
            "positions_count": len(positions),
            "portfolio_count": len(portfolio_list)
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Direct account summary failed: {str(e)}",
            "error_type": type(e).__name__
        }

@mcp.tool()
async def test_direct_ibkr_access(symbol: str = "AAPL") -> Dict:
    """
    Test direct IBKR access bypassing domain layer

    Args:
        symbol: Symbol to test with

    Returns:
        Dict containing test results
    """
    try:
        # Test if we can access the global IBKR service
        if not ibkr_service:
            return {
                "status": "error",
                "message": "Global IBKR service not available",
                "service_available": False
            }

        if not ibkr_service.connected:
            return {
                "status": "error",
                "message": "IBKR service not connected",
                "service_available": True,
                "service_connected": False
            }

        # Test basic contract creation and qualification
        from ib_async import Stock
        contract = Stock(symbol, 'SMART', 'USD')

        # Test contract qualification
        qualified = await ibkr_service.ib.qualifyContractsAsync(contract)

        # Test account info
        accounts = ibkr_service.ib.managedAccounts()

        # Test basic market data request
        ticker = ibkr_service.ib.reqMktData(contract)
        import asyncio
        await asyncio.sleep(1)  # Wait for data

        return {
            "status": "success",
            "service_status": {
                "available": True,
                "connected": True,
                "accounts": accounts
            },
            "contract_test": {
                "symbol": symbol,
                "qualified": len(qualified) > 0,
                "contract_details": {
                    "symbol": qualified[0].symbol if qualified else None,
                    "exchange": qualified[0].exchange if qualified else None,
                    "con_id": qualified[0].conId if qualified else None
                } if qualified else None
            },
            "market_data_test": {
                "ticker_available": ticker is not None,
                "last_price": ticker.last if ticker and hasattr(ticker, 'last') else None,
                "bid": ticker.bid if ticker and hasattr(ticker, 'bid') else None,
                "ask": ticker.ask if ticker and hasattr(ticker, 'ask') else None
            }
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Direct IBKR access test failed: {str(e)}",
            "error_type": type(e).__name__
        }

@mcp.tool()
async def get_server_health() -> Dict:
    """
    Get overall server health status
    
    Returns:
        Dict containing server health information
    """
    try:
        health_info = {
            "mcp_server": "healthy",
            "ibkr_connection": "connected" if ibkr_service.connected else "disconnected",
            "services_initialized": bool(oms_instance),
            "domain_tools_loaded": {
                "historical_data": bool(historical_data_tools_v2),
                "tick_by_tick": bool(tickbytick_tools_v2),
                "market_depth": bool(marketdepth_tools_v2),
                "real_time_bars": bool(realtimebars_tools_v2),
                "news": bool(news_tools_v2),
                "scanner": bool(scanner_tools_v2),
                "options_trading": bool(optionstrading_tools_v2),
                "risk_management": bool(riskmanagement_tools_v2),
                "technical_analysis": bool(technicalanalysis_tools_v2),
                "volatility_surface": bool(volatility_surface_tools_v2),
                "algorithmic_trading": bool(algorithmictrading_tools_v2),
                "order_management": bool(ordermanagement_tools_v2),
                "account_management": bool(account_tools_v2),
                "contract_research": bool(contracts_tools_v2),
                "backtesting_infrastructure": bool(backtesting_tools_v2)
            },
            "uptime": "Available",  # Could track actual uptime
            "memory_usage": "Available"  # Could add memory monitoring
        }
        
        return {
            "status": "success",
            "health": health_info
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to get server health: {str(e)}"
        }

def setup_logging():
    """Set up logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

if __name__ == "__main__":
    setup_logging()
    logger.info("Starting IBKR MCP Server (Clean Architecture)")
    mcp.run()
