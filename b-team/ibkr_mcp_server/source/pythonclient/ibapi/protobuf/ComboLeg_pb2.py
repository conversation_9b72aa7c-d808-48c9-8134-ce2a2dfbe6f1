# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ComboLeg.proto
# Protobuf Python Version: 5.29.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    3,
    '',
    'ComboLeg.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0e\x43omboLeg.proto\x12\x08protobuf\"\xea\x02\n\x08\x43omboLeg\x12\x12\n\x05\x63onId\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x12\n\x05ratio\x18\x02 \x01(\x05H\x01\x88\x01\x01\x12\x13\n\x06\x61\x63tion\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x15\n\x08\x65xchange\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x16\n\topenClose\x18\x05 \x01(\x05H\x04\x88\x01\x01\x12\x1b\n\x0eshortSalesSlot\x18\x06 \x01(\x05H\x05\x88\x01\x01\x12\x1f\n\x12\x64\x65signatedLocation\x18\x07 \x01(\tH\x06\x88\x01\x01\x12\x17\n\nexemptCode\x18\x08 \x01(\x05H\x07\x88\x01\x01\x12\x18\n\x0bperLegPrice\x18\t \x01(\x01H\x08\x88\x01\x01\x42\x08\n\x06_conIdB\x08\n\x06_ratioB\t\n\x07_actionB\x0b\n\t_exchangeB\x0c\n\n_openCloseB\x11\n\x0f_shortSalesSlotB\x15\n\x13_designatedLocationB\r\n\x0b_exemptCodeB\x0e\n\x0c_perLegPriceB8\n\x16\x63om.ib.client.protobufB\rComboLegProto\xaa\x02\x0eIBApi.protobufb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ComboLeg_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.ib.client.protobufB\rComboLegProto\252\002\016IBApi.protobuf'
  _globals['_COMBOLEG']._serialized_start=29
  _globals['_COMBOLEG']._serialized_end=391
# @@protoc_insertion_point(module_scope)
