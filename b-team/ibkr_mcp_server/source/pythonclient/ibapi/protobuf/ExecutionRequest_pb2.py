# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ExecutionRequest.proto
# Protobuf Python Version: 5.29.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    3,
    '',
    'ExecutionRequest.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import ibapi.protobuf.ExecutionFilter_pb2 as ExecutionFilter__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16\x45xecutionRequest.proto\x12\x08protobuf\x1a\x15\x45xecutionFilter.proto\"}\n\x10\x45xecutionRequest\x12\x12\n\x05reqId\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x37\n\x0f\x65xecutionFilter\x18\x02 \x01(\x0b\x32\x19.protobuf.ExecutionFilterH\x01\x88\x01\x01\x42\x08\n\x06_reqIdB\x12\n\x10_executionFilterB@\n\x16\x63om.ib.client.protobufB\x15\x45xecutionRequestProto\xaa\x02\x0eIBApi.protobufb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ExecutionRequest_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.ib.client.protobufB\025ExecutionRequestProto\252\002\016IBApi.protobuf'
  _globals['_EXECUTIONREQUEST']._serialized_start=59
  _globals['_EXECUTIONREQUEST']._serialized_end=184
# @@protoc_insertion_point(module_scope)
