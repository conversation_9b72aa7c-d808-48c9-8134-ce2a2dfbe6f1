# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: Execution.proto
# Protobuf Python Version: 5.29.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    3,
    '',
    'Execution.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0f\x45xecution.proto\x12\x08protobuf\"\xaf\x06\n\tExecution\x12\x14\n\x07orderId\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x13\n\x06\x65xecId\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x11\n\x04time\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x17\n\nacctNumber\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x15\n\x08\x65xchange\x18\x05 \x01(\tH\x04\x88\x01\x01\x12\x11\n\x04side\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\x13\n\x06shares\x18\x07 \x01(\tH\x06\x88\x01\x01\x12\x12\n\x05price\x18\x08 \x01(\x01H\x07\x88\x01\x01\x12\x13\n\x06permId\x18\t \x01(\x03H\x08\x88\x01\x01\x12\x15\n\x08\x63lientId\x18\n \x01(\x05H\t\x88\x01\x01\x12\x1a\n\risLiquidation\x18\x0b \x01(\x08H\n\x88\x01\x01\x12\x13\n\x06\x63umQty\x18\x0c \x01(\tH\x0b\x88\x01\x01\x12\x15\n\x08\x61vgPrice\x18\r \x01(\x01H\x0c\x88\x01\x01\x12\x15\n\x08orderRef\x18\x0e \x01(\tH\r\x88\x01\x01\x12\x13\n\x06\x65vRule\x18\x0f \x01(\tH\x0e\x88\x01\x01\x12\x19\n\x0c\x65vMultiplier\x18\x10 \x01(\x01H\x0f\x88\x01\x01\x12\x16\n\tmodelCode\x18\x11 \x01(\tH\x10\x88\x01\x01\x12\x1a\n\rlastLiquidity\x18\x12 \x01(\x05H\x11\x88\x01\x01\x12#\n\x16isPriceRevisionPending\x18\x13 \x01(\x08H\x12\x88\x01\x01\x12\x16\n\tsubmitter\x18\x14 \x01(\tH\x13\x88\x01\x01\x12#\n\x16optExerciseOrLapseType\x18\x15 \x01(\x05H\x14\x88\x01\x01\x42\n\n\x08_orderIdB\t\n\x07_execIdB\x07\n\x05_timeB\r\n\x0b_acctNumberB\x0b\n\t_exchangeB\x07\n\x05_sideB\t\n\x07_sharesB\x08\n\x06_priceB\t\n\x07_permIdB\x0b\n\t_clientIdB\x10\n\x0e_isLiquidationB\t\n\x07_cumQtyB\x0b\n\t_avgPriceB\x0b\n\t_orderRefB\t\n\x07_evRuleB\x0f\n\r_evMultiplierB\x0c\n\n_modelCodeB\x10\n\x0e_lastLiquidityB\x19\n\x17_isPriceRevisionPendingB\x0c\n\n_submitterB\x19\n\x17_optExerciseOrLapseTypeB9\n\x16\x63om.ib.client.protobufB\x0e\x45xecutionProto\xaa\x02\x0eIBApi.protobufb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'Execution_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.ib.client.protobufB\016ExecutionProto\252\002\016IBApi.protobuf'
  _globals['_EXECUTION']._serialized_start=30
  _globals['_EXECUTION']._serialized_end=845
# @@protoc_insertion_point(module_scope)
