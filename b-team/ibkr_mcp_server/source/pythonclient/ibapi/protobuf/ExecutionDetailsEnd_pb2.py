# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ExecutionDetailsEnd.proto
# Protobuf Python Version: 5.29.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    3,
    '',
    'ExecutionDetailsEnd.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19\x45xecutionDetailsEnd.proto\x12\x08protobuf\"3\n\x13\x45xecutionDetailsEnd\x12\x12\n\x05reqId\x18\x01 \x01(\x05H\x00\x88\x01\x01\x42\x08\n\x06_reqIdBC\n\x16\x63om.ib.client.protobufB\x18\x45xecutionDetailsEndProto\xaa\x02\x0eIBApi.protobufb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ExecutionDetailsEnd_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.ib.client.protobufB\030ExecutionDetailsEndProto\252\002\016IBApi.protobuf'
  _globals['_EXECUTIONDETAILSEND']._serialized_start=39
  _globals['_EXECUTIONDETAILSEND']._serialized_end=90
# @@protoc_insertion_point(module_scope)
