"""
IBKR MCP Server Package

This package provides MCP (Model Context Protocol) server functionality
for Interactive Brokers TWS API integration.
"""

# Allow importing services directly without the full MCP server
__all__ = ['mcp']

def get_mcp_server():
    """Get the MCP server instance (lazy loading)"""
    try:
        from .app.ibkr_mcp_server import mcp
        return mcp
    except ImportError as e:
        # Provide a more helpful error message
        raise ImportError(
            f"Failed to import MCP server: {e}\n"
            "Make sure all dependencies are installed by running:\n"
            "pip install -r ibkr_mcp_server/requirements.txt"
        ) from e

# Only import MCP server if explicitly requested
try:
    # This allows importing the package without importing the full server
    import sys
    if 'ibkr_mcp_server.mcp' in sys.modules or __name__ == '__main__':
        mcp = get_mcp_server()
except ImportError:
    # If MCP server can't be imported, that's okay for service-only imports
    pass
