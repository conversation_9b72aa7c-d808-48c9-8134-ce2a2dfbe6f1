#!/usr/bin/env python3
"""
Validation script to test imports and basic type checking for the IBKR MCP server.
"""

import sys
import traceback
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

def test_import(module_name, description):
    """Test importing a module and report results."""
    try:
        __import__(module_name)
        print(f"✅ {description}: {module_name}")
        return True
    except Exception as e:
        print(f"❌ {description}: {module_name}")
        print(f"   Error: {e}")
        if "syntax" in str(e).lower() or "invalid" in str(e).lower():
            print("   Detailed traceback:")
            traceback.print_exc(limit=3)
        return False

def main():
    print("🔍 IBKR MCP Server Import Validation")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    # Test core modules
    modules_to_test = [
        ("services.ibkr_service", "Main IBKR Service"),
        ("services.order_management_service", "Order Management Service"),
        ("services.algo.order_routing_service", "Order Routing Service"),
        ("services.algo.algorithmic_trading_service", "Algorithmic Trading Service"),
        ("implementations.algo.order_routing_impl", "Order Routing Implementation"),
        ("implementations.algorithmictrading.algorithmictrading_impl", "Algorithmic Trading Implementation"),
        ("mcp.ibkr_mcp_server", "Main MCP Server"),
        ("mcp.tools.algorithmictrading_tools_v2", "Algorithmic Trading Tools"),
        ("mcp.domains.algorithmictrading_domain", "Algorithmic Trading Domain"),
        ("models.order_models", "Order Models"),
        ("models.options_models", "Options Models"),
        ("models.portfolio", "Portfolio Models"),
    ]
    
    for module_name, description in modules_to_test:
        total_count += 1
        if test_import(module_name, description):
            success_count += 1
        print()  # Empty line for readability
    
    print("=" * 50)
    print(f"📊 Results: {success_count}/{total_count} modules imported successfully")
    
    if success_count == total_count:
        print("🎉 All critical modules imported successfully!")
        print("✅ Type fixes appear to be working correctly.")
        return True
    else:
        print("⚠️  Some modules failed to import. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
