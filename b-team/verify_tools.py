#!/usr/bin/env python3
"""
Verification script for 133 tools in IBKR MCP Server
This script helps verify that all 133 tools are properly registered and available
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

def verify_tool_count():
    """Verify the tool count in the integrated server"""
    try:
        # Import the server module
        from integrated_mcp_server_133_tools import mcp
        
        # Count all registered tools
        tool_count = len(mcp._tools)
        
        print(f"📊 TOOL COUNT VERIFICATION")
        print(f"{'='*50}")
        print(f"🔧 Registered tools in MCP server: {tool_count}")
        
        # Expected categories and counts
        expected_categories = {
            "Connection & Status": 5,
            "Account Management": 10,
            "Market Data": 15,
            "Historical Data": 12,
            "Order Management": 20,
            "Portfolio Management": 10,
            "Options Trading": 15,
            "Technical Analysis": 8,
            "News & Research": 5,
            "Scanning & Screening": 8,
            "Supabase Integration": 10,
            "Futures Trading": 10,
            "Trading Guardrails": 10,
            "Investment Policies": 5
        }
        
        expected_total = sum(expected_categories.values())
        
        print(f"📈 Expected total: {expected_total}")
        print(f"✅ Actual total: {tool_count}")
        
        if tool_count == expected_total:
            print(f"🎉 SUCCESS: Tool count matches expected {expected_total} tools!")
        else:
            print(f"⚠️  WARNING: Tool count mismatch!")
            print(f"   Expected: {expected_total}")
            print(f"   Actual: {tool_count}")
            print(f"   Difference: {tool_count - expected_total}")
        
        print(f"\n📋 TOOL BREAKDOWN:")
        for category, count in expected_categories.items():
            print(f"   {category}: {count} tools")
        
        # List all registered tool names
        print(f"\n🔍 REGISTERED TOOLS:")
        for i, tool_name in enumerate(sorted(mcp._tools.keys()), 1):
            print(f"   {i:3d}. {tool_name}")
        
        return tool_count == expected_total
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def verify_specific_tools():
    """Verify that specific key tools are present"""
    try:
        from integrated_mcp_server_133_tools import mcp
        
        # Key tools that should be present for 133-tool version
        key_tools = [
            "test_connection",
            "get_tool_count",
            "get_integrated_tool_count", 
            "verify_new_server_133_tools",
            "get_supabase_status",
            "place_futures_order",
            "check_daily_loss_limit",
            "create_investment_objective"
        ]
        
        print(f"\n🔍 VERIFYING KEY TOOLS:")
        print(f"{'='*50}")
        
        all_present = True
        for tool in key_tools:
            if tool in mcp._tools:
                print(f"✅ {tool}")
            else:
                print(f"❌ {tool} - MISSING!")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ Error verifying specific tools: {e}")
        return False

if __name__ == "__main__":
    print("🚀 IBKR MCP Server 133-Tool Verification")
    print("="*60)
    
    # Verify total tool count
    count_ok = verify_tool_count()
    
    # Verify specific tools
    tools_ok = verify_specific_tools()
    
    print(f"\n📊 FINAL RESULT:")
    print(f"{'='*50}")
    
    if count_ok and tools_ok:
        print(f"🎉 ALL VERIFICATIONS PASSED!")
        print(f"   The integrated server has all 133 tools properly registered.")
        sys.exit(0)
    else:
        print(f"⚠️  VERIFICATION ISSUES FOUND!")
        if not count_ok:
            print(f"   - Tool count mismatch")
        if not tools_ok:
            print(f"   - Missing key tools")
        sys.exit(1)
