#!/bin/bash
# IBKR MCP Server Testing Helper Script

echo "=========================================="
echo "IBKR MCP Server Testing Helper"
echo "=========================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Base directory
BASE_DIR="/Users/<USER>/projects/b-team"
TESTS_DIR="$BASE_DIR/tests"

# Function to check if process is running
check_process() {
    if pgrep -f "$1" > /dev/null; then
        echo -e "${GREEN}✓${NC} $2 is running"
        return 0
    else
        echo -e "${RED}✗${NC} $2 is not running"
        return 1
    fi
}

# Check prerequisites
echo -e "\n${YELLOW}Checking prerequisites...${NC}"

# Check if TWS is running
check_process "Trader Workstation" "TWS"
TWS_RUNNING=$?

# Check if IB Gateway is running
check_process "IB Gateway" "IB Gateway"
GATEWAY_RUNNING=$?

if [ $TWS_RUNNING -ne 0 ] && [ $GATEWAY_RUNNING -ne 0 ]; then
    echo -e "\n${RED}Neither TWS nor IB Gateway is running!${NC}"
    echo "Please start TWS or IB Gateway before running tests."
    echo "Make sure to:"
    echo "  1. Enable API connections"
    echo "  2. Use port 7497 for paper trading"
    echo "  3. Check 'Download open orders on connection'"
    exit 1
fi

# Check if MCP server is running
check_process "ibkr_mcp_server.py" "MCP Server"
MCP_RUNNING=$?

if [ $MCP_RUNNING -ne 0 ]; then
    echo -e "\n${YELLOW}MCP Server is not running.${NC}"
    echo "Would you like to start it? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "Starting MCP Server..."
        cd "$BASE_DIR/ibkr_mcp_server"
        python app/ibkr_mcp_server.py &
        sleep 3
    fi
fi

# Menu
echo -e "\n${YELLOW}Select test to run:${NC}"
echo "1. Validate fixes are applied"
echo "2. Test basic MCP connectivity"
echo "3. Run comprehensive fix tests"
echo "4. Run all tests"
echo "5. View test documentation"
echo "6. Exit"

read -p "Enter choice (1-6): " choice

case $choice in
    1)
        echo -e "\n${YELLOW}Running fix validation...${NC}"
        cd "$TESTS_DIR"
        python validate_fixes.py
        ;;
    2)
        echo -e "\n${YELLOW}Running basic connectivity test...${NC}"
        cd "$TESTS_DIR"
        python test_mcp_basic.py
        ;;
    3)
        echo -e "\n${YELLOW}Running comprehensive tests...${NC}"
        cd "$TESTS_DIR"
        python test_ibkr_fixes.py
        ;;
    4)
        echo -e "\n${YELLOW}Running all tests...${NC}"
        cd "$TESTS_DIR"
        echo -e "\n--- Fix Validation ---"
        python validate_fixes.py
        echo -e "\n--- Basic Connectivity ---"
        python test_mcp_basic.py
        echo -e "\n--- Comprehensive Tests ---"
        python test_ibkr_fixes.py
        ;;
    5)
        echo -e "\n${YELLOW}Opening test documentation...${NC}"
        cat "$TESTS_DIR/README.md" | less
        ;;
    6)
        echo "Exiting..."
        exit 0
        ;;
    *)
        echo -e "${RED}Invalid choice${NC}"
        exit 1
        ;;
esac

echo -e "\n${GREEN}Test complete!${NC}"
echo "Check the output above for results."
echo "For troubleshooting, see: $TESTS_DIR/README.md"
