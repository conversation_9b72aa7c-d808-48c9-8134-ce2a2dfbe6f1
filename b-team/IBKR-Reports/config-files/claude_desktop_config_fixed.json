{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IBKR/b-team"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAjQpxPzjay5DrCA-uy3QkB27tTouN"}, "disabled": false, "autoApprove": []}, "dalle-mcp": {"command": "node", "args": ["/Users/<USER>/Documents/Cline/MCP/dalle-mcp-server/build/index.js"], "env": {"OPENAI_API_KEY": "********************************************************************************************************************************************************************", "SAVE_DIR": "/Users/<USER>/adverp/docs/dall-e"}, "disabled": false, "autoApprove": ["/Users/<USER>/adverp/docs/dall-e"]}, "fundraise-server": {"command": "node", "args": ["/Users/<USER>/Documents/Cline/MCP/fundraise-server/build/index.js"]}, "github.com/supabase-community/supabase-mcp": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": []}, "github-mcp-server": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}, "disabled": false, "autoApprove": []}, "ibkr-trading": {"command": "/Users/<USER>/IBKR/.venv/bin/python", "args": ["/Users/<USER>/IBKR/b-team/full_mcp_server.py"], "env": {"IBKR_AUTO_CONNECT": "false", "IBKR_DEFAULT_EXCHANGE": "SMART", "IBKR_DEFAULT_CURRENCY": "USD", "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"}, "disabled": false}}}