#!/usr/bin/env python3
"""
Comprehensive test script for IBKR MCP server fixes.
Tests order placement, real-time data, scanner, and risk management functionality.
"""

import asyncio
import json
import time
from datetime import datetime

# Test Configuration
TEST_SYMBOL = "AAPL"  # Apple stock for testing
TEST_ACCOUNT = "DU123456"  # Replace with your paper trading account
TEST_QUANTITY = 100

# Terminal color codes
GREEN = '\033[92m'
RED = '\033[91m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
RESET = '\033[0m'

def print_test_header(test_name):
    """Print a formatted test header"""
    print(f"\n{BLUE}{'='*60}{RESET}")
    print(f"{BLUE}Testing: {test_name}{RESET}")
    print(f"{BLUE}{'='*60}{RESET}")

def print_result(success, message):
    """Print colored test result"""
    if success:
        print(f"{GREEN}✅ SUCCESS: {message}{RESET}")
    else:
        print(f"{RED}❌ FAILURE: {message}{RESET}")

def print_info(message):
    """Print info message"""
    print(f"{YELLOW}ℹ️  INFO: {message}{RESET}")

async def test_connection():
    """Test 1: Connection to TWS"""
    print_test_header("1. Connection to TWS")
    
    # Using direct curl to MCP server
    import subprocess
    
    # First check connection status
    cmd = [
        'curl', '-s', '-X', 'POST',
        'http://localhost:8002/mcp/v1/resources',
        '-H', 'Content-Type: application/json',
        '-d', '{"method": "tools/call", "params": {"name": "connect_to_tws", "arguments": {}}}'
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        try:
            response = json.loads(result.stdout)
            if response.get('status') == 'success':
                print_result(True, "Connected to TWS successfully")
                return True
            else:
                print_result(False, f"Connection failed: {response.get('message', 'Unknown error')}")
                return False
        except json.JSONDecodeError:
            print_result(False, f"Invalid response: {result.stdout}")
            return False
    else:
        print_result(False, f"Command failed: {result.stderr}")
        return False

async def test_bracket_order():
    """Test 2: Bracket Order Placement (Critical Fix)"""
    print_test_header("2. Bracket Order Placement")
    
    import subprocess
    
    # Get current market price first
    print_info("Getting current market price...")
    
    # Place bracket order
    cmd = [
        'curl', '-s', '-X', 'POST',
        'http://localhost:8002/mcp/v1/resources',
        '-H', 'Content-Type: application/json',
        '-d', json.dumps({
            "method": "tools/call",
            "params": {
                "name": "place_bracket_order",
                "arguments": {
                    "symbol": TEST_SYMBOL,
                    "action": "BUY",
                    "quantity": TEST_QUANTITY,
                    "entry_price": 150.00,  # Example prices
                    "profit_price": 155.00,
                    "stop_price": 145.00,
                    "account": TEST_ACCOUNT
                }
            }
        })
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        try:
            response = json.loads(result.stdout)
            if response.get('status') == 'success':
                print_result(True, f"Bracket order placed successfully. Order ID: {response.get('orderId')}")
                print_info(f"Profit Order ID: {response.get('profitOrderId')}")
                print_info(f"Stop Order ID: {response.get('stopOrderId')}")
                
                # Check for workaround notice
                if 'workaround_applied' in response:
                    print_info(f"Workaround applied: {response['workaround_applied']}")
                
                return True
            else:
                print_result(False, f"Bracket order failed: {response.get('message', 'Unknown error')}")
                return False
        except json.JSONDecodeError:
            print_result(False, f"Invalid response: {result.stdout}")
            return False
    else:
        print_result(False, f"Command failed: {result.stderr}")
        return False

async def test_real_time_data():
    """Test 3: Real-time Data Subscription"""
    print_test_header("3. Real-time Data Subscription")
    
    import subprocess
    
    cmd = [
        'curl', '-s', '-X', 'POST',
        'http://localhost:8002/mcp/v1/resources',
        '-H', 'Content-Type: application/json',
        '-d', json.dumps({
            "method": "tools/call",
            "params": {
                "name": "start_real_time_bars",
                "arguments": {
                    "symbol": TEST_SYMBOL,
                    "bar_size": "5 secs",
                    "what_to_show": "TRADES",
                    "use_rth": True
                }
            }
        })
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        try:
            response = json.loads(result.stdout)
            if response.get('success'):
                print_result(True, f"Real-time bars subscription started. ID: {response.get('subscription_id')}")
                
                # Wait a few seconds for data
                print_info("Waiting 10 seconds for real-time data...")
                await asyncio.sleep(10)
                
                # Get recent bars
                cmd_recent = [
                    'curl', '-s', '-X', 'POST',
                    'http://localhost:8002/mcp/v1/resources',
                    '-H', 'Content-Type: application/json',
                    '-d', json.dumps({
                        "method": "tools/call",
                        "params": {
                            "name": "get_recent_bars",
                            "arguments": {
                                "symbol": TEST_SYMBOL,
                                "count": 5
                            }
                        }
                    })
                ]
                
                result_recent = subprocess.run(cmd_recent, capture_output=True, text=True)
                if result_recent.returncode == 0:
                    recent_response = json.loads(result_recent.stdout)
                    if recent_response.get('success') and recent_response.get('bars'):
                        print_info(f"Received {len(recent_response['bars'])} real-time bars")
                        latest_bar = recent_response['bars'][-1]
                        print_info(f"Latest bar: Open={latest_bar['open']}, High={latest_bar['high']}, Low={latest_bar['low']}, Close={latest_bar['close']}")
                
                # Cancel subscription
                print_info("Canceling real-time bars subscription...")
                
                return True
            else:
                print_result(False, f"Real-time bars subscription failed: {response.get('error', 'Unknown error')}")
                return False
        except json.JSONDecodeError:
            print_result(False, f"Invalid response: {result.stdout}")
            return False
    else:
        print_result(False, f"Command failed: {result.stderr}")
        return False

async def test_scanner():
    """Test 4: Market Scanner"""
    print_test_header("4. Market Scanner")
    
    import subprocess
    
    cmd = [
        'curl', '-s', '-X', 'POST',
        'http://localhost:8002/mcp/v1/resources',
        '-H', 'Content-Type: application/json',
        '-d', json.dumps({
            "method": "tools/call",
            "params": {
                "name": "run_market_scanner",
                "arguments": {
                    "scan_type": "TOP_PERC_GAIN",
                    "instrument": "STK",
                    "location": "STK.US.MAJOR",
                    "max_results": 10
                }
            }
        })
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        try:
            response = json.loads(result.stdout)
            if response.get('success'):
                print_result(True, f"Scanner returned {response.get('count', 0)} results")
                
                # Display top 3 results
                results = response.get('results', [])
                if results:
                    print_info("Top 3 gainers:")
                    for i, result in enumerate(results[:3]):
                        contract = result.get('contract', {})
                        print_info(f"  {i+1}. {contract.get('symbol', 'N/A')} - Rank: {result.get('rank', 'N/A')}")
                
                return True
            else:
                print_result(False, f"Scanner failed: {response.get('error', 'Unknown error')}")
                return False
        except json.JSONDecodeError:
            print_result(False, f"Invalid response: {result.stdout}")
            return False
    else:
        print_result(False, f"Command failed: {result.stderr}")
        return False

async def test_risk_calculations():
    """Test 5: Risk Management Calculations"""
    print_test_header("5. Risk Management - VaR Calculation")
    
    import subprocess
    
    cmd = [
        'curl', '-s', '-X', 'POST',
        'http://localhost:8002/mcp/v1/resources',
        '-H', 'Content-Type: application/json',
        '-d', json.dumps({
            "method": "tools/call",
            "params": {
                "name": "calculate_portfolio_var",
                "arguments": {
                    "account": TEST_ACCOUNT,
                    "confidence_level": 0.95,
                    "time_horizon": 1,
                    "method": "historical"
                }
            }
        })
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        try:
            response = json.loads(result.stdout)
            if response.get('status') == 'success':
                var_calc = response.get('var_calculation', {})
                print_result(True, f"VaR calculated successfully")
                print_info(f"Daily VaR (95%): {var_calc.get('var_percentage', 0):.2f}%")
                
                # Display risk assessment
                risk_assessment = response.get('risk_assessment', {})
                print_info(f"Overall Risk Level: {risk_assessment.get('overall_risk', 'N/A')}")
                
                return True
            else:
                print_result(False, f"VaR calculation failed: {response.get('message', 'Unknown error')}")
                return False
        except json.JSONDecodeError:
            print_result(False, f"Invalid response: {result.stdout}")
            return False
    else:
        print_result(False, f"Command failed: {result.stderr}")
        return False

async def test_contract_search():
    """Test 6: Multi-Asset Contract Search"""
    print_test_header("6. Multi-Asset Contract Search")
    
    # Test futures search
    print_info("Testing futures contract search...")
    import subprocess
    
    cmd = [
        'curl', '-s', '-X', 'POST',
        'http://localhost:8002/mcp/v1/resources',
        '-H', 'Content-Type: application/json',
        '-d', json.dumps({
            "method": "tools/call",
            "params": {
                "name": "search_futures",
                "arguments": {
                    "symbol": "ES",
                    "exchange": "CME",
                    "currency": "USD"
                }
            }
        })
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    futures_success = False
    
    if result.returncode == 0:
        try:
            response = json.loads(result.stdout)
            if response.get('status') == 'success':
                count = response.get('count', 0)
                print_result(True, f"Found {count} futures contracts")
                futures_success = True
            else:
                print_result(False, f"Futures search failed: {response.get('message', 'Unknown error')}")
        except json.JSONDecodeError:
            print_result(False, f"Invalid response: {result.stdout}")
    
    # Test forex search
    print_info("\nTesting forex contract search...")
    
    cmd_forex = [
        'curl', '-s', '-X', 'POST',
        'http://localhost:8002/mcp/v1/resources',
        '-H', 'Content-Type: application/json',
        '-d', json.dumps({
            "method": "tools/call",
            "params": {
                "name": "search_forex_pairs",
                "arguments": {
                    "base_currency": "EUR",
                    "quote_currency": "USD"
                }
            }
        })
    ]
    
    result_forex = subprocess.run(cmd_forex, capture_output=True, text=True)
    forex_success = False
    
    if result_forex.returncode == 0:
        try:
            response = json.loads(result_forex.stdout)
            if response.get('status') == 'success':
                count = response.get('count', 0)
                print_result(True, f"Found {count} forex pairs")
                forex_success = True
            else:
                print_result(False, f"Forex search failed: {response.get('message', 'Unknown error')}")
        except json.JSONDecodeError:
            print_result(False, f"Invalid response: {result_forex.stdout}")
    
    return futures_success and forex_success

async def main():
    """Run all tests"""
    print(f"{BLUE}{'='*60}{RESET}")
    print(f"{BLUE}IBKR MCP Server Fix Validation Test Suite{RESET}")
    print(f"{BLUE}{'='*60}{RESET}")
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Test Account: {TEST_ACCOUNT}")
    print(f"Test Symbol: {TEST_SYMBOL}")
    
    # Test results
    results = {
        "Connection": False,
        "Bracket Orders": False,
        "Real-time Data": False,
        "Scanner": False,
        "Risk Calculations": False,
        "Contract Search": False
    }
    
    # Run tests
    print_info("Starting test sequence...")
    
    # Test 1: Connection
    if await test_connection():
        results["Connection"] = True
        
        # Only run other tests if connected
        # Test 2: Bracket Orders (Critical)
        results["Bracket Orders"] = await test_bracket_order()
        
        # Test 3: Real-time Data
        results["Real-time Data"] = await test_real_time_data()
        
        # Test 4: Scanner
        results["Scanner"] = await test_scanner()
        
        # Test 5: Risk Calculations
        results["Risk Calculations"] = await test_risk_calculations()
        
        # Test 6: Contract Search
        results["Contract Search"] = await test_contract_search()
    else:
        print_info("Cannot run remaining tests without connection to TWS")
    
    # Summary
    print(f"\n{BLUE}{'='*60}{RESET}")
    print(f"{BLUE}Test Summary{RESET}")
    print(f"{BLUE}{'='*60}{RESET}")
    
    total_tests = len(results)
    passed_tests = sum(1 for v in results.values() if v)
    
    for test_name, passed in results.items():
        status = f"{GREEN}PASSED{RESET}" if passed else f"{RED}FAILED{RESET}"
        print(f"{test_name}: {status}")
    
    print(f"\n{BLUE}Total: {passed_tests}/{total_tests} tests passed{RESET}")
    
    if passed_tests == total_tests:
        print(f"{GREEN}🎉 ALL TESTS PASSED! The fixes are working correctly.{RESET}")
    elif passed_tests > 0:
        print(f"{YELLOW}⚠️  Some tests passed. Please review failed tests.{RESET}")
    else:
        print(f"{RED}❌ All tests failed. Please check TWS connection and server status.{RESET}")

if __name__ == "__main__":
    asyncio.run(main())
