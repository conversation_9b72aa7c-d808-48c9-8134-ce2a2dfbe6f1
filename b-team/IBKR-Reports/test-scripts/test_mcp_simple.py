#!/usr/bin/env python3
"""
Simple test script to verify MCP server can start
"""
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

def test_basic_imports():
    """Test basic imports"""
    print("Testing basic imports...")
    
    try:
        import mcp
        print("✅ MCP imported successfully")
    except ImportError as e:
        print(f"❌ MCP import failed: {e}")
        return False
    
    try:
        import matplotlib
        print("✅ matplotlib imported successfully")
    except ImportError as e:
        print(f"❌ matplotlib import failed: {e}")
        return False
    
    try:
        import ib_async
        print("✅ ib_async imported successfully")
    except ImportError as e:
        print(f"❌ ib_async import failed: {e}")
        return False
    
    return True

def test_mcp_server_import():
    """Test MCP server import"""
    print("\nTesting MCP server import...")
    
    try:
        from mcp.server.fastmcp import FastMCP
        print("✅ FastMCP imported successfully")
        
        # Create a simple MCP server
        mcp = FastMCP("Test IBKR Server")
        print("✅ FastMCP instance created successfully")
        return True
        
    except ImportError as e:
        print(f"❌ FastMCP import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ FastMCP creation failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing IBKR MCP Server Dependencies")
    print("=" * 50)
    
    # Test basic imports
    if not test_basic_imports():
        print("\n❌ Basic imports failed")
        return False
    
    # Test MCP server import
    if not test_mcp_server_import():
        print("\n❌ MCP server import failed")
        return False
    
    print("\n✅ All tests passed! MCP server should work.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
