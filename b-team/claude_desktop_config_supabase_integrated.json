{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IBKR/b-team"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAjQpxPzjay5DrCA-uy3QkB27tTouN"}, "disabled": false, "autoApprove": []}, "github.com/supabase-community/supabase-mcp": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": []}, "ibkr-trading": {"command": "/Users/<USER>/IBKR/.venv/bin/python", "args": ["/Users/<USER>/IBKR/b-team/integrated_mcp_server_133_tools.py"], "env": {"IBKR_AUTO_CONNECT": "false", "IBKR_DEFAULT_EXCHANGE": "SMART", "IBKR_DEFAULT_CURRENCY": "USD", "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"}, "disabled": false}}}