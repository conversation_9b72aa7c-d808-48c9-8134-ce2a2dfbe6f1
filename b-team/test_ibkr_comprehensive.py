#!/usr/bin/env python3
"""
Comprehensive IBKR TWS Testing Script - Testing Order Placement
This script will diagnose connection issues and test order placement functionality.
"""

import os
import sys
import asyncio
import socket
import logging
from datetime import datetime
from pathlib import Path

# Add project paths
project_path = Path(__file__).parent
sys.path.insert(0, str(project_path))
sys.path.insert(0, str(project_path / 'ibkr_mcp_server'))

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
env_path = project_path / 'ibkr_mcp_server' / '.env'
load_dotenv(env_path)

print(f"\n{'='*80}")
print("IBKR TWS DIAGNOSTIC AND TESTING SCRIPT")
print(f"{'='*80}")
print(f"Time: {datetime.now()}")
print(f"Python: {sys.version}")
print(f"\nEnvironment Variables:")
print(f"  IBKR_HOST: {os.getenv('IBKR_HOST', 'Not set')}")
print(f"  IBKR_PORT: {os.getenv('IBKR_PORT', 'Not set')}")
print(f"  IBKR_CLIENT_ID: {os.getenv('IBKR_CLIENT_ID', 'Not set')}")


def check_port_open(host: str, port: int) -> bool:
    """Check if a port is open and accepting connections"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(2)
    try:
        result = sock.connect_ex((host, port))
        return result == 0
    except Exception as e:
        logger.error(f"Port check error: {e}")
        return False
    finally:
        sock.close()


async def test_ib_async_connection():
    """Test basic ib_async connection"""
    print(f"\n{'='*80}")
    print("TEST 1: Basic ib_async Connection Test")
    print(f"{'='*80}")
    
    try:
        from ib_async import IB, util
        print("✅ ib_async imported successfully")
        print(f"ib_async version: {getattr(util, '__version__', 'Unknown')}")
        
        # Check if port is open
        host = os.getenv('IBKR_HOST', '127.0.0.1')
        port = int(os.getenv('IBKR_PORT', '7497'))
        
        print(f"\nChecking if TWS/Gateway is listening on {host}:{port}...")
        if check_port_open(host, port):
            print(f"✅ Port {port} is open and accepting connections")
        else:
            print(f"❌ Port {port} is not accessible")
            print("\nTroubleshooting steps:")
            print("1. Ensure TWS/IB Gateway is running")
            print("2. Check TWS Configuration:")
            print("   - File -> Global Configuration -> API -> Settings")
            print("   - Enable ActiveX and Socket Clients: ✓")
            print("   - Socket port: 7497 (paper) or 7496 (live)")
            print("   - Read-Only API: ✗ (unchecked for trading)")
            print("   - Master API client ID: (leave blank)")
            print("3. Check firewall settings")
            return False
        
        # Try to connect
        ib = IB()
        print(f"\nAttempting connection to {host}:{port}...")
        
        await ib.connectAsync(host, port, clientId=1, timeout=10)
        print("✅ Connection successful!")
        
        # Get some basic info
        print(f"Server version: {ib.client.serverVersion()}")
        accounts = ib.managedAccounts()
        print(f"Managed accounts: {accounts}")
        
        ib.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {type(e).__name__}: {str(e)}")
        return False


async def test_simple_bracket_order():
    """Test placing a simple bracket order"""
    print(f"\n{'='*80}")
    print("TEST 2: Simple Bracket Order Test")
    print(f"{'='*80}")
    
    try:
        from ib_async import IB, Stock, Order, util
        
        ib = IB()
        await ib.connectAsync(
            os.getenv('IBKR_HOST', '127.0.0.1'),
            int(os.getenv('IBKR_PORT', '7497')),
            clientId=2,
            readonly=False
        )
        print("✅ Connected to TWS")
        
        # Create contract
        contract = Stock('AAPL', 'SMART', 'USD')
        print(f"Contract: {contract}")
        
        # Get current price (for realistic order prices)
        ticker = ib.reqMktData(contract)
        await asyncio.sleep(2)  # Wait for data
        
        current_price = ticker.last or ticker.close or 180.0
        print(f"Current price: ${current_price}")
        
        # Create bracket order with prices around current
        entry_price = round(current_price - 1, 2)
        profit_price = round(current_price + 2, 2)
        stop_price = round(current_price - 3, 2)
        
        print(f"\nBracket Order:")
        print(f"  Entry: BUY 100 @ ${entry_price}")
        print(f"  Profit: SELL 100 @ ${profit_price}")
        print(f"  Stop: SELL 100 @ ${stop_price}")
        
        # Use ib_async's bracket order helper
        bracket = ib.bracketOrder(
            action='BUY',
            quantity=100,
            limitPrice=entry_price,
            takeProfitPrice=profit_price,
            stopLossPrice=stop_price
        )
        
        # Place the bracket orders
        trades = []
        for order in bracket:
            trade = ib.placeOrder(contract, order)
            trades.append(trade)
            print(f"✅ Placed order {trade.order.orderId}")
        
        # Wait for status
        await asyncio.sleep(3)
        
        # Check status
        for trade in trades:
            print(f"Order {trade.order.orderId}: {trade.orderStatus.status}")
        
        # Cancel orders
        for order in bracket:
            ib.cancelOrder(order)
            print(f"Cancelled order {order.orderId}")
        
        ib.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Bracket order test failed: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_ibkr_service_bracket():
    """Test bracket order through IBKRService"""
    print(f"\n{'='*80}")
    print("TEST 3: IBKRService Bracket Order Test")
    print(f"{'='*80}")
    
    try:
        from ibkr_mcp_server.app.services.ibkr_service import IBKRService
        
        service = IBKRService()
        await service.initialize()
        
        # Connect with trading enabled
        await service.connect(readonly=False)
        print("✅ Service connected")
        
        # Test the fixed bracket order implementation
        result = await service.place_bracket_order(
            symbol='AAPL',
            action='BUY',
            quantity=100,
            entry_price=180.00,
            profit_price=185.00,
            stop_price=175.00,
            account='DU1234567'  # This will be ignored due to the workaround
        )
        
        print(f"\n✅ Bracket order result:")
        print(f"  Status: {result.get('status')}")
        print(f"  Parent Order ID: {result.get('orderId')}")
        print(f"  Profit Order ID: {result.get('profitOrderId')}")  
        print(f"  Stop Order ID: {result.get('stopOrderId')}")
        print(f"  Message: {result.get('message')}")
        print(f"  Workaround: {result.get('workaround_applied')}")
        
        # Cancel the orders
        if result.get('status') == 'success':
            await asyncio.sleep(2)
            # Note: In production you'd cancel via order IDs
            print("\nOrders would be cancelled here in production")
        
        await service.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Service bracket order test failed: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_order_types():
    """Test various order types"""
    print(f"\n{'='*80}")
    print("TEST 4: Order Types Test")
    print(f"{'='*80}")
    
    try:
        from ib_async import IB, Stock, Order, LimitOrder, MarketOrder
        
        ib = IB()
        await ib.connectAsync(
            os.getenv('IBKR_HOST', '127.0.0.1'),
            int(os.getenv('IBKR_PORT', '7497')),
            clientId=3,
            readonly=False
        )
        print("✅ Connected to TWS")
        
        contract = Stock('AAPL', 'SMART', 'USD')
        
        # Test different order types
        test_orders = [
            ("Market Order", MarketOrder('BUY', 100)),
            ("Limit Order", LimitOrder('BUY', 100, 180.00)),
        ]
        
        for order_name, order in test_orders:
            print(f"\nTesting {order_name}...")
            try:
                trade = ib.placeOrder(contract, order)
                print(f"✅ {order_name} placed: Order ID {trade.order.orderId}")
                await asyncio.sleep(1)
                ib.cancelOrder(order)
                print(f"   Cancelled")
            except Exception as e:
                print(f"❌ {order_name} failed: {e}")
        
        ib.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Order types test failed: {type(e).__name__}: {str(e)}")
        return False


async def main():
    """Run all tests"""
    print("\nStarting IBKR TWS Diagnostic Tests...")
    
    # Run tests in sequence
    test_results = []
    
    # Test 1: Basic connection
    result = await test_ib_async_connection()
    test_results.append(("Basic Connection", result))
    
    if result:  # Only continue if connection works
        # Test 2: Simple bracket order
        result = await test_simple_bracket_order()
        test_results.append(("Simple Bracket Order", result))
        
        # Test 3: Service bracket order
        result = await test_ibkr_service_bracket()
        test_results.append(("Service Bracket Order", result))
        
        # Test 4: Order types
        result = await test_order_types()
        test_results.append(("Order Types", result))
    
    # Summary
    print(f"\n{'='*80}")
    print("TEST SUMMARY")
    print(f"{'='*80}")
    for test_name, passed in test_results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    passed_count = sum(1 for _, passed in test_results if passed)
    print(f"\nTotal: {passed_count}/{len(test_results)} tests passed")
    
    if passed_count < len(test_results):
        print("\n⚠️  Some tests failed. Please check:")
        print("1. TWS/IB Gateway is running")
        print("2. API settings are configured correctly")
        print("3. You're using the correct port (7497 for paper, 7496 for live)")
        print("4. Read-Only API is unchecked for order placement")


if __name__ == "__main__":
    import nest_asyncio
    nest_asyncio.apply()
    
    asyncio.run(main())
