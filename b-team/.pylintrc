[MASTER]
ignore=site-packages,typing.py,types.py,collections,asyncio,cpython,stdlib

[MESSAGES CONTROL]
disable=import-error,no-name-in-module,missing-docstring,invalid-name,too-few-public-methods,too-many-arguments,too-many-locals,too-many-branches,too-many-statements,line-too-long,unused-import,unused-variable,redefined-outer-name

[TYPECHECK]
ignored-modules=typing,types,collections,asyncio,site-packages
ignored-classes=typing,types,collections,asyncio

[IMPORTS]
known-third-party=ib_async,mcp
