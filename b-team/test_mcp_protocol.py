#!/usr/bin/env python3
"""
Test MCP Protocol Compliance
This script tests if our MCP server follows the protocol correctly
"""
import subprocess
import json
import sys
import time
from pathlib import Path

def test_mcp_server():
    """Test the MCP server protocol compliance"""
    print("🧪 Testing MCP Server Protocol Compliance")
    print("=" * 50)
    
    server_path = Path(__file__).parent / "simple_mcp_server.py"
    venv_python = "/Users/<USER>/IBKR/.venv/bin/python"
    
    print(f"📁 Server path: {server_path}")
    print(f"🐍 Python path: {venv_python}")
    
    # Start the server
    try:
        print("\n🚀 Starting MCP server...")
        process = subprocess.Popen(
            [venv_python, str(server_path)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # Give it a moment to start
        time.sleep(2)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ Server exited early with code: {process.returncode}")
            print(f"📤 Stdout: {stdout}")
            print(f"📤 Stderr: {stderr}")
            return False
        
        print("✅ Server started successfully")
        
        # Test MCP initialization
        print("\n📡 Testing MCP initialization...")
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # Send initialization message
        init_json = json.dumps(init_message) + "\n"
        process.stdin.write(init_json)
        process.stdin.flush()
        
        # Try to read response
        try:
            # Set a timeout for reading
            import select
            ready, _, _ = select.select([process.stdout], [], [], 5.0)
            
            if ready:
                response_line = process.stdout.readline()
                if response_line:
                    print(f"📥 Received response: {response_line.strip()}")
                    try:
                        response = json.loads(response_line)
                        print("✅ Valid JSON response received")
                        print(f"📋 Response: {json.dumps(response, indent=2)}")
                    except json.JSONDecodeError as e:
                        print(f"❌ Invalid JSON response: {e}")
                        print(f"📤 Raw response: {response_line}")
                        return False
                else:
                    print("❌ No response received")
                    return False
            else:
                print("❌ Timeout waiting for response")
                return False
                
        except Exception as e:
            print(f"❌ Error reading response: {e}")
            return False
        
        print("✅ MCP protocol test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing server: {e}")
        return False
    
    finally:
        # Clean up
        if 'process' in locals():
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                process.kill()
            print("🧹 Server process cleaned up")

def main():
    """Main test function"""
    success = test_mcp_server()
    
    if success:
        print("\n🎉 All tests passed! MCP server is protocol compliant.")
        print("\n📋 Next steps:")
        print("1. Restart Claude Desktop")
        print("2. The IBKR-trading server should now work")
        print("3. Try using the tools in Claude Desktop")
    else:
        print("\n💥 Tests failed! Check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
