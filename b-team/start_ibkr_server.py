#!/usr/bin/env python3
"""
Simple startup script for IBKR MCP Server
Handles import path issues and connects to paper trading port 7497
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Set up the environment for paper trading
os.environ['IBKR_HOST'] = '127.0.0.1'
os.environ['IBKR_PORT'] = '7497'  # Paper trading port
os.environ['IBKR_CLIENT_ID'] = '1'
os.environ['IBKR_READONLY'] = 'false'  # Enable trading

# Add the app directory to Python path
app_dir = Path(__file__).parent / "ibkr_mcp_server" / "app"
sys.path.insert(0, str(app_dir))

def setup_logging():
    """Set up logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def main():
    """Main entry point"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting IBKR MCP Server for Paper Trading")
    logger.info("📊 Configuration:")
    logger.info(f"   Host: {os.environ['IBKR_HOST']}")
    logger.info(f"   Port: {os.environ['IBKR_PORT']} (Paper Trading)")
    logger.info(f"   Client ID: {os.environ['IBKR_CLIENT_ID']}")
    logger.info(f"   Trading Mode: {'Enabled' if os.environ['IBKR_READONLY'] == 'false' else 'Read-Only'}")
    
    try:
        # Import and run the MCP server
        from ibkr_mcp_server import mcp
        logger.info("✅ MCP server imported successfully")
        
        # Run the FastMCP server
        logger.info("🔌 Starting MCP server...")
        mcp.run()
        
    except ImportError as e:
        logger.error(f"❌ Failed to import MCP server: {e}")
        logger.error("💡 Make sure you're running from the correct directory and dependencies are installed")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Failed to start MCP server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
