#!/usr/bin/env python3
"""
Test script for debugging TWS connection and testing bracket orders.
This script will help us identify and fix issues with order placement.
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path

# Add project path to Python path
project_path = Path(__file__).parent
sys.path.insert(0, str(project_path))

# Import necessary components
from ib_async import IB, Contract, Order, Stock, util
from ibkr_mcp_server.app.services.ibkr_service import IBKRService
import logging

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_direct_connection():
    """Test direct connection to TWS using ib_async"""
    print("\n" + "="*80)
    print("TEST 1: Direct TWS Connection Test")
    print("="*80)
    
    ib = IB()
    
    # Connection parameters from .env
    host = os.getenv("IBKR_HOST", "127.0.0.1")
    port = int(os.getenv("IBKR_PORT", "7497").split()[0])
    client_id = int(os.getenv("IBKR_CLIENT_ID", "1"))
    
    print(f"Attempting to connect to TWS at {host}:{port} with client ID {client_id}")
    print("Please ensure:")
    print("1. TWS is running")
    print("2. API connections are enabled in TWS Configuration")
    print("3. Port 7497 is set for paper trading API")
    print("4. 'Read-Only API' is unchecked if you want to place orders")
    
    try:
        await ib.connectAsync(host, port, clientId=client_id, timeout=10)
        print(f"✅ SUCCESS: Connected to TWS!")
        print(f"Server version: {ib.client.serverVersion()}")
        
        # Test basic functionality
        current_time = await ib.reqCurrentTimeAsync()
        print(f"Current TWS time: {current_time}")
        
        # Get account summary
        account_summary = await ib.accountSummaryAsync()
        print(f"\nAccount Summary:")
        for item in account_summary[:5]:  # Show first 5 items
            print(f"  {item.tag}: {item.value} {item.currency}")
        
        ib.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {type(e).__name__}: {str(e)}")
        print("\nTroubleshooting steps:")
        print("1. Check if TWS is running")
        print("2. Verify API settings in TWS -> File -> Global Configuration -> API -> Settings")
        print("3. Ensure 'Enable ActiveX and Socket Clients' is checked")
        print("4. Check that Socket port is 7497 for paper trading")
        print("5. Try restarting TWS")
        return False


async def test_simple_order():
    """Test placing a simple limit order"""
    print("\n" + "="*80)
    print("TEST 2: Simple Limit Order Test")
    print("="*80)
    
    ib = IB()
    
    try:
        # Connect to TWS
        await ib.connectAsync("127.0.0.1", 7497, clientId=2, readonly=False)
        print("✅ Connected to TWS")
        
        # Create a simple stock contract
        contract = Stock('AAPL', 'SMART', 'USD')
        print(f"Contract created: {contract}")
        
        # Create a simple limit order
        order = Order()
        order.action = 'BUY'
        order.totalQuantity = 100
        order.orderType = 'LMT'
        order.lmtPrice = 150.00
        
        print(f"Order created: BUY 100 AAPL @ $150.00 LMT")
        
        # Place the order
        trade = ib.placeOrder(contract, order)
        print(f"✅ Order placed successfully! Order ID: {trade.order.orderId}")
        
        # Wait a moment for status
        await asyncio.sleep(2)
        
        # Check order status
        print(f"Order status: {trade.orderStatus.status}")
        
        # Cancel the order
        ib.cancelOrder(order)
        print("Order cancelled")
        
        ib.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        if ib.isConnected():
            ib.disconnect()
        return False


async def test_bracket_order_direct():
    """Test placing a bracket order using ib_async directly"""
    print("\n" + "="*80)
    print("TEST 3: Direct Bracket Order Test (ib_async)")
    print("="*80)
    
    ib = IB()
    
    try:
        # Connect to TWS
        await ib.connectAsync("127.0.0.1", 7497, clientId=3, readonly=False)
        print("✅ Connected to TWS")
        
        # Create contract
        contract = Stock('AAPL', 'SMART', 'USD')
        
        # Use ib_async's bracketOrder helper
        bracket = ib.bracketOrder(
            action='BUY',
            quantity=100,
            limitPrice=180.00,
            takeProfitPrice=185.00,
            stopLossPrice=175.00
        )
        
        print(f"Bracket order created:")
        print(f"  Parent: BUY 100 @ $180.00")
        print(f"  Profit: SELL 100 @ $185.00")
        print(f"  Stop: SELL 100 @ $175.00")
        
        # Place all orders
        for o in bracket:
            trade = ib.placeOrder(contract, o)
            print(f"✅ Order placed: {trade.order.orderId}")
        
        # Wait for status updates
        await asyncio.sleep(3)
        
        # Cancel all orders
        for o in bracket:
            ib.cancelOrder(o)
        print("All orders cancelled")
        
        ib.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        if ib.isConnected():
            ib.disconnect()
        return False


async def test_service_bracket_order():
    """Test placing a bracket order through IBKRService"""
    print("\n" + "="*80)
    print("TEST 4: Bracket Order via IBKRService")
    print("="*80)
    
    service = IBKRService()
    
    try:
        # Initialize and connect
        await service.initialize()
        await service.connect(readonly=False)
        print("✅ Service connected to TWS")
        
        # Place bracket order
        result = await service.place_bracket_order(
            symbol='AAPL',
            action='BUY',
            quantity=100,
            entry_price=180.00,
            profit_price=185.00,
            stop_price=175.00,
            account='DU1234567'  # Replace with your paper account
        )
        
        print(f"✅ Bracket order result: {result}")
        
        await service.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests"""
    print("\nStarting IBKR Connection and Order Tests")
    print(f"Time: {datetime.now()}")
    print(f"Python: {sys.version}")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv(os.path.join(project_path, 'ibkr_mcp_server', '.env'))
    
    # Run tests
    tests = [
        ("Direct Connection", test_direct_connection),
        ("Simple Order", test_simple_order),
        ("Direct Bracket Order", test_bracket_order_direct),
        ("Service Bracket Order", test_service_bracket_order)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"\n❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, success in results if success)
    print(f"\nTotal: {passed}/{len(results)} tests passed")


if __name__ == "__main__":
    # Enable nest_asyncio for Jupyter/interactive environments
    import nest_asyncio
    nest_asyncio.apply()
    
    # Run the tests
    asyncio.run(main())
