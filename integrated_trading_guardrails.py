#!/usr/bin/env python3
"""
Integrated Trading Guardrails System
Advanced risk management integrated with IBKR MCP and Supabase MCP
"""
import asyncio
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

# Import IBKR MCP services
from ibkr_mcp_server.app.services.ibkr_service import ibkr_service
from ibkr_mcp_server.app.services.order_management_service import OrderManagementService

# Import Supabase integration
from supabase_config import SupabaseTradeLogger, RealtimeMonitor, supabase

class IntegratedTradingGuardrails:
    """Advanced trading guardrails with IBKR MCP and Supabase MCP integration"""
    
    def __init__(self, account_id: str):
        self.account_id = account_id
        self.ibkr_service = ibkr_service
        self.supabase_logger = None
        self.realtime_monitor = None
        
        # Risk management state
        self.investment_objectives = {}
        self.trading_policies = {}
        self.daily_stats = {
            'pnl': 0.0,
            'trades': 0,
            'wins': 0,
            'losses': 0,
            'consecutive_losses': 0,
            'max_drawdown': 0.0,
            'largest_loss': 0.0,
            'largest_win': 0.0
        }
        
        # Default risk limits
        self.default_limits = {
            'max_daily_loss': 500.0,
            'max_weekly_loss': 1500.0,
            'max_position_size': 10000.0,
            'max_positions': 10,
            'max_consecutive_losses': 3,
            'profit_target_daily': 200.0,
            'stop_loss_percentage': 0.02,  # 2%
            'trailing_stop_percentage': 0.015,  # 1.5%
            'allowed_instruments': ['MNQ', 'MES', 'AAPL', 'SPY', 'QQQ', 'TSLA']
        }
        
    async def initialize(self):
        """Initialize the integrated guardrails system"""
        print("🛡️ Initializing Integrated Trading Guardrails...")
        
        # Initialize Supabase integration
        self.supabase_logger = SupabaseTradeLogger()
        await self.supabase_logger.initialize(self.account_id)
        self.realtime_monitor = RealtimeMonitor(self.supabase_logger)
        print("✅ Supabase integration initialized")
        
        # Load existing policies and objectives
        await self.load_investment_objectives()
        await self.load_trading_policies()
        
        # Initialize daily stats from Supabase
        await self.load_daily_stats()
        
        print("✅ Integrated Trading Guardrails ready!")
        
    async def load_investment_objectives(self):
        """Load investment objectives from Supabase"""
        try:
            result = supabase.table("investment_objectives").select("*").order("created_at", desc=True).limit(1).execute()
            if result.data:
                self.investment_objectives = result.data[0]
                print("✅ Investment objectives loaded from Supabase")
            else:
                # Create default objectives
                await self.create_default_objectives()
        except Exception as e:
            print(f"⚠️ Could not load investment objectives: {e}")
            await self.create_default_objectives()
    
    async def load_trading_policies(self):
        """Load trading policies from Supabase"""
        try:
            result = supabase.table("trading_policies").select("*").eq("active", True).execute()
            for policy in result.data:
                self.trading_policies[policy['policy_type']] = policy
            print(f"✅ Loaded {len(self.trading_policies)} trading policies")
        except Exception as e:
            print(f"⚠️ Could not load trading policies: {e}")
    
    async def load_daily_stats(self):
        """Load today's trading statistics"""
        try:
            today = datetime.now().date().isoformat()
            
            # Get today's trades
            trades_result = supabase.table("trades").select("*").gte("created_at", today).execute()
            
            if trades_result.data:
                self.daily_stats['trades'] = len(trades_result.data)
                
                # Calculate P&L and win/loss stats
                for trade in trades_result.data:
                    if trade.get('status') == 'FILLED':
                        # This would need actual P&L calculation
                        # For now, we'll use a placeholder
                        pass
            
            print(f"✅ Daily stats loaded: {self.daily_stats['trades']} trades")
            
        except Exception as e:
            print(f"⚠️ Could not load daily stats: {e}")
    
    async def create_default_objectives(self):
        """Create default investment objectives"""
        default_objectives = {
            "objective_name": "Integrated Trading Goals 2025",
            "target_return": 25.0,  # 25% annual return
            "risk_tolerance": "moderate",
            "time_horizon": "medium",
            "capital_allocation": 25000.0,
            "constraints": self.default_limits,
            "created_by": "INTEGRATED_GUARDRAILS"
        }
        
        try:
            result = supabase.table("investment_objectives").insert(default_objectives).execute()
            self.investment_objectives = result.data[0]
            print("✅ Default investment objectives created")
        except Exception as e:
            print(f"❌ Could not create default objectives: {e}")
            self.investment_objectives = {"constraints": self.default_limits}
    
    async def check_pre_trade_guardrails(self, symbol: str, side: str, quantity: int, price: float) -> Dict[str, Any]:
        """Comprehensive pre-trade risk checks"""
        checks = {
            "allowed": True,
            "reasons": [],
            "warnings": [],
            "risk_score": 0,
            "position_size_adjusted": False,
            "recommended_quantity": quantity
        }
        
        constraints = self.investment_objectives.get('constraints', self.default_limits)
        
        # 1. Daily Loss Limit Check
        max_daily_loss = constraints.get('max_daily_loss', 500.0)
        if self.daily_stats['pnl'] <= -max_daily_loss:
            checks['allowed'] = False
            checks['reasons'].append(f"Daily loss limit reached: ${self.daily_stats['pnl']:.2f}")
            checks['risk_score'] += 100
        elif self.daily_stats['pnl'] <= -max_daily_loss * 0.8:
            checks['warnings'].append(f"Approaching daily loss limit: ${self.daily_stats['pnl']:.2f}")
            checks['risk_score'] += 50
        
        # 2. Consecutive Losses Check
        max_consecutive = constraints.get('max_consecutive_losses', 3)
        if self.daily_stats['consecutive_losses'] >= max_consecutive:
            checks['allowed'] = False
            checks['reasons'].append(f"Consecutive losses limit reached: {self.daily_stats['consecutive_losses']}")
            checks['risk_score'] += 80
        elif self.daily_stats['consecutive_losses'] >= max_consecutive - 1:
            checks['warnings'].append(f"High consecutive losses: {self.daily_stats['consecutive_losses']}")
            checks['risk_score'] += 40
        
        # 3. Position Size Check
        position_value = quantity * price
        max_position_size = constraints.get('max_position_size', 10000.0)
        
        if position_value > max_position_size:
            # Adjust quantity instead of blocking
            recommended_quantity = int(max_position_size / price)
            if recommended_quantity > 0:
                checks['warnings'].append(f"Position size adjusted from {quantity} to {recommended_quantity}")
                checks['recommended_quantity'] = recommended_quantity
                checks['position_size_adjusted'] = True
                checks['risk_score'] += 20
            else:
                checks['allowed'] = False
                checks['reasons'].append(f"Position size ${position_value:.2f} exceeds limit ${max_position_size}")
                checks['risk_score'] += 60
        
        # 4. Instrument Whitelist Check
        allowed_instruments = constraints.get('allowed_instruments', [])
        if allowed_instruments and symbol not in allowed_instruments:
            checks['allowed'] = False
            checks['reasons'].append(f"{symbol} not in allowed instruments: {allowed_instruments}")
            checks['risk_score'] += 70
        
        # 5. Account Balance Check (via IBKR MCP)
        try:
            account_summary = await self.ibkr_service.get_account_summary()
            if account_summary:
                buying_power = float(account_summary.get('BuyingPower', 0))
                if position_value > buying_power:
                    checks['allowed'] = False
                    checks['reasons'].append(f"Insufficient buying power: ${buying_power:.2f} < ${position_value:.2f}")
                    checks['risk_score'] += 90
        except Exception as e:
            checks['warnings'].append(f"Could not verify buying power: {e}")
            checks['risk_score'] += 10
        
        # 6. Market Conditions Check
        try:
            market_data = await self.ibkr_service.get_market_data(symbol=symbol)
            if market_data:
                # Check for wide spreads (high volatility indicator)
                bid = market_data.get('bid', 0)
                ask = market_data.get('ask', 0)
                if bid and ask:
                    spread_pct = (ask - bid) / ((ask + bid) / 2) * 100
                    if spread_pct > 2.0:  # 2% spread
                        checks['warnings'].append(f"Wide spread detected: {spread_pct:.2f}%")
                        checks['risk_score'] += 15
        except Exception as e:
            checks['warnings'].append(f"Could not check market conditions: {e}")
        
        # 7. Daily Profit Target Check (warning only)
        daily_target = constraints.get('profit_target_daily', 200.0)
        if self.daily_stats['pnl'] >= daily_target:
            checks['warnings'].append(f"Daily profit target reached: ${self.daily_stats['pnl']:.2f}")
            checks['risk_score'] += 25
        
        # Log the risk check
        await self.log_risk_check(symbol, side, quantity, price, checks)
        
        return checks
    
    async def calculate_optimal_position_size(self, symbol: str, account_value: float, volatility: float = 0.02) -> Dict[str, Any]:
        """Calculate optimal position size using multiple methods"""
        constraints = self.investment_objectives.get('constraints', self.default_limits)
        
        # Method 1: Fixed Fractional (2% risk per trade)
        risk_per_trade = 0.02
        fixed_fractional_value = account_value * risk_per_trade
        
        # Method 2: Kelly Criterion (simplified)
        win_rate = self.daily_stats['wins'] / max(self.daily_stats['trades'], 1) if self.daily_stats['trades'] > 0 else 0.6
        avg_win_loss_ratio = 1.5  # Assume 1.5:1 reward/risk
        
        kelly_fraction = (win_rate * avg_win_loss_ratio - (1 - win_rate)) / avg_win_loss_ratio
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
        kelly_value = account_value * kelly_fraction
        
        # Method 3: Volatility-adjusted
        volatility_adjusted_value = account_value * (0.02 / max(volatility, 0.01))
        
        # Use the most conservative approach
        recommended_value = min(
            fixed_fractional_value,
            kelly_value,
            volatility_adjusted_value,
            constraints.get('max_position_size', 10000.0)
        )
        
        return {
            "recommended_value": recommended_value,
            "methods": {
                "fixed_fractional": fixed_fractional_value,
                "kelly_criterion": kelly_value,
                "volatility_adjusted": volatility_adjusted_value
            },
            "win_rate": win_rate,
            "kelly_fraction": kelly_fraction
        }
    
    async def monitor_positions_for_exits(self) -> List[Dict[str, Any]]:
        """Monitor positions and generate exit signals"""
        exit_signals = []
        
        try:
            # Get current positions from IBKR
            positions = await self.ibkr_service.get_positions()
            constraints = self.investment_objectives.get('constraints', self.default_limits)
            
            for position in positions:
                if position.get('position', 0) == 0:
                    continue  # Skip empty positions
                
                symbol = position.get('symbol', '')
                current_pnl = position.get('unrealizedPNL', 0)
                position_value = abs(position.get('marketValue', 0))
                avg_cost = position.get('avgCost', 0)
                market_price = position.get('marketPrice', avg_cost)
                
                if avg_cost == 0:
                    continue
                
                pnl_percentage = (market_price - avg_cost) / avg_cost * 100
                
                signal = {
                    'symbol': symbol,
                    'action': None,
                    'reason': None,
                    'urgency': 'normal',
                    'current_pnl': current_pnl,
                    'pnl_percentage': pnl_percentage
                }
                
                # Stop Loss Check
                stop_loss_pct = constraints.get('stop_loss_percentage', 0.02) * 100
                if pnl_percentage <= -stop_loss_pct:
                    signal['action'] = 'CLOSE'
                    signal['reason'] = f"Stop loss triggered: {pnl_percentage:.1f}%"
                    signal['urgency'] = 'high'
                
                # Trailing Stop Check
                trailing_stop_pct = constraints.get('trailing_stop_percentage', 0.015) * 100
                max_pnl_pct = position.get('max_pnl_pct', pnl_percentage)  # Would need to track this
                
                if max_pnl_pct > 0 and (max_pnl_pct - pnl_percentage) > trailing_stop_pct:
                    signal['action'] = 'CLOSE'
                    signal['reason'] = f"Trailing stop triggered"
                    signal['urgency'] = 'high'
                
                # Daily Target Reached
                daily_target = constraints.get('profit_target_daily', 200.0)
                if self.daily_stats['pnl'] >= daily_target and current_pnl > 0:
                    signal['action'] = 'CLOSE'
                    signal['reason'] = f"Daily target reached: ${self.daily_stats['pnl']:.2f}"
                
                if signal['action']:
                    exit_signals.append(signal)
                    
                    # Log exit signal
                    await self.supabase_logger.log_system_event({
                        "event_type": "EXIT_SIGNAL",
                        "message": f"Exit signal for {symbol}: {signal['reason']}",
                        "details": signal
                    })
        
        except Exception as e:
            print(f"❌ Error monitoring positions: {e}")
        
        return exit_signals
    
    async def update_trade_result(self, trade_result: Dict[str, Any]):
        """Update statistics after trade completion"""
        pnl = trade_result.get('pnl', 0)
        
        # Update daily stats
        self.daily_stats['trades'] += 1
        self.daily_stats['pnl'] += pnl
        
        if pnl > 0:
            self.daily_stats['wins'] += 1
            self.daily_stats['consecutive_losses'] = 0
            self.daily_stats['largest_win'] = max(self.daily_stats['largest_win'], pnl)
        else:
            self.daily_stats['losses'] += 1
            self.daily_stats['consecutive_losses'] += 1
            self.daily_stats['largest_loss'] = min(self.daily_stats['largest_loss'], pnl)
        
        # Update max drawdown
        if self.daily_stats['pnl'] < 0:
            self.daily_stats['max_drawdown'] = min(self.daily_stats['max_drawdown'], self.daily_stats['pnl'])
        
        # Check stop conditions
        await self.check_stop_conditions()
        
        # Log performance update
        await self.supabase_logger.log_performance({
            "total_pnl": self.daily_stats['pnl'],
            "daily_trades": self.daily_stats['trades'],
            "win_rate": self.daily_stats['wins'] / max(self.daily_stats['trades'], 1),
            "consecutive_losses": self.daily_stats['consecutive_losses'],
            "max_drawdown": self.daily_stats['max_drawdown']
        })
    
    async def check_stop_conditions(self) -> bool:
        """Check if trading should be stopped"""
        constraints = self.investment_objectives.get('constraints', self.default_limits)
        stop_reasons = []
        
        # Daily loss limit
        max_daily_loss = constraints.get('max_daily_loss', 500.0)
        if self.daily_stats['pnl'] <= -max_daily_loss:
            stop_reasons.append(f"Daily loss limit reached: ${self.daily_stats['pnl']:.2f}")
        
        # Consecutive losses
        max_consecutive = constraints.get('max_consecutive_losses', 3)
        if self.daily_stats['consecutive_losses'] >= max_consecutive:
            stop_reasons.append(f"Consecutive losses limit: {self.daily_stats['consecutive_losses']}")
        
        if stop_reasons:
            await self.create_stop_alert(stop_reasons)
            return True
        
        return False
    
    async def create_stop_alert(self, reasons: List[str]):
        """Create critical alert when trading should stop"""
        alert_data = {
            "name": "Trading Stop Alert",
            "condition_type": "CRITICAL_STOP",
            "threshold_value": self.daily_stats['pnl'],
            "severity": "CRITICAL",
            "active": True,
            "message": f"Trading stopped: {', '.join(reasons)}"
        }
        
        try:
            await supabase.table("alerts").insert(alert_data).execute()
            
            # Also log as system event
            await self.supabase_logger.log_system_event({
                "event_type": "TRADING_STOPPED",
                "message": f"Trading halted: {', '.join(reasons)}",
                "details": {"reasons": reasons, "daily_stats": self.daily_stats}
            })
            
            print(f"🛑 TRADING STOPPED: {', '.join(reasons)}")
            
        except Exception as e:
            print(f"❌ Could not create stop alert: {e}")
    
    async def log_risk_check(self, symbol: str, side: str, quantity: int, price: float, checks: Dict[str, Any]):
        """Log risk check to audit trail"""
        audit_entry = {
            "event_type": "RISK_CHECK",
            "message": f"Risk check for {symbol} {side} {quantity}@${price:.2f}",
            "details": {
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "price": price,
                "allowed": checks['allowed'],
                "risk_score": checks['risk_score'],
                "reasons": checks.get('reasons', []),
                "warnings": checks.get('warnings', []),
                "daily_stats": self.daily_stats
            }
        }
        
        await self.supabase_logger.log_system_event(audit_entry)
    
    def get_dashboard_metrics(self) -> Dict[str, Any]:
        """Get comprehensive metrics for dashboard"""
        constraints = self.investment_objectives.get('constraints', self.default_limits)
        
        return {
            "daily_pnl": self.daily_stats['pnl'],
            "daily_trades": self.daily_stats['trades'],
            "win_rate": (self.daily_stats['wins'] / max(self.daily_stats['trades'], 1)) * 100,
            "consecutive_losses": self.daily_stats['consecutive_losses'],
            "max_drawdown": self.daily_stats['max_drawdown'],
            "largest_win": self.daily_stats['largest_win'],
            "largest_loss": self.daily_stats['largest_loss'],
            "daily_target": constraints.get('profit_target_daily', 200.0),
            "max_daily_loss": constraints.get('max_daily_loss', 500.0),
            "risk_status": self.get_risk_status(),
            "target_progress": (self.daily_stats['pnl'] / constraints.get('profit_target_daily', 200.0)) * 100,
            "loss_limit_usage": abs(self.daily_stats['pnl'] / constraints.get('max_daily_loss', 500.0)) * 100
        }
    
    def get_risk_status(self) -> str:
        """Get current risk status"""
        constraints = self.investment_objectives.get('constraints', self.default_limits)
        max_daily_loss = constraints.get('max_daily_loss', 500.0)
        
        if self.daily_stats['pnl'] <= -max_daily_loss * 0.9:
            return "CRITICAL"
        elif self.daily_stats['pnl'] <= -max_daily_loss * 0.7:
            return "HIGH"
        elif self.daily_stats['pnl'] <= -max_daily_loss * 0.5:
            return "MEDIUM"
        elif self.daily_stats['consecutive_losses'] >= 2:
            return "CAUTION"
        else:
            return "OK"

async def main():
    """Test the integrated guardrails system"""
    print("🛡️ INTEGRATED TRADING GUARDRAILS SYSTEM")
    print("=" * 60)
    
    guardrails = IntegratedTradingGuardrails("TEST_ACCOUNT")
    await guardrails.initialize()
    
    # Test pre-trade check
    print("\n🧪 Testing pre-trade checks...")
    checks = await guardrails.check_pre_trade_guardrails("AAPL", "BUY", 100, 150.0)
    print(f"Trade allowed: {checks['allowed']}")
    print(f"Risk score: {checks['risk_score']}")
    if checks['warnings']:
        print(f"Warnings: {checks['warnings']}")
    
    # Display dashboard metrics
    print("\n📊 Dashboard Metrics:")
    metrics = guardrails.get_dashboard_metrics()
    for key, value in metrics.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
