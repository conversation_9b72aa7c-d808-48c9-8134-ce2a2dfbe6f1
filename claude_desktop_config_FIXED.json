{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/IBKR/b-team"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAjQpxPzjay5DrCA-uy3QkB27tTouN"}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]}, "ibkr-133-tools": {"command": "/Users/<USER>/IBKR/.venv/bin/python", "args": ["/Users/<USER>/IBKR/b-team/final_ibkr_mcp_server.py"], "env": {"PYTHONPATH": "/Users/<USER>/IBKR/b-team", "IBKR_AUTO_CONNECT": "false", "IBKR_DEFAULT_EXCHANGE": "SMART", "IBKR_DEFAULT_CURRENCY": "USD", "IBKR_DEFAULT_TIME_IN_FORCE": "DAY"}}}}